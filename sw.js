/**
 * Simple Service Worker for Küp Cadısı
 * Basic caching and offline support
 */

const CACHE_NAME = 'kupcadisi-v1.0.0';

// Basic files to cache
const STATIC_FILES = [
    '/',
    '/static/assets/css/main.css',
    '/static/assets/vendor/bootstrap/css/bootstrap.min.css',
    '/static/assets/img/favicon/favicon.ico'
];

// Install event
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching files');
                return cache.addAll(STATIC_FILES);
            })
            .catch((error) => {
                console.log('Service Worker: Cache failed', error);
            })
    );
    
    self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    self.clients.claim();
});

// Fetch event - basic caching strategy
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip external requests
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then((response) => {
                // Return cached version if available
                if (response) {
                    return response;
                }
                
                // Otherwise fetch from network
                return fetch(event.request)
                    .then((response) => {
                        // Don't cache if not successful
                        if (!response || response.status !== 200) {
                            return response;
                        }
                        
                        // Clone and cache the response
                        const responseToCache = response.clone();
                        caches.open(CACHE_NAME)
                            .then((cache) => {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return response;
                    })
                    .catch(() => {
                        // Return offline message for HTML pages
                        if (event.request.headers.get('accept').includes('text/html')) {
                            return new Response('Offline - İnternet bağlantınızı kontrol edin', {
                                status: 200,
                                headers: { 'Content-Type': 'text/html; charset=utf-8' }
                            });
                        }
                    });
            })
    );
});

console.log('Service Worker: Loaded successfully');
