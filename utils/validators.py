"""
Dosya doğrulama ve güvenlik utilities
"""
import os
import mimetypes
from django.core.exceptions import ValidationError
from django.conf import settings
from PIL import Image
import logging

logger = logging.getLogger(__name__)

# Güvenli dosya uzantıları
SAFE_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
SAFE_MIME_TYPES = [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp'
]

def validate_file_extension(file):
    """
    Dosya uzantısını kontrol eder
    """
    ext = os.path.splitext(file.name)[1].lower()
    if ext not in SAFE_IMAGE_EXTENSIONS:
        raise ValidationError(
            f'Geçersiz dosya uzantısı: {ext}. '
            f'İzin verilen uzantılar: {", ".join(SAFE_IMAGE_EXTENSIONS)}'
        )

def validate_file_size(file, max_size_mb=5):
    """
    Dosya boyutunu kontrol eder
    """
    max_size = max_size_mb * 1024 * 1024  # MB to bytes
    if file.size > max_size:
        raise ValidationError(
            f'Dosya boyutu çok büyük. Maksimum {max_size_mb}MB olabilir. '
            f'Yüklenen dosya: {file.size / (1024*1024):.2f}MB'
        )

def validate_image_content(file):
    """
    Dosyanın gerçekten bir resim olduğunu kontrol eder
    """
    try:
        # PIL ile resmi açmaya çalış
        image = Image.open(file)
        image.verify()  # Resmin bozuk olmadığını kontrol et
        
        # Dosya pointer'ını başa al
        file.seek(0)
        
        # MIME type kontrolü
        mime_type, _ = mimetypes.guess_type(file.name)
        if mime_type not in SAFE_MIME_TYPES:
            raise ValidationError(f'Geçersiz MIME type: {mime_type}')
            
    except Exception as e:
        logger.warning(f'Image validation failed for {file.name}: {str(e)}')
        raise ValidationError('Geçersiz resim dosyası')

def validate_image_dimensions(file, max_width=4000, max_height=4000):
    """
    Resim boyutlarını kontrol eder
    """
    try:
        image = Image.open(file)
        width, height = image.size
        
        if width > max_width or height > max_height:
            raise ValidationError(
                f'Resim boyutları çok büyük. '
                f'Maksimum: {max_width}x{max_height}px, '
                f'Yüklenen: {width}x{height}px'
            )
        
        file.seek(0)
        
    except Exception as e:
        logger.warning(f'Image dimension validation failed for {file.name}: {str(e)}')
        raise ValidationError('Resim boyutları kontrol edilemedi')

def clean_filename(filename):
    """
    Dosya adını güvenli hale getirir
    """
    # Tehlikeli karakterleri kaldır
    import re
    filename = re.sub(r'[^\w\s-.]', '', filename)
    filename = re.sub(r'[-\s]+', '-', filename)
    return filename.strip('-')

def comprehensive_file_validation(file):
    """
    Kapsamlı dosya doğrulaması
    """
    # Dosya uzantısı kontrolü
    validate_file_extension(file)
    
    # Dosya boyutu kontrolü
    max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 5 * 1024 * 1024) // (1024 * 1024)
    validate_file_size(file, max_size)
    
    # Resim içeriği kontrolü
    validate_image_content(file)
    
    # Resim boyutları kontrolü
    validate_image_dimensions(file)
    
    logger.info(f'File validation passed for: {file.name}')
    return True
