"""
Güvenli dosya yönetimi utilities.
"""

import os
import shutil
import tempfile
import time
from pathlib import Path
from typing import Optional, Tuple, List
from django.conf import settings
from django.core.files.storage import default_storage
from utils.logging import get_logger

logger = get_logger(__name__)


class FileOperationError(Exception):
    """Dosya işlemi hatası için özel exception."""
    pass


class SafeFileManager:
    """
    Güvenli dosya işlemleri için yönetici sınıf.
    """
    
    def __init__(self, backup_enabled: bool = True):
        self.backup_enabled = backup_enabled
        self.backup_dir = Path(settings.MEDIA_ROOT) / 'backups'
        self.backup_dir.mkdir(exist_ok=True)
    
    def safe_delete_file(self, file_path: str, create_backup: bool = None) -> Tuple[bool, Optional[str]]:
        """
        Dosyayı güvenli bir şekilde siler.
        
        Args:
            file_path: <PERSON>line<PERSON>k dosyanın yolu
            create_backup: <PERSON><PERSON> oluşturulsun mu (None ise class default'u kullanılır)
        
        Returns:
            Tuple[bool, Optional[str]]: (başarılı_mı, hata_mesajı)
        """
        if not file_path:
            return False, "Dosya yolu boş"
        
        file_path = Path(file_path)
        
        # Dosya var mı kontrol et
        if not file_path.exists():
            logger.warning("Silinmeye çalışılan dosya bulunamadı", extra={
                'file_path': str(file_path),
                'operation': 'safe_delete'
            })
            return True, None  # Dosya zaten yok, başarılı sayılır
        
        # Dosya okunabilir mi kontrol et
        if not file_path.is_file():
            return False, f"Belirtilen yol bir dosya değil: {file_path}"
        
        # Dosya yazma izni var mı kontrol et
        if not os.access(file_path.parent, os.W_OK):
            return False, f"Dosya dizinine yazma izni yok: {file_path.parent}"
        
        backup_path = None
        
        try:
            # Yedek oluştur
            if create_backup if create_backup is not None else self.backup_enabled:
                backup_path = self._create_backup(file_path)
                if not backup_path:
                    logger.warning("Yedek oluşturulamadı, silme işlemi devam ediyor", extra={
                        'file_path': str(file_path)
                    })
            
            # Dosyayı sil
            file_path.unlink()
            
            logger.info("Dosya başarıyla silindi", extra={
                'file_path': str(file_path),
                'backup_path': str(backup_path) if backup_path else None,
                'operation': 'safe_delete'
            })
            
            return True, None
            
        except PermissionError as e:
            error_msg = f"Dosya silme izni yok: {e}"
            logger.error("Dosya silme izni hatası", extra={
                'file_path': str(file_path),
                'error': str(e),
                'operation': 'safe_delete'
            })
            return False, error_msg
            
        except OSError as e:
            error_msg = f"Dosya silme hatası: {e}"
            logger.error("Dosya silme sistem hatası", extra={
                'file_path': str(file_path),
                'error': str(e),
                'operation': 'safe_delete'
            })
            return False, error_msg
            
        except Exception as e:
            error_msg = f"Beklenmeyen hata: {e}"
            logger.error("Dosya silme beklenmeyen hatası", extra={
                'file_path': str(file_path),
                'error': str(e),
                'operation': 'safe_delete'
            })
            return False, error_msg
    
    def _create_backup(self, file_path: Path) -> Optional[Path]:
        """
        Dosyanın yedeğini oluşturur.
        
        Args:
            file_path: Yedeklenecek dosya
        
        Returns:
            Yedek dosya yolu veya None
        """
        try:
            # Yedek dosya adı oluştur
            timestamp = int(time.time())
            backup_filename = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = self.backup_dir / backup_filename
            
            # Yedek dizini oluştur
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Dosyayı kopyala
            shutil.copy2(file_path, backup_path)
            
            logger.info("Dosya yedeği oluşturuldu", extra={
                'original_path': str(file_path),
                'backup_path': str(backup_path),
                'operation': 'create_backup'
            })
            
            return backup_path
            
        except Exception as e:
            logger.error("Yedek oluşturma hatası", extra={
                'file_path': str(file_path),
                'error': str(e),
                'operation': 'create_backup'
            })
            return None
    
    def safe_move_file(self, source: str, destination: str) -> Tuple[bool, Optional[str]]:
        """
        Dosyayı güvenli bir şekilde taşır.
        
        Args:
            source: Kaynak dosya yolu
            destination: Hedef dosya yolu
        
        Returns:
            Tuple[bool, Optional[str]]: (başarılı_mı, hata_mesajı)
        """
        source_path = Path(source)
        dest_path = Path(destination)
        
        try:
            # Kaynak dosya var mı kontrol et
            if not source_path.exists():
                return False, f"Kaynak dosya bulunamadı: {source}"
            
            # Hedef dizin var mı kontrol et, yoksa oluştur
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Atomic move operation
            shutil.move(str(source_path), str(dest_path))
            
            logger.info("Dosya başarıyla taşındı", extra={
                'source': source,
                'destination': destination,
                'operation': 'safe_move'
            })
            
            return True, None
            
        except Exception as e:
            error_msg = f"Dosya taşıma hatası: {e}"
            logger.error("Dosya taşıma hatası", extra={
                'source': source,
                'destination': destination,
                'error': str(e),
                'operation': 'safe_move'
            })
            return False, error_msg
    
    def cleanup_old_backups(self, days_to_keep: int = 30) -> int:
        """
        Eski yedekleri temizler.
        
        Args:
            days_to_keep: Kaç günlük yedek tutulsun
        
        Returns:
            Silinen dosya sayısı
        """
        import time
        
        if not self.backup_dir.exists():
            return 0
        
        cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
        deleted_count = 0
        
        try:
            for backup_file in self.backup_dir.rglob('*'):
                if backup_file.is_file() and backup_file.stat().st_mtime < cutoff_time:
                    backup_file.unlink()
                    deleted_count += 1
            
            logger.info("Eski yedekler temizlendi", extra={
                'deleted_count': deleted_count,
                'days_to_keep': days_to_keep,
                'operation': 'cleanup_backups'
            })
            
        except Exception as e:
            logger.error("Yedek temizleme hatası", extra={
                'error': str(e),
                'operation': 'cleanup_backups'
            })
        
        return deleted_count


# Global instance
file_manager = SafeFileManager()


def safe_delete_file(file_path: str, create_backup: bool = True) -> Tuple[bool, Optional[str]]:
    """
    Dosyayı güvenli bir şekilde siler (convenience function).
    
    Args:
        file_path: Silinecek dosya yolu
        create_backup: Yedek oluşturulsun mu
    
    Returns:
        Tuple[bool, Optional[str]]: (başarılı_mı, hata_mesajı)
    """
    return file_manager.safe_delete_file(file_path, create_backup)


def safe_move_file(source: str, destination: str) -> Tuple[bool, Optional[str]]:
    """
    Dosyayı güvenli bir şekilde taşır (convenience function).
    
    Args:
        source: Kaynak dosya yolu
        destination: Hedef dosya yolu
    
    Returns:
        Tuple[bool, Optional[str]]: (başarılı_mı, hata_mesajı)
    """
    return file_manager.safe_move_file(source, destination)
