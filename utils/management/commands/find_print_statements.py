"""
<PERSON><PERSON><PERSON> kalan print ifadelerini bulan management command.
"""

import os
import re
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Projede kalan print ifadelerini bulur ve raporlar'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Print ifadelerini logging ile değiştirmeye yönelik öneriler gösterir',
        )
        parser.add_argument(
            '--exclude-dirs',
            nargs='+',
            default=['venv', 'env', '__pycache__', '.git', 'node_modules', 'static', 'media'],
            help='Hariç tutulacak dizinler',
        )

    def handle(self, *args, **options):
        fix_mode = options['fix']
        exclude_dirs = options['exclude_dirs']
        
        self.stdout.write(
            self.style.SUCCESS('Print ifadeleri taranıyor...')
        )
        
        # Proje kök dizini
        project_root = settings.BASE_DIR
        
        print_statements = []
        
        # Python dosyalarını tara
        for root, dirs, files in os.walk(project_root):
            # Hariç tutulacak dizinleri atla
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, project_root)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            
                        for line_num, line in enumerate(lines, 1):
                            # Print ifadelerini bul
                            if self._contains_print_statement(line):
                                print_statements.append({
                                    'file': relative_path,
                                    'line': line_num,
                                    'content': line.strip(),
                                    'type': self._get_print_type(line)
                                })
                                
                    except (UnicodeDecodeError, PermissionError):
                        continue
        
        # Sonuçları göster
        if not print_statements:
            self.stdout.write(
                self.style.SUCCESS('✅ Hiç print ifadesi bulunamadı!')
            )
            return
        
        self.stdout.write(
            self.style.WARNING(f'⚠️  {len(print_statements)} print ifadesi bulundu:')
        )
        
        # Dosyalara göre grupla
        files_with_prints = {}
        for stmt in print_statements:
            file_path = stmt['file']
            if file_path not in files_with_prints:
                files_with_prints[file_path] = []
            files_with_prints[file_path].append(stmt)
        
        # Raporla
        for file_path, statements in files_with_prints.items():
            self.stdout.write(f'\n📁 {file_path}:')
            
            for stmt in statements:
                line_info = f"  Satır {stmt['line']}: {stmt['content']}"
                
                if stmt['type'] == 'debug':
                    self.stdout.write(self.style.WARNING(f"  🐛 {line_info}"))
                elif stmt['type'] == 'error':
                    self.stdout.write(self.style.ERROR(f"  ❌ {line_info}"))
                else:
                    self.stdout.write(f"  📝 {line_info}")
                
                if fix_mode:
                    suggestion = self._get_logging_suggestion(stmt)
                    if suggestion:
                        self.stdout.write(f"     💡 Öneri: {suggestion}")
        
        # Özet
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('ÖZET'))
        self.stdout.write('='*50)
        
        debug_count = len([s for s in print_statements if s['type'] == 'debug'])
        error_count = len([s for s in print_statements if s['type'] == 'error'])
        other_count = len(print_statements) - debug_count - error_count
        
        self.stdout.write(f'Toplam print ifadesi: {len(print_statements)}')
        self.stdout.write(f'Debug amaçlı: {debug_count}')
        self.stdout.write(f'Hata mesajları: {error_count}')
        self.stdout.write(f'Diğer: {other_count}')
        
        if fix_mode:
            self.stdout.write('\n💡 Öneriler:')
            self.stdout.write('1. Debug print\'leri: logger.debug() kullanın')
            self.stdout.write('2. Hata print\'leri: logger.error() kullanın')
            self.stdout.write('3. Bilgi print\'leri: logger.info() kullanın')
            self.stdout.write('4. Logging import\'u: from utils.logging import get_logger')
        
        self.stdout.write(f'\n🔧 Düzeltme önerileri için: --fix parametresini kullanın')

    def _contains_print_statement(self, line):
        """Satırda print ifadesi var mı kontrol eder."""
        # Yorum satırlarını atla
        stripped = line.strip()
        if stripped.startswith('#'):
            return False
        
        # Print pattern'leri
        patterns = [
            r'\bprint\s*\(',  # print(...)
            r'\bprint\s+',    # print ...
        ]
        
        for pattern in patterns:
            if re.search(pattern, line):
                return True
        
        return False

    def _get_print_type(self, line):
        """Print ifadesinin tipini belirler."""
        line_lower = line.lower()
        
        # Debug amaçlı print'ler
        debug_keywords = ['debug', 'test', '===', '---', 'info', 'log']
        if any(keyword in line_lower for keyword in debug_keywords):
            return 'debug'
        
        # Hata mesajları
        error_keywords = ['error', 'hata', 'exception', 'fail', 'warning']
        if any(keyword in line_lower for keyword in error_keywords):
            return 'error'
        
        return 'other'

    def _get_logging_suggestion(self, stmt):
        """Print ifadesi için logging önerisi verir."""
        if stmt['type'] == 'debug':
            return "logger.debug('mesaj', extra={'key': 'value'})"
        elif stmt['type'] == 'error':
            return "logger.error('hata mesajı', extra={'error': str(e)})"
        else:
            return "logger.info('bilgi mesajı')"
