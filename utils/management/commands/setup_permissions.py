"""
<PERSON>zin Gruplarını Kurulum Komutu

Bu komut, tüm izin gruplarını oluşturur ve gerekli izinleri atar.
Yöneticiler için kullanıcı dostu çıktı sağlar.

Kullanım:
    python manage.py setup_permissions
    python manage.py setup_permissions --verbose  # Detaylı çıktı için
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group, Permission, User
from django.db import transaction
from utils.permissions import setup_permission_groups, PERMISSION_GROUPS
from utils.logging import get_logger

logger = get_logger(__name__)

class Command(BaseCommand):
    help = '''
    İzin gruplarını kurar ve gerekli izinleri atar.
    
    Bu komut şu işlemleri yapar:
    1. Önceden tanımlanmış izin gruplarını oluşturur
    2. Her gruba uygun izinleri atar
    3. Mevcut grupları günceller
    4. Detaylı rapor sunar
    
    Güvenli bir işlemdir - mevcut kullanıcı atamalarını bozmaz.
    '''
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Detaylı çıktı gösterir (her izin için ayrı satır)',
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Sadece ne yapılacağını gösterir, gerçek değişiklik yapmaz',
        )
        
        parser.add_argument(
            '--list-groups',
            action='store_true',
            help='Mevcut grupları ve izinlerini listeler',
        )

    def handle(self, *args, **options):
        """Ana komut işleyicisi"""
        
        # Sadece grup listesi isteniyorsa
        if options['list_groups']:
            self.list_existing_groups()
            return
        
        self.stdout.write(
            self.style.SUCCESS('🔐 Küp Cadısı - İzin Grupları Kurulum Sistemi')
        )
        self.stdout.write('=' * 60)
        
        # Dry run kontrolü
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('⚠️  DRY RUN MODU - Gerçek değişiklik yapılmayacak')
            )
        
        try:
            with transaction.atomic():
                # İzin gruplarını kur
                if options['dry_run']:
                    self.show_planned_changes()
                else:
                    result = setup_permission_groups()
                    self.show_results(result, options['verbose'])
                    
                    # Özet bilgi
                    self.show_summary()
                    
        except Exception as e:
            logger.error(f"İzin kurulumu sırasında hata: {e}")
            raise CommandError(f'İzin kurulumu başarısız: {e}')

    def show_planned_changes(self):
        """Dry run modunda yapılacak değişiklikleri gösterir"""
        self.stdout.write('\n📋 Yapılacak İşlemler:')
        self.stdout.write('-' * 40)
        
        for group_key, group_info in PERMISSION_GROUPS.items():
            # Grup mevcut mu kontrol et
            try:
                existing_group = Group.objects.get(name=group_info['name'])
                status = '🔄 Güncellenecek'
            except Group.DoesNotExist:
                status = '✨ Oluşturulacak'
            
            self.stdout.write(f"{status} {group_info['icon']} {group_info['name']}")
            self.stdout.write(f"   📝 {group_info['description']}")
            self.stdout.write(f"   🔑 {len(group_info['permissions'])} izin atanacak")
            self.stdout.write('')

    def show_results(self, result, verbose=False):
        """Kurulum sonuçlarını gösterir"""
        self.stdout.write('\n✅ Kurulum Tamamlandı!')
        self.stdout.write('-' * 40)
        
        # Oluşturulan gruplar
        if result['created']:
            self.stdout.write(
                self.style.SUCCESS(f'✨ Oluşturulan Gruplar ({len(result["created"])}):')
            )
            for group_name in result['created']:
                group_info = self.get_group_info(group_name)
                self.stdout.write(f'   {group_info["icon"]} {group_name}')
                if verbose:
                    self.stdout.write(f'      📝 {group_info["description"]}')
        
        # Güncellenen gruplar
        if result['updated']:
            self.stdout.write(
                self.style.WARNING(f'🔄 Güncellenen Gruplar ({len(result["updated"])}):')
            )
            for group_name in result['updated']:
                group_info = self.get_group_info(group_name)
                self.stdout.write(f'   {group_info["icon"]} {group_name}')
                if verbose:
                    self.stdout.write(f'      📝 {group_info["description"]}')
        
        # Detaylı izin listesi
        if verbose:
            self.stdout.write('\n🔑 Detaylı İzin Listesi:')
            self.stdout.write('-' * 40)
            for group_key, group_info in PERMISSION_GROUPS.items():
                self.stdout.write(f'{group_info["icon"]} {group_info["name"]}:')
                for permission in group_info['permissions']:
                    self.stdout.write(f'   • {permission}')
                self.stdout.write('')

    def show_summary(self):
        """Genel özet bilgilerini gösterir"""
        self.stdout.write('\n📊 Sistem Özeti:')
        self.stdout.write('-' * 40)
        
        # Toplam grup sayısı
        total_groups = Group.objects.count()
        permission_groups_count = len(PERMISSION_GROUPS)
        
        self.stdout.write(f'📁 Toplam Grup Sayısı: {total_groups}')
        self.stdout.write(f'🔐 İzin Sistemi Grupları: {permission_groups_count}')
        
        # Toplam kullanıcı sayısı
        total_users = User.objects.count()
        staff_users = User.objects.filter(is_staff=True).count()
        super_users = User.objects.filter(is_superuser=True).count()
        
        self.stdout.write(f'👥 Toplam Kullanıcı: {total_users}')
        self.stdout.write(f'👨‍💼 Yönetici Kullanıcı: {staff_users}')
        self.stdout.write(f'🔑 Süper Kullanıcı: {super_users}')
        
        # Kullanım talimatları
        self.stdout.write('\n💡 Sonraki Adımlar:')
        self.stdout.write('-' * 40)
        self.stdout.write('1. Django admin paneline gidin (/admin/)')
        self.stdout.write('2. "Gruplar" bölümünden kullanıcıları gruplara atayın')
        self.stdout.write('3. Kullanıcı izinlerini test edin')
        self.stdout.write('4. Gerekirse özel izinler ekleyin')
        
        self.stdout.write(
            self.style.SUCCESS('\n🎉 İzin sistemi başarıyla kuruldu!')
        )

    def list_existing_groups(self):
        """Mevcut grupları listeler"""
        self.stdout.write(
            self.style.SUCCESS('📋 Mevcut İzin Grupları')
        )
        self.stdout.write('=' * 50)
        
        groups = Group.objects.all().order_by('name')
        
        if not groups:
            self.stdout.write(
                self.style.WARNING('❌ Hiç grup bulunamadı.')
            )
            return
        
        for group in groups:
            # Bu grup bizim sistem gruplarımızdan mı?
            group_info = self.get_group_info(group.name)
            
            if group_info:
                # Sistem grubu
                self.stdout.write(
                    f'{group_info["icon"]} {group.name} '
                    f'({group.permissions.count()} izin)'
                )
                self.stdout.write(f'   📝 {group_info["description"]}')
                self.stdout.write(f'   👥 {group.user_set.count()} kullanıcı')
            else:
                # Özel grup
                self.stdout.write(
                    f'👥 {group.name} ({group.permissions.count()} izin) '
                    f'[Özel Grup]'
                )
                self.stdout.write(f'   👥 {group.user_set.count()} kullanıcı')
            
            self.stdout.write('')

    def get_group_info(self, group_name):
        """Grup adından grup bilgisini döndürür"""
        for group_key, group_info in PERMISSION_GROUPS.items():
            if group_info['name'] == group_name:
                return group_info
        return None
