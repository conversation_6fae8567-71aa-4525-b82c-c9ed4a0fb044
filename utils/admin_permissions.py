"""
Admin Panel İzin Yönetimi

Bu modül admin panelinde izin yönetimini kolaylaştırır.
Yöneticiler için görsel ve kullanıcı dostu arayüz <PERSON>ğ<PERSON>.
"""

from django.contrib import admin
from django.contrib.auth.models import Group, User
from django.contrib.auth.admin import GroupAdmin, UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from utils.permissions import PERMISSION_GROUPS, get_user_permission_summary

class CustomGroupAdmin(GroupAdmin):
    """
    Gelişmiş Grup Yönetimi
    
    Yöneticiler için grup bilgilerini daha anlaşılır şekilde sunar.
    """
    
    list_display = ('group_icon', 'name', 'description_info', 'permission_count', 'user_count', 'group_type')
    list_filter = ('name',)
    search_fields = ('name',)
    
    def group_icon(self, obj):
        """Grup ikonu gösterir"""
        group_info = self.get_group_info(obj.name)
        if group_info:
            return format_html(
                '<span style="font-size: 20px;">{}</span>',
                group_info['icon']
            )
        return '👥'
    group_icon.short_description = ""
    group_icon.admin_order_field = 'name'
    
    def description_info(self, obj):
        """Grup açıklaması gösterir"""
        group_info = self.get_group_info(obj.name)
        if group_info:
            return format_html(
                '<div style="max-width: 300px;">'
                '<strong style="color: {};">{}</strong><br>'
                '<small style="color: #666;">{}</small>'
                '</div>',
                group_info['color'],
                obj.name,
                group_info['description']
            )
        return format_html(
            '<strong>{}</strong><br><small style="color: #666;">Özel grup</small>',
            obj.name
        )
    description_info.short_description = "Grup Bilgisi"
    
    def permission_count(self, obj):
        """İzin sayısını gösterir"""
        count = obj.permissions.count()
        if count > 0:
            return format_html(
                '<span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">'
                '{} izin'
                '</span>',
                count
            )
        return format_html(
            '<span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">'
            'İzin yok'
            '</span>'
        )
    permission_count.short_description = "İzin Sayısı"
    
    def user_count(self, obj):
        """Kullanıcı sayısını gösterir"""
        count = obj.user_set.count()
        if count > 0:
            return format_html(
                '<span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">'
                '{} kullanıcı'
                '</span>',
                count
            )
        return format_html(
            '<span style="color: #666; font-size: 12px;">Kullanıcı yok</span>'
        )
    user_count.short_description = "Kullanıcı Sayısı"
    
    def group_type(self, obj):
        """Grup türünü gösterir"""
        group_info = self.get_group_info(obj.name)
        if group_info:
            return format_html(
                '<span style="background: {}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">'
                'Sistem Grubu'
                '</span>',
                group_info['color']
            )
        return format_html(
            '<span style="background: #6c757d; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">'
            'Özel Grup'
            '</span>'
        )
    group_type.short_description = "Grup Türü"
    
    def get_group_info(self, group_name):
        """Grup adından grup bilgisini döndürür"""
        for group_key, group_info in PERMISSION_GROUPS.items():
            if group_info['name'] == group_name:
                return group_info
        return None

class CustomUserAdmin(UserAdmin):
    """
    Gelişmiş Kullanıcı Yönetimi
    
    Kullanıcı izinlerini daha anlaşılır şekilde gösterir.
    """
    
    list_display = UserAdmin.list_display + ('user_permissions_summary', 'user_groups_display')
    list_filter = UserAdmin.list_filter + ('groups',)
    
    def user_permissions_summary(self, obj):
        """Kullanıcı izin özetini gösterir"""
        summary = get_user_permission_summary(obj)
        
        # Kullanıcı türü
        user_type_html = format_html(
            '<div style="margin-bottom: 5px;">'
            '<span style="background: {}; color: white; padding: 2px 6px; border-radius: 10px; font-size: 11px;">'
            '{}'
            '</span>'
            '</div>',
            summary['user_type_color'],
            summary['user_type']
        )
        
        # Grup sayısı
        group_count = len(summary['groups'])
        if group_count > 0:
            groups_html = format_html(
                '<div style="font-size: 12px; color: #666;">'
                '👥 {} grup, 🔑 {} izin'
                '</div>',
                group_count,
                summary['permission_count']
            )
        else:
            groups_html = format_html(
                '<div style="font-size: 12px; color: #999;">Grup yok</div>'
            )
        
        return format_html('{}<br>{}', user_type_html, groups_html)
    
    user_permissions_summary.short_description = "İzin Özeti"
    
    def user_groups_display(self, obj):
        """Kullanıcının gruplarını gösterir"""
        summary = get_user_permission_summary(obj)
        
        if not summary['groups']:
            return format_html('<span style="color: #999;">Grup yok</span>')
        
        group_badges = []
        for group in summary['groups'][:3]:  # İlk 3 grubu göster
            badge = format_html(
                '<span style="background: {}; color: white; padding: 1px 6px; border-radius: 8px; font-size: 10px; margin-right: 2px;">'
                '{} {}'
                '</span>',
                group['color'],
                group['icon'],
                group['name'][:15] + ('...' if len(group['name']) > 15 else '')
            )
            group_badges.append(badge)
        
        # Eğer 3'ten fazla grup varsa "..." ekle
        if len(summary['groups']) > 3:
            group_badges.append(
                format_html(
                    '<span style="color: #666; font-size: 10px;">+{} daha</span>',
                    len(summary['groups']) - 3
                )
            )
        
        return format_html('<br>'.join(group_badges))
    
    user_groups_display.short_description = "Gruplar"

# Admin kayıtlarını güncelle
try:
    admin.site.unregister(Group)
    admin.site.unregister(User)
except admin.sites.NotRegistered:
    pass

admin.site.register(Group, CustomGroupAdmin)
admin.site.register(User, CustomUserAdmin)

# Admin panel başlığını özelleştir
admin.site.site_header = "Küp Cadısı Yönetim Paneli"
admin.site.site_title = "Küp Cadısı Admin"
admin.site.index_title = "İçerik ve Kullanıcı Yönetimi"
