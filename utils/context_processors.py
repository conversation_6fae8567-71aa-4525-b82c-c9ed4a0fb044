"""
Template Context Processors

Template'lerde kullanılacak global değişkenleri sağlar.
"""

from utils.permissions import get_user_permission_summary

def user_permissions(request):
    """
    Kullanıcı izinlerini template'lerde kullanılabilir hale getirir.
    
    Template'lerde şu şekilde kullanılabilir:
    {% if user_perms.can_add_product %}
        <a href="{% url 'urunler:urun_ekle' %}"><PERSON><PERSON><PERSON><PERSON></a>
    {% endif %}
    """
    if not request.user.is_authenticated:
        return {
            'user_perms': {
                'can_add_product': False,
                'can_edit_product': False,
                'can_delete_product': False,
                'can_add_video': False,
                'can_edit_video': False,
                'can_delete_video': False,
                'can_add_calisma': False,
                'can_edit_calisma': False,
                'can_delete_calisma': False,
                'can_moderate_comments': False,
                'can_view_admin': <PERSON>alse,
                'is_content_editor': <PERSON><PERSON><PERSON>,
                'is_video_manager': <PERSON><PERSON><PERSON>,
                'is_product_manager': <PERSON><PERSON><PERSON>,
                'is_moderator': <PERSON><PERSON><PERSON>,
                'user_type': 'Giriş Yapmamış',
                'groups': [],
                'permission_count': 0
            }
        }
    
    # Kullanıcı izin özetini al
    summary = get_user_permission_summary(request.user)
    
    # Grup kontrolü için yardımcı fonksiyon
    def has_group(group_name):
        return any(group['name'] == group_name for group in summary['groups'])
    
    # İzin kontrolü için yardımcı fonksiyon
    def has_permission(permission):
        return permission in summary['permissions']
    
    # Template'de kullanılacak izin sözlüğü
    user_perms = {
        # Ürün izinleri
        'can_add_product': (
            request.user.is_superuser or 
            has_permission('urunler.add_product')
        ),
        'can_edit_product': (
            request.user.is_superuser or 
            has_permission('urunler.change_product')
        ),
        'can_delete_product': (
            request.user.is_superuser or 
            has_permission('urunler.delete_product')
        ),
        'can_manage_product_images': (
            request.user.is_superuser or 
            has_permission('urunler.add_productimage') or
            has_permission('urunler.change_productimage')
        ),
        
        # Video izinleri
        'can_add_video': (
            request.user.is_superuser or 
            has_permission('nasilyapilir.add_video')
        ),
        'can_edit_video': (
            request.user.is_superuser or 
            has_permission('nasilyapilir.change_video')
        ),
        'can_delete_video': (
            request.user.is_superuser or 
            has_permission('nasilyapilir.delete_video')
        ),
        
        # Çalışma izinleri
        'can_add_calisma': (
            request.user.is_superuser or 
            has_permission('calismalar.add_calisma') or
            (hasattr(request.user, 'profil') and request.user.profil.is_approved)
        ),
        'can_edit_calisma': (
            request.user.is_superuser or 
            has_permission('calismalar.change_calisma')
        ),
        'can_delete_calisma': (
            request.user.is_superuser or 
            has_permission('calismalar.delete_calisma')
        ),
        
        # Yorum moderasyonu
        'can_moderate_comments': (
            request.user.is_superuser or 
            has_permission('yorumlar.change_yorum') or
            has_permission('yorumlar.delete_yorum')
        ),
        
        # Kategori yönetimi
        'can_manage_categories': (
            request.user.is_superuser or 
            has_permission('urunler.add_category') or
            has_permission('urunler.change_category')
        ),
        
        # Admin panel erişimi
        'can_view_admin': (
            request.user.is_superuser or 
            request.user.is_staff or
            summary['permission_count'] > 0
        ),
        
        # Grup kontrolleri
        'is_content_editor': has_group('İçerik Editörü'),
        'is_video_manager': has_group('Video Yöneticisi'),
        'is_product_manager': has_group('Ürün Yöneticisi'),
        'is_calisma_manager': has_group('Çalışma Yöneticisi'),
        'is_moderator': has_group('İçerik Moderatörü'),
        'is_viewer': has_group('İçerik Görüntüleyici'),
        
        # Genel bilgiler
        'user_type': summary['user_type'],
        'user_type_color': summary['user_type_color'],
        'groups': summary['groups'],
        'permission_count': summary['permission_count'],
        'is_superuser': summary['is_superuser'],
        'is_staff': summary['is_staff'],
        
        # Yönetici paneli için özel
        'admin_sections': {
            'can_manage_users': request.user.is_superuser,
            'can_manage_groups': request.user.is_superuser or request.user.is_staff,
            'can_view_logs': request.user.is_superuser or request.user.is_staff,
            'can_manage_site_settings': request.user.is_superuser,
        }
    }
    
    return {'user_perms': user_perms}
