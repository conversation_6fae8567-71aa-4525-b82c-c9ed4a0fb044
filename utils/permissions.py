"""
<PERSON><PERSON><PERSON><PERSON>zin Yönetimi Sistemi

<PERSON>, tü<PERSON> u<PERSON>alar için kull<PERSON>ılacak granüler izin sistemini yönetir.
Yöneticiler için Türkçe açıklamalar ve kullanıcı dostu arayüz sağlar.
"""

from django.contrib.auth.models import Group, Permission, User
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import PermissionDenied
from django.contrib import messages
from django.shortcuts import redirect
from functools import wraps
from utils.logging import get_logger

logger = get_logger(__name__)

# Yönetici dostu grup tanımları
PERMISSION_GROUPS = {
    'icerik_editoru': {
        'name': 'İçerik Editörü',
        'description': 'Ürün ve video içeriklerini ekleyebilir, düzenleyebilir ve silebilir. Site içeriğinin yönetiminden sorumludur.',
        'color': '#28a745',  # Ye<PERSON>il
        'icon': '📝',
        'permissions': [
            # Ürün yönetimi
            'urunler.add_product',
            'urunler.change_product', 
            'urunler.delete_product',
            'urunler.view_product',
            'urunler.add_productimage',
            'urunler.change_productimage',
            'urunler.delete_productimage',
            # Video yönetimi
            'nasilyapilir.add_video',
            'nasilyapilir.change_video',
            'nasilyapilir.delete_video',
            'nasilyapilir.view_video',
            # Kategori yönetimi
            'urunler.add_category',
            'urunler.change_category',
            'urunler.delete_category',
        ]
    },
    'video_yoneticisi': {
        'name': 'Video Yöneticisi',
        'description': 'Sadece "Nasıl Yapılır" videolarını yönetebilir. Video ekleme, düzenleme ve silme yetkisine sahiptir.',
        'color': '#dc3545',  # Kırmızı
        'icon': '🎬',
        'permissions': [
            'nasilyapilir.add_video',
            'nasilyapilir.change_video',
            'nasilyapilir.delete_video',
            'nasilyapilir.view_video',
        ]
    },
    'urun_yoneticisi': {
        'name': 'Ürün Yöneticisi', 
        'description': 'Sadece ürünleri yönetebilir. Ürün ekleme, düzenleme, silme ve ürün resimlerini yönetme yetkisine sahiptir.',
        'color': '#007bff',  # Mavi
        'icon': '🛍️',
        'permissions': [
            'urunler.add_product',
            'urunler.change_product',
            'urunler.delete_product', 
            'urunler.view_product',
            'urunler.add_productimage',
            'urunler.change_productimage',
            'urunler.delete_productimage',
            'urunler.add_category',
            'urunler.change_category',
        ]
    },
    'moderator': {
        'name': 'İçerik Moderatörü',
        'description': 'Yorumları onaylayabilir, silebilir ve kullanıcı çalışmalarını moderasyona tabi tutabilir.',
        'color': '#ffc107',  # Sarı
        'icon': '💬',
        'permissions': [
            'yorumlar.add_yorum',
            'yorumlar.change_yorum',
            'yorumlar.delete_yorum',
            'yorumlar.view_yorum',
            'calismalar.view_calisma',
            'calismalar.change_calisma',  # Sadece moderasyon için
        ]
    },
    'calisma_yoneticisi': {
        'name': 'Çalışma Yöneticisi',
        'description': 'Kullanıcı çalışmalarını yönetebilir. Çalışma ekleme, düzenleme ve fotoğraf yönetimi yetkisine sahiptir.',
        'color': '#6f42c1',  # Mor
        'icon': '🎨',
        'permissions': [
            'calismalar.add_calisma',
            'calismalar.change_calisma',
            'calismalar.delete_calisma',
            'calismalar.view_calisma',
            'calismalar.add_calismafotograf',
            'calismalar.change_calismafotograf',
            'calismalar.delete_calismafotograf',
        ]
    },
    'icerik_goruntuleyici': {
        'name': 'İçerik Görüntüleyici',
        'description': 'Sadece içerikleri görüntüleyebilir. Herhangi bir değişiklik yapamaz, sadece okuma yetkisine sahiptir.',
        'color': '#6c757d',  # Gri
        'icon': '👀',
        'permissions': [
            'urunler.view_product',
            'nasilyapilir.view_video',
            'calismalar.view_calisma',
            'yorumlar.view_yorum',
        ]
    }
}

def setup_permission_groups():
    """
    Tüm izin gruplarını oluşturur ve gerekli izinleri atar.
    
    Bu fonksiyon management command ile çalıştırılmalıdır:
    python manage.py setup_permissions
    """
    created_groups = []
    updated_groups = []
    
    for group_key, group_info in PERMISSION_GROUPS.items():
        # Grubu al veya oluştur
        group, created = Group.objects.get_or_create(name=group_info['name'])
        
        if created:
            created_groups.append(group_info['name'])
            logger.info(f"Yeni grup oluşturuldu: {group_info['name']}")
        else:
            updated_groups.append(group_info['name'])
            logger.info(f"Mevcut grup güncellendi: {group_info['name']}")
        
        # Mevcut izinleri temizle
        group.permissions.clear()
        
        # Yeni izinleri ekle
        for permission_code in group_info['permissions']:
            try:
                app_label, codename = permission_code.split('.')
                permission = Permission.objects.get(
                    content_type__app_label=app_label,
                    codename=codename
                )
                group.permissions.add(permission)
                logger.debug(f"İzin eklendi: {group_info['name']} -> {permission_code}")
            except Permission.DoesNotExist:
                logger.warning(f"İzin bulunamadı: {permission_code}")
            except ValueError:
                logger.error(f"Geçersiz izin formatı: {permission_code}")
    
    return {
        'created': created_groups,
        'updated': updated_groups
    }

def get_user_permission_summary(user):
    """
    Kullanıcının tüm izinlerini özetler.
    Yönetici panelinde kullanım için.
    
    Args:
        user: Kontrol edilecek kullanıcı
        
    Returns:
        dict: İzin özeti
    """
    if not user.is_authenticated:
        return {
            'user_type': 'Giriş Yapmamış',
            'groups': [],
            'permissions': [],
            'is_superuser': False,
            'is_staff': False
        }
    
    # Kullanıcı türünü belirle
    if user.is_superuser:
        user_type = 'Süper Yönetici (Tüm Yetkiler)'
        user_type_color = '#dc3545'  # Kırmızı
    elif user.is_staff:
        user_type = 'Yönetici'
        user_type_color = '#007bff'  # Mavi
    else:
        user_type = 'Normal Kullanıcı'
        user_type_color = '#28a745'  # Yeşil
    
    # Kullanıcının gruplarını al
    user_groups = []
    for group in user.groups.all():
        group_info = None
        for group_key, info in PERMISSION_GROUPS.items():
            if info['name'] == group.name:
                group_info = info
                break
        
        user_groups.append({
            'name': group.name,
            'description': group_info['description'] if group_info else 'Özel grup',
            'color': group_info['color'] if group_info else '#6c757d',
            'icon': group_info['icon'] if group_info else '👥'
        })
    
    # Kullanıcının tüm izinlerini al
    all_permissions = user.get_all_permissions()
    
    return {
        'user_type': user_type,
        'user_type_color': user_type_color,
        'groups': user_groups,
        'permissions': list(all_permissions),
        'permission_count': len(all_permissions),
        'is_superuser': user.is_superuser,
        'is_staff': user.is_staff
    }

def check_permission(user, permission_code, obj=None):
    """
    Kullanıcının belirli bir izni olup olmadığını kontrol eder.
    
    Args:
        user: Kontrol edilecek kullanıcı
        permission_code: Kontrol edilecek izin (örn: 'urunler.add_product')
        obj: İsteğe bağlı, belirli bir nesne için kontrol
        
    Returns:
        bool: İzin varsa True, yoksa False
    """
    if not user.is_authenticated:
        return False
    
    # Süper kullanıcılar her zaman yetkili
    if user.is_superuser:
        return True
    
    # İzin kontrolü
    return user.has_perm(permission_code, obj)

def require_permission(permission_code, raise_exception=False, redirect_url=None):
    """
    Belirli bir izin gerektiren view'lar için decorator.
    
    Args:
        permission_code: Gerekli izin kodu
        raise_exception: True ise PermissionDenied fırlatır
        redirect_url: Yetki yoksa yönlendirilecek URL
        
    Usage:
        @require_permission('urunler.add_product')
        def urun_ekle(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not check_permission(request.user, permission_code):
                # Yetkisiz erişimi logla
                logger.warning("Yetkisiz erişim denemesi", extra={
                    'user_id': request.user.id if request.user.is_authenticated else None,
                    'username': request.user.username if request.user.is_authenticated else 'Anonymous',
                    'permission': permission_code,
                    'view_name': view_func.__name__,
                    'ip_address': request.META.get('REMOTE_ADDR'),
                    'operation': 'unauthorized_access'
                })
                
                if raise_exception:
                    raise PermissionDenied(f"Bu işlem için '{permission_code}' yetkisi gereklidir.")
                
                # AJAX isteği kontrolü
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': 'Bu işlemi yapmaya yetkiniz yok.',
                        'required_permission': permission_code
                    }, status=403)
                
                # Normal istek için mesaj ve yönlendirme
                if request.user.is_authenticated:
                    messages.error(request, 'Bu işlemi yapmaya yetkiniz yok. Gerekli izin: ' + permission_code)
                else:
                    messages.error(request, 'Bu işlemi yapmak için giriş yapmanız ve gerekli yetkiye sahip olmanız gerekir.')
                
                return redirect(redirect_url or 'anasayfa:ana_sayfa')
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
