"""
Admin Panel Dashboard Özelleştirmeleri

Admin panelinin ana sayfasını daha kullanıcı dostu hale getiren fonksiyonlar.
"""

from django.contrib import admin
from django.contrib.admin import AdminSite
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta

class CustomAdminSite(AdminSite):
    """
    Özelleştirilmiş Admin Site
    """
    site_header = "🎨 Küp Cadısı Yönetim Paneli"
    site_title = "Küp Cadısı Admin"
    index_title = "📊 Yönetim Paneli Ana Sayfa"
    
    def index(self, request, extra_context=None):
        """
        Admin ana sayfasını özelleştir
        """
        extra_context = extra_context or {}
        
        # İstatistikleri hesapla
        stats = self.get_dashboard_stats()
        extra_context.update(stats)
        
        return super().index(request, extra_context)
    
    def get_dashboard_stats(self):
        """
        Dashboard için istatistikleri hesapla
        """
        from django.contrib.auth.models import User
        from urunler.models import Product, Category
        from calismalar.models import Calisma
        from nasilyapilir.models import Video
        from yorumlar.models import Yorum
        
        # Temel sayılar
        stats = {
            'total_users': User.objects.count(),
            'total_products': Product.objects.count(),
            'total_categories': Category.objects.count(),
            'total_calismalar': Calisma.objects.count(),
            'total_videos': Video.objects.count(),
            'total_comments': Yorum.objects.count(),
        }
        
        # Son 30 gün
        last_30_days = timezone.now() - timedelta(days=30)
        
        stats.update({
            'new_users_30d': User.objects.filter(date_joined__gte=last_30_days).count(),
            'new_products_30d': Product.objects.filter(created_at__gte=last_30_days).count(),
            'new_calismalar_30d': Calisma.objects.filter(created_at__gte=last_30_days).count(),
            'new_videos_30d': Video.objects.filter(created_at__gte=last_30_days).count(),
        })
        
        # Aktif/Pasif durumlar
        stats.update({
            'active_products': Product.objects.filter(is_available=True).count(),
            'inactive_products': Product.objects.filter(is_available=False).count(),
            'approved_calismalar': Calisma.objects.filter(is_approved=True).count(),
            'pending_calismalar': Calisma.objects.filter(is_approved=False).count(),
        })
        
        # Stok durumu
        stats.update({
            'out_of_stock': Product.objects.filter(stock_quantity=0).count(),
            'low_stock': Product.objects.filter(stock_quantity__gt=0, stock_quantity__lte=5).count(),
            'good_stock': Product.objects.filter(stock_quantity__gt=5).count(),
        })
        
        return stats

def get_admin_dashboard_context():
    """
    Admin dashboard için context verilerini hazırla
    """
    from django.contrib.auth.models import User, Group
    from urunler.models import Product, Category
    from calismalar.models import Calisma
    from nasilyapilir.models import Video
    from yorumlar.models import Yorum
    
    # Hızlı istatistikler
    quick_stats = [
        {
            'title': '👥 Toplam Kullanıcı',
            'count': User.objects.count(),
            'icon': 'fas fa-users',
            'color': '#3498db',
            'url': reverse('admin:auth_user_changelist'),
        },
        {
            'title': '📦 Toplam Ürün',
            'count': Product.objects.count(),
            'icon': 'fas fa-box',
            'color': '#e74c3c',
            'url': reverse('admin:urunler_product_changelist'),
        },
        {
            'title': '🎨 Toplam Çalışma',
            'count': Calisma.objects.count(),
            'icon': 'fas fa-palette',
            'color': '#9b59b6',
            'url': reverse('admin:calismalar_calisma_changelist'),
        },
        {
            'title': '🎬 Toplam Video',
            'count': Video.objects.count(),
            'icon': 'fas fa-video',
            'color': '#f39c12',
            'url': reverse('admin:nasilyapilir_video_changelist'),
        },
    ]
    
    # Son aktiviteler
    recent_activities = []
    
    # Son eklenen ürünler
    recent_products = Product.objects.order_by('-created_at')[:5]
    for product in recent_products:
        recent_activities.append({
            'type': 'product',
            'title': f'Yeni ürün: {product.name}',
            'time': product.created_at,
            'icon': 'fas fa-box',
            'color': '#e74c3c',
            'url': reverse('admin:urunler_product_change', args=[product.id]),
        })
    
    # Son eklenen çalışmalar
    recent_calismalar = Calisma.objects.order_by('-created_at')[:5]
    for calisma in recent_calismalar:
        recent_activities.append({
            'type': 'calisma',
            'title': f'Yeni çalışma: {calisma.baslik}',
            'time': calisma.created_at,
            'icon': 'fas fa-palette',
            'color': '#9b59b6',
            'url': reverse('admin:calismalar_calisma_change', args=[calisma.id]),
        })
    
    # Son kayıt olan kullanıcılar
    recent_users = User.objects.order_by('-date_joined')[:5]
    for user in recent_users:
        recent_activities.append({
            'type': 'user',
            'title': f'Yeni kullanıcı: {user.username}',
            'time': user.date_joined,
            'icon': 'fas fa-user',
            'color': '#3498db',
            'url': reverse('admin:auth_user_change', args=[user.id]),
        })
    
    # Aktiviteleri zamana göre sırala
    recent_activities.sort(key=lambda x: x['time'], reverse=True)
    recent_activities = recent_activities[:10]  # Son 10 aktivite
    
    # Dikkat gerektiren durumlar
    alerts = []
    
    # Stok uyarıları
    out_of_stock = Product.objects.filter(stock_quantity=0, is_available=True).count()
    if out_of_stock > 0:
        alerts.append({
            'type': 'danger',
            'title': f'⚠️ {out_of_stock} ürünün stoğu tükendi',
            'message': 'Bu ürünler hala aktif durumda ama stokları yok.',
            'url': reverse('admin:urunler_product_changelist') + '?stock_quantity__exact=0&is_available__exact=1',
        })
    
    low_stock = Product.objects.filter(stock_quantity__gt=0, stock_quantity__lte=5).count()
    if low_stock > 0:
        alerts.append({
            'type': 'warning',
            'title': f'📦 {low_stock} ürünün stoğu azalıyor',
            'message': 'Bu ürünlerin stok miktarı 5 veya daha az.',
            'url': reverse('admin:urunler_product_changelist') + '?stock_quantity__lte=5&stock_quantity__gt=0',
        })
    
    # Onay bekleyen çalışmalar
    pending_calismalar = Calisma.objects.filter(is_approved=False).count()
    if pending_calismalar > 0:
        alerts.append({
            'type': 'info',
            'title': f'⏳ {pending_calismalar} çalışma onay bekliyor',
            'message': 'Bu çalışmalar henüz onaylanmamış.',
            'url': reverse('admin:calismalar_calisma_changelist') + '?is_approved__exact=0',
        })
    
    return {
        'quick_stats': quick_stats,
        'recent_activities': recent_activities,
        'alerts': alerts,
    }

# Admin site'ı özelleştir
admin_site = CustomAdminSite(name='custom_admin')

def admin_dashboard_view(request):
    """
    Admin dashboard view
    """
    context = get_admin_dashboard_context()
    return render(request, 'admin/dashboard.html', context)
