"""
CSRF Yardımcı Fonksiyonları

Cache middleware ile CSRF token sorunlarını çözmek için yardımcı fonksiyonlar.
"""

from django.middleware.csrf import get_token
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
import json

@require_http_methods(["GET"])
def get_csrf_token(request):
    """
    CSRF token'ı JSON olarak döndürür.
    AJAX istekleri için kullanılır.
    
    Usage:
        fetch('/utils/csrf-token/')
        .then(response => response.json())
        .then(data => {
            // data.csrf_token kullan
        });
    """
    token = get_token(request)
    return JsonResponse({
        'csrf_token': token,
        'success': True
    })

def refresh_csrf_token(request):
    """
    Mevcut CSRF token'ı yeniler ve yeni token'ı döndürür.
    Cache sorunları için kullanılır.
    """
    # Mevcut token'ı sil
    if hasattr(request, 'META') and 'CSRF_COOKIE' in request.META:
        del request.META['CSRF_COOKIE']
    
    # Yeni token oluştur
    new_token = get_token(request)
    
    return JsonResponse({
        'csrf_token': new_token,
        'success': True,
        'message': 'CSRF token yenilendi'
    })

def add_csrf_to_context(request):
    """
    Template context'ine CSRF token ekler.
    Context processor olarak kullanılabilir.
    """
    return {
        'csrf_token': get_token(request)
    }

class CSRFErrorHandler:
    """
    CSRF hatalarını ele alan sınıf.
    Middleware olarak kullanılabilir.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        return response
    
    def process_exception(self, request, exception):
        """CSRF hatalarını yakalar ve kullanıcı dostu mesaj döndürür"""
        from django.middleware.csrf import CsrfViewMiddleware
        
        if isinstance(exception, CsrfViewMiddleware):
            # AJAX isteği mi kontrol et
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'error': 'csrf_failed',
                    'message': 'Güvenlik doğrulaması başarısız. Sayfa yenilenecek.',
                    'csrf_token': get_token(request)
                }, status=403)
            
            # Normal istek için özel hata sayfası
            from django.shortcuts import render
            return render(request, 'errors/csrf_error.html', {
                'csrf_token': get_token(request)
            }, status=403)
        
        return None

# JavaScript için CSRF token alma fonksiyonu
CSRF_JS_CODE = """
// CSRF Token Yardımcı Fonksiyonları
window.CSRFUtils = {
    // CSRF token'ı al
    getToken: function() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : null;
    },
    
    // CSRF token'ı yenile
    refreshToken: function() {
        return fetch('/utils/csrf-token/')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Tüm CSRF input'larını güncelle
                    document.querySelectorAll('[name=csrfmiddlewaretoken]').forEach(input => {
                        input.value = data.csrf_token;
                    });
                    
                    // Meta tag'i güncelle
                    let metaTag = document.querySelector('meta[name=csrf-token]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.csrf_token);
                    }
                    
                    return data.csrf_token;
                }
                throw new Error('CSRF token yenilenemedi');
            });
    },
    
    // AJAX istekleri için header ekle
    setupAjax: function() {
        // jQuery varsa
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", CSRFUtils.getToken());
                    }
                }
            });
        }
        
        // Fetch API için
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (options.method && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(options.method)) {
                options.headers = options.headers || {};
                options.headers['X-CSRFToken'] = CSRFUtils.getToken();
            }
            return originalFetch(url, options);
        };
    },
    
    // CSRF hatası durumunda otomatik yenileme
    handleError: function(response) {
        if (response.status === 403) {
            return response.json().then(data => {
                if (data.error === 'csrf_failed') {
                    console.warn('CSRF hatası, token yenileniyor...');
                    return this.refreshToken().then(() => {
                        // İsteği tekrar dene
                        window.location.reload();
                    });
                }
                throw new Error(data.message || 'CSRF hatası');
            });
        }
        return response;
    }
};

// Sayfa yüklendiğinde AJAX'ı ayarla
document.addEventListener('DOMContentLoaded', function() {
    CSRFUtils.setupAjax();
});
"""
