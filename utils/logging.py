"""
Logging utilities for the Küp Cadısı project.
"""

import logging
import traceback
import sys
from functools import wraps
from typing import Any, Dict, Optional
from django.conf import settings
from django.http import JsonResponse


class StructuredLogger:
    """
    Structured logging utility for consistent log formatting.
    """
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log debug message with structured data."""
        self._log(logging.DEBUG, message, extra, **kwargs)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log info message with structured data."""
        self._log(logging.INFO, message, extra, **kwargs)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log warning message with structured data."""
        self._log(logging.WARNING, message, extra, **kwargs)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log error message with structured data."""
        self._log(logging.ERROR, message, extra, **kwargs)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log critical message with structured data."""
        self._log(logging.CRITICAL, message, extra, **kwargs)
    
    def exception(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log exception with traceback."""
        extra = extra or {}
        extra.update({
            'traceback': traceback.format_exc(),
            'exception_type': sys.exc_info()[0].__name__ if sys.exc_info()[0] else None,
        })
        self._log(logging.ERROR, message, extra, **kwargs)
    
    def _log(self, level: int, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Internal logging method."""
        if extra:
            # Structured data'yı message'a ekle
            structured_message = f"{message} | {extra}"
            self.logger.log(level, structured_message, **kwargs)
        else:
            self.logger.log(level, message, **kwargs)


def get_logger(name: str) -> StructuredLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        StructuredLogger instance
    """
    return StructuredLogger(name)


def log_view_error(view_name: str, error: Exception, request=None, extra_data: Optional[Dict] = None):
    """
    Log view errors with context information.
    
    Args:
        view_name: Name of the view where error occurred
        error: Exception instance
        request: Django request object (optional)
        extra_data: Additional data to log
    """
    logger = get_logger('kupcadisi.views')
    
    error_data = {
        'view': view_name,
        'error_type': type(error).__name__,
        'error_message': str(error),
    }
    
    if request:
        error_data.update({
            'method': request.method,
            'path': request.path,
            'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'ip_address': get_client_ip(request),
        })
    
    if extra_data:
        error_data.update(extra_data)
    
    logger.exception(f"View error in {view_name}", extra=error_data)


def log_model_error(model_name: str, operation: str, error: Exception, extra_data: Optional[Dict] = None):
    """
    Log model operation errors.
    
    Args:
        model_name: Name of the model
        operation: Operation being performed (save, delete, etc.)
        error: Exception instance
        extra_data: Additional data to log
    """
    logger = get_logger('kupcadisi.models')
    
    error_data = {
        'model': model_name,
        'operation': operation,
        'error_type': type(error).__name__,
        'error_message': str(error),
    }
    
    if extra_data:
        error_data.update(extra_data)
    
    logger.exception(f"Model error in {model_name}.{operation}", extra=error_data)


def log_performance(operation: str, duration: float, extra_data: Optional[Dict] = None):
    """
    Log performance metrics.
    
    Args:
        operation: Operation name
        duration: Duration in seconds
        extra_data: Additional data to log
    """
    logger = get_logger('kupcadisi.performance')
    
    perf_data = {
        'operation': operation,
        'duration_seconds': duration,
        'duration_ms': duration * 1000,
    }
    
    if extra_data:
        perf_data.update(extra_data)
    
    # Log as warning if operation takes too long
    level = 'warning' if duration > 1.0 else 'info'
    getattr(logger, level)(f"Performance: {operation}", extra=perf_data)


def get_client_ip(request):
    """
    Get client IP address from request.
    
    Args:
        request: Django request object
    
    Returns:
        Client IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def handle_view_exception(view_name: str):
    """
    Decorator to handle view exceptions with proper logging.
    
    Args:
        view_name: Name of the view for logging
    
    Returns:
        Decorator function
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                return view_func(request, *args, **kwargs)
            except Exception as e:
                log_view_error(view_name, e, request, {
                    'args': args,
                    'kwargs': kwargs,
                })
                
                # Return appropriate error response
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'error': 'Bir hata oluştu. Lütfen tekrar deneyin.',
                        'error_code': 'INTERNAL_ERROR'
                    }, status=500)
                else:
                    # For non-AJAX requests, re-raise to let Django handle it
                    raise
        
        return wrapper
    return decorator


def log_user_action(user, action: str, target: str = None, extra_data: Optional[Dict] = None):
    """
    Log user actions for audit trail.
    
    Args:
        user: User instance
        action: Action performed
        target: Target of the action (optional)
        extra_data: Additional data to log
    """
    logger = get_logger('kupcadisi.audit')
    
    audit_data = {
        'user_id': user.id if hasattr(user, 'id') else None,
        'username': str(user),
        'action': action,
        'target': target,
    }
    
    if extra_data:
        audit_data.update(extra_data)
    
    logger.info(f"User action: {action}", extra=audit_data)
