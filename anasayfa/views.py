from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth.models import User
import json
import os
from .models import Hakkimda, Egitim, IletisimBilgisi, SosyalMedya
from .forms import HakkimdaForm, EgitimForm, IletisimBilgisiForm, SosyalMedyaForm
from django.views.decorators.cache import cache_page
from django.conf import settings
from .utils import get_primary_profile_data, merge_contact_data, get_user_works
from .utils.social_media import get_platform_by_index, validate_platform, get_platform_info
from utils.logging import get_logger, handle_view_exception, log_user_action, log_view_error
from utils.file_management import safe_delete_file, safe_move_file

# Logger'ı başlat
logger = get_logger(__name__)

def ana_sayfa(request):
    """Ana sayfa görünümü"""
    # <PERSON><PERSON> kullanmıyoruz çünkü authentication durumu kullanıcıya özel
    return render(request, 'anasayfa/ana_sayfa.html')

def hakkimda(request):
    """Hakkımda sayfasını render eder."""
    # Özel manager metodunu kullanarak birincil profili al
    hakkimda = Hakkimda.objects.get_primary_or_fallback(current_user=request.user)

    # Eğitim bilgilerini al
    egitimler = []
    if hakkimda:
        egitimler = Egitim.objects.filter(hakkimda=hakkimda)

    # Son çalışmaları al
    son_calismalar = get_user_works(hakkimda.user if hakkimda else None)

    context = {
        'hakkimda': hakkimda,
        'egitimler': egitimler,
        'son_calismalar': son_calismalar,
        'can_edit': request.user.is_authenticated and request.user.is_staff
    }
    return render(request, 'anasayfa/hakkimda.html', context)

def iletisim(request):
    """İletişim sayfasını render eder."""
    # Özel manager metodlarını kullanarak birincil bilgileri al
    iletisim_bilgisi = IletisimBilgisi.objects.get_primary_or_fallback(current_user=request.user)
    sosyal_medya_hesaplari = SosyalMedya.objects.get_primary_user_accounts(current_user=request.user)

    # Verileri birleştir
    merged_data = merge_contact_data(iletisim_bilgisi, sosyal_medya_hesaplari)

    context = {
        'iletisim': merged_data['iletisim'],
        'sosyal_medya': merged_data['sosyal_medya'],
        'can_edit': request.user.is_authenticated and request.user.is_staff
    }

    return render(request, 'anasayfa/iletisim.html', context)


@login_required
def hakkimda_duzenle(request):
    # Birden fazla Hakkimda nesnesi olabilir, ilkini al
    hakkimda = Hakkimda.objects.filter(user=request.user).first()

    if not hakkimda:
        hakkimda = Hakkimda(user=request.user)

    if request.method == 'POST':
        # Eski resmi kaydet
        eski_resim = None
        if hakkimda.resim:
            eski_resim = hakkimda.resim.path

        form = HakkimdaForm(request.POST, request.FILES, instance=hakkimda)
        if form.is_valid():
            # Yeni form kaydedildi
            yeni_hakkimda = form.save()

            # Eğer resim değiştiyse ve eski resim varsa, eski resmi güvenli bir şekilde sil
            if eski_resim and (not yeni_hakkimda.resim or eski_resim != yeni_hakkimda.resim.path):
                success, error_msg = safe_delete_file(eski_resim, create_backup=True)

                if success:
                    logger.info("Profil resmi güvenli bir şekilde silindi", extra={
                        'user': request.user.username,
                        'file_path': eski_resim,
                        'operation': 'safe_file_delete',
                        'backup_created': True
                    })

                    log_user_action(request.user, 'delete_profile_image', eski_resim, {
                        'backup_created': True,
                        'new_image': yeni_hakkimda.resim.path if yeni_hakkimda.resim else None
                    })
                else:
                    logger.error("Profil resmi silinemedi", extra={
                        'user': request.user.username,
                        'file_path': eski_resim,
                        'error': error_msg,
                        'operation': 'safe_file_delete'
                    })

                    # Kullanıcıya bilgi ver ama işlemi durdurmayalım
                    messages.warning(request,
                        'Profil bilgileri güncellendi ancak eski resim silinemedi. '
                        'Bu durum disk alanını etkileyebilir.')

            # Başarılı güncelleme mesajı
            log_user_action(request.user, 'update_profile', 'hakkimda', {
                'has_image': bool(yeni_hakkimda.resim),
                'image_changed': bool(eski_resim and yeni_hakkimda.resim and eski_resim != yeni_hakkimda.resim.path)
            })

            messages.success(request, 'Hakkımda bilgileri başarıyla güncellendi.')
        else:
            messages.error(request, 'Lütfen formu doğru şekilde doldurun.')
    else:
        form = HakkimdaForm(instance=hakkimda)
        return render(request, 'anasayfa/hakkimda_duzenle.html', {
            'form': form,
            'hakkimda': hakkimda
        })

    return redirect('anasayfa:hakkimda')

@login_required
def egitim_ekle(request):
    # Hakkimda nesnesini al veya oluştur
    hakkimda = Hakkimda.objects.filter(user=request.user).first()
    if not hakkimda:
        hakkimda = Hakkimda.objects.create(user=request.user, baslik="", aciklama="")

    if request.method == 'POST':
        form = EgitimForm(request.POST)
        if form.is_valid():
            egitim = form.save(commit=False)
            egitim.hakkimda = hakkimda
            egitim.save()
            messages.success(request, 'Eğitim bilgisi başarıyla eklendi.')
        else:
            messages.error(request, 'Lütfen formu doğru şekilde doldurun.')
    return redirect('anasayfa:hakkimda_duzenle')

@login_required
def egitim_duzenle(request, pk):
    egitim = get_object_or_404(Egitim, pk=pk, hakkimda__user=request.user)

    if request.method == 'POST':
        form = EgitimForm(request.POST, instance=egitim)
        if form.is_valid():
            form.save()
            messages.success(request, 'Eğitim bilgisi başarıyla güncellendi.')
        else:
            messages.error(request, 'Lütfen formu doğru şekilde doldurun.')
    return redirect('anasayfa:hakkimda_duzenle')

@login_required
def egitim_sil(request, pk):
    egitim = get_object_or_404(Egitim, pk=pk, hakkimda__user=request.user)
    egitim.delete()
    messages.success(request, 'Eğitim bilgisi başarıyla silindi.')
    return redirect('anasayfa:hakkimda_duzenle')

@login_required
def iletisim_duzenle(request):
    iletisim, created = IletisimBilgisi.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = IletisimBilgisiForm(request.POST, instance=iletisim)
        if form.is_valid():
            form.save()
            messages.success(request, 'İletişim bilgileri başarıyla güncellendi.')
            return redirect('anasayfa:iletisim')
    else:
        form = IletisimBilgisiForm(instance=iletisim)

    return render(request, 'anasayfa/iletisim_duzenle.html', {
        'form': form,
        'iletisim': iletisim
    })

@login_required
def sosyal_medya_ekle(request):
    if request.method == 'POST':
        form = SosyalMedyaForm(request.POST)
        if form.is_valid():
            sosyal_medya = form.save(commit=False)
            sosyal_medya.user = request.user
            sosyal_medya.save()
            messages.success(request, 'Sosyal medya hesabı başarıyla eklendi.')
            return redirect('anasayfa:iletisim')
    else:
        form = SosyalMedyaForm()

    return render(request, 'anasayfa/sosyal_medya_ekle.html', {'form': form})

@login_required
def sosyal_medya_duzenle(request, pk):
    sosyal_medya = get_object_or_404(SosyalMedya, pk=pk, user=request.user)

    if request.method == 'POST':
        form = SosyalMedyaForm(request.POST, instance=sosyal_medya)
        if form.is_valid():
            form.save()
            messages.success(request, 'Sosyal medya hesabı başarıyla güncellendi.')
            return redirect('anasayfa:iletisim')
    else:
        form = SosyalMedyaForm(instance=sosyal_medya)

    return render(request, 'anasayfa/sosyal_medya_duzenle.html', {'form': form})

@login_required
def sosyal_medya_sil(request, pk):
    sosyal_medya = get_object_or_404(SosyalMedya, pk=pk, user=request.user)
    sosyal_medya.delete()
    messages.success(request, 'Sosyal medya hesabı başarıyla silindi.')
    return redirect('anasayfa:iletisim')

# Inline Editing Views
@login_required
@require_POST
@handle_view_exception('hakkimda_inline_update')
def hakkimda_inline_update(request):
    """Hakkımda sayfasındaki içeriği AJAX ile günceller"""
    # Form verilerini al
    content_type = request.POST.get('content_type')
    content_id = request.POST.get('content_id', '0')
    content = request.POST.get('content')

    logger.debug("Hakkımda inline update başlatıldı", extra={
        'user': request.user.username,
        'content_type': content_type,
        'content_id': content_id,
        'content_length': len(content) if content else 0
    })

    # Oturum açmış kullanıcının Hakkimda nesnesini al/oluştur
    hakkimda = Hakkimda.objects.filter(user=request.user).first()
    if not hakkimda:
        hakkimda = Hakkimda.objects.create(user=request.user)
        logger.info("Yeni Hakkımda nesnesi oluşturuldu", extra={
            'user': request.user.username,
            'hakkimda_id': hakkimda.id
        })

    # İçerik tipine göre güncelleme yap
    updated = False

    # İçerik tiplerini Hakkimda modeli alanlarına eşleştir
    # Sadece gerçekte var olan model alanları kullanılıyor
    field_mapping = {
        'hakkimda_baslik': 'baslik',
        'hakkimda_aciklama': 'aciklama',
        'hakkimda_sanat_baslangic': 'sanat_baslangic',
        'hakkimda_kullandigi_teknikler': 'kullandigi_teknikler',
        'hakkimda_atolye_bilgileri': 'atolye_bilgileri',
        # Eski mapping'ler (geriye uyumluluk için)
        'atolye_aciklama': 'atolye_bilgileri',  # Eski frontend kodu için
    }

    # İçerik tipine karşılık gelen model alanını bul
    model_field = field_mapping.get(content_type)

    if model_field:
        # Modelde bu alan var mı kontrol et
        if hasattr(hakkimda, model_field):
            # Alanı güncelle
            setattr(hakkimda, model_field, content)
            hakkimda.save()
            updated = True

            logger.info("Hakkımda alanı güncellendi", extra={
                'user': request.user.username,
                'field': model_field,
                'content_type': content_type,
                'hakkimda_id': hakkimda.id
            })

            log_user_action(request.user, 'update_profile_field', model_field, {
                'content_type': content_type,
                'field': model_field
            })
        else:
            logger.error("Hakkımda modelinde alan bulunamadı", extra={
                'user': request.user.username,
                'field': model_field,
                'content_type': content_type,
                'available_fields': [f.name for f in Hakkimda._meta.get_fields() if not f.many_to_many and not f.one_to_many],
                'operation': 'field_mapping_validation'
            })
    else:
        logger.warning("İçerik tipi için alan eşleştirmesi bulunamadı", extra={
            'user': request.user.username,
            'content_type': content_type,
            'available_mappings': list(field_mapping.keys()),
            'operation': 'field_mapping_lookup'
        })

    return JsonResponse({'success': True, 'updated': updated})

@login_required
@require_POST
@handle_view_exception('iletisim_inline_update')
def iletisim_inline_update(request):
    """İletişim sayfasındaki içeriği AJAX ile günceller"""
    data = json.loads(request.body)
    content_type = data.get('content_type')
    content_id = data.get('content_id', 0)
    content = data.get('content')

    logger.debug("İletişim inline update başlatıldı", extra={
        'user': request.user.username,
        'content_type': content_type,
        'content_id': content_id,
        'content_length': len(content) if content else 0
    })

    # Oturum açmış kullanıcının IletisimBilgisi nesnesini al/oluştur
    iletisim, created = IletisimBilgisi.objects.get_or_create(user=request.user)

    if created:
        logger.info("Yeni İletişim Bilgisi nesnesi oluşturuldu", extra={
            'user': request.user.username,
            'iletisim_id': iletisim.id
        })

    # İçerik tipine ve indeksine göre güncelleme yap
    if content_type == 'iletisim_bilgisi':
        field_map = {
            0: ('adres', 'Adres'),
            1: ('email', 'E-posta'),
            2: ('telefon', 'Telefon'),
            3: ('calisma_saatleri', 'Çalışma Saatleri'),
            4: ('baslik', 'Başlık')  # Yeni eklenen alan
        }

        field_info = field_map.get(int(content_id))
        if not field_info:
            logger.warning("Geçersiz içerik indeksi", extra={
                'user': request.user.username,
                'content_id': content_id,
                'valid_ids': list(field_map.keys())
            })
            return JsonResponse({'success': False, 'error': 'Geçersiz içerik indeksi'})

        field_name, field_display = field_info
        setattr(iletisim, field_name, content)
        iletisim.save()

        logger.info("İletişim bilgisi güncellendi", extra={
            'user': request.user.username,
            'field': field_name,
            'field_display': field_display,
            'iletisim_id': iletisim.id
        })

        log_user_action(request.user, 'update_contact_field', field_name, {
            'field_display': field_display,
            'content_id': content_id
        })

    elif content_type == 'iletisim_baslik':
        # İletişim başlığını güncelle
        iletisim.baslik = content
        iletisim.save()

        logger.info("İletişim başlığı güncellendi", extra={
            'user': request.user.username,
            'iletisim_id': iletisim.id,
            'new_baslik': content
        })

        log_user_action(request.user, 'update_contact_title', 'iletisim_baslik', {
            'new_title': content
        })

    elif content_type == 'sosyal_medya_url':
        # Platform mapping'ini model choices'dan al
        platform = get_platform_by_index(int(content_id))

        if not platform:
            logger.warning("Geçersiz sosyal medya platform indeksi", extra={
                'user': request.user.username,
                'content_id': content_id,
                'available_platforms': get_platform_info()
            })
            return JsonResponse({'success': False, 'error': 'Geçersiz platform indeksi'})

        # Platform'un geçerli olduğunu kontrol et
        if not validate_platform(platform):
            logger.error("Geçersiz sosyal medya platformu", extra={
                'user': request.user.username,
                'platform': platform,
                'content_id': content_id
            })
            return JsonResponse({'success': False, 'error': 'Geçersiz platform'})
        sosyal_medya, created = SosyalMedya.objects.get_or_create(
            user=request.user,
            platform=platform
        )
        sosyal_medya.url = content
        sosyal_medya.save()

        logger.info("Sosyal medya URL'si güncellendi", extra={
            'user': request.user.username,
            'platform': platform,
            'sosyal_medya_id': sosyal_medya.id,
            'created': created
        })

        log_user_action(request.user, 'update_social_media', platform, {
            'content_id': content_id,
            'created': created
        })

    else:
        logger.warning("Geçersiz içerik tipi", extra={
            'user': request.user.username,
            'content_type': content_type,
            'valid_types': ['iletisim_bilgisi', 'iletisim_baslik', 'sosyal_medya_url']
        })
        return JsonResponse({'success': False, 'error': 'Geçersiz içerik tipi'})

    return JsonResponse({'success': True})


# Create your views here.

def test(request):
    """Test sayfasını render eder. Bu sayfa tüm UI bileşenlerini gösterir."""
    from django.conf import settings
    from django.http import Http404

    # Sadece DEBUG modunda erişilebilir
    if not settings.DEBUG:
        raise Http404("Bu sayfa sadece geliştirme ortamında erişilebilir.")

    return render(request, 'anasayfa/test.html')

