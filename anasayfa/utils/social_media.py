"""
Sosyal medya platform yönetimi için utility fonksiyonlar.
"""

from typing import Dict, List, Optional, Tuple
from ..models import SosyalMedya


class SocialMediaPlatformManager:
    """
    Sosyal medya platform yönetimi için yardımcı sınıf.
    """
    
    @staticmethod
    def get_platform_choices() -> List[Tuple[str, str]]:
        """
        Model'den platform seçeneklerini döndürür.
        
        Returns:
            List[Tuple[str, str]]: Platform seçenekleri
        """
        return SosyalMedya.PLATFORM_CHOICES
    
    @staticmethod
    def get_platform_mapping() -> Dict[str, str]:
        """
        Platform seçeneklerini sıralı mapping olarak döndürür.
        
        Returns:
            Dict[str, str]: {index: platform_key} mapping'i
        """
        choices = SocialMediaPlatformManager.get_platform_choices()
        return {str(i): choice[0] for i, choice in enumerate(choices)}
    
    @staticmethod
    def get_platform_by_index(index: int) -> Optional[str]:
        """
        Index'e göre platform key'ini döndürür.
        
        Args:
            index: Platform index'i
        
        Returns:
            Platform key veya None
        """
        mapping = SocialMediaPlatformManager.get_platform_mapping()
        return mapping.get(str(index))
    
    @staticmethod
    def get_platform_display_name(platform_key: str) -> Optional[str]:
        """
        Platform key'ine göre görüntüleme adını döndürür.
        
        Args:
            platform_key: Platform key'i
        
        Returns:
            Platform görüntüleme adı veya None
        """
        choices_dict = dict(SocialMediaPlatformManager.get_platform_choices())
        return choices_dict.get(platform_key)
    
    @staticmethod
    def validate_platform(platform_key: str) -> bool:
        """
        Platform key'inin geçerli olup olmadığını kontrol eder.
        
        Args:
            platform_key: Kontrol edilecek platform key'i
        
        Returns:
            bool: Geçerli ise True
        """
        valid_platforms = [choice[0] for choice in SocialMediaPlatformManager.get_platform_choices()]
        return platform_key in valid_platforms
    
    @staticmethod
    def get_platform_info() -> Dict[str, Dict[str, str]]:
        """
        Tüm platform bilgilerini detaylı olarak döndürür.
        
        Returns:
            Dict: Platform bilgileri
        """
        choices = SocialMediaPlatformManager.get_platform_choices()
        mapping = SocialMediaPlatformManager.get_platform_mapping()
        
        platform_info = {}
        
        for index, (platform_key, display_name) in enumerate(choices):
            platform_info[str(index)] = {
                'key': platform_key,
                'display_name': display_name,
                'icon_class': f'bi-{platform_key}',
                'index': index
            }
        
        return platform_info
    
    @staticmethod
    def get_default_platform() -> str:
        """
        Varsayılan platform key'ini döndürür.
        
        Returns:
            str: Varsayılan platform key'i
        """
        choices = SocialMediaPlatformManager.get_platform_choices()
        return choices[0][0] if choices else 'instagram'


# Convenience functions
def get_platform_by_index(index: int) -> Optional[str]:
    """Platform index'ine göre platform key'ini döndürür."""
    return SocialMediaPlatformManager.get_platform_by_index(index)


def validate_platform(platform_key: str) -> bool:
    """Platform key'inin geçerli olup olmadığını kontrol eder."""
    return SocialMediaPlatformManager.validate_platform(platform_key)


def get_platform_mapping() -> Dict[str, str]:
    """Platform mapping'ini döndürür."""
    return SocialMediaPlatformManager.get_platform_mapping()


def get_platform_info() -> Dict[str, Dict[str, str]]:
    """Tüm platform bilgilerini döndürür."""
    return SocialMediaPlatformManager.get_platform_info()
