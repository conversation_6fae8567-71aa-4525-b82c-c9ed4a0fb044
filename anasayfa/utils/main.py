"""
Anasayfa uygulaması için yardımcı fonksiyonlar.
"""

from django.contrib.auth.models import User
from ..models import Hakkimda, IletisimBilgisi, SosyalMedya
from utils.logging import get_logger

logger = get_logger(__name__)


def get_primary_profile_data(current_user=None):
    """
    Birincil profil verilerini döndürür.
    
    Args:
        current_user: Mev<PERSON> kullanıc<PERSON> (eğer yöneticiyse öncelik verilir)
    
    Returns:
        dict: {
            'hakkimda': Hakkimda instance veya None,
            'iletisim': IletisimBilgisi instance veya None,
            'sosyal_medya': QuerySet
        }
    """
    return {
        'hakkimda': Hakkimda.objects.get_primary_or_fallback(current_user=current_user),
        'iletisim': IletisimBilgisi.objects.get_primary_or_fallback(current_user=current_user),
        'sosyal_medya': SosyalMedya.objects.get_primary_user_accounts(current_user=current_user)
    }


def get_default_contact_data():
    """
    Varsayılan iletişim verilerini döndürür.
    
    Returns:
        dict: Varsayılan iletişim bilgileri
    """
    return {
        'email': '<EMAIL>',
        'telefon': '+90 (212) 123 45 67',
        'adres': 'Seramik Atölyesi, Kadıköy, İstanbul',
        'calisma_saatleri': 'Pazartesi - Cumartesi: 10:00 - 18:00'
    }


def get_default_social_media():
    """
    Varsayılan sosyal medya verilerini döndürür.
    
    Returns:
        list: Varsayılan sosyal medya hesapları
    """
    return [
        {'platform': 'instagram', 'url': '#', 'icon': 'bi-instagram'},
        {'platform': 'facebook', 'url': '#', 'icon': 'bi-facebook'},
        {'platform': 'twitter', 'url': '#', 'icon': 'bi-twitter'},
        {'platform': 'youtube', 'url': '#', 'icon': 'bi-youtube'},
        {'platform': 'linkedin', 'url': '#', 'icon': 'bi-linkedin'}
    ]


def merge_contact_data(iletisim_bilgisi, sosyal_medya_hesaplari):
    """
    İletişim bilgilerini ve sosyal medya hesaplarını varsayılan verilerle birleştirir.
    
    Args:
        iletisim_bilgisi: IletisimBilgisi instance veya None
        sosyal_medya_hesaplari: SosyalMedya QuerySet
    
    Returns:
        dict: {
            'iletisim': Birleştirilmiş iletişim bilgileri,
            'sosyal_medya': Birleştirilmiş sosyal medya hesapları
        }
    """
    # Varsayılan verileri al
    default_iletisim = get_default_contact_data()
    default_sosyal_medya = get_default_social_media()
    
    # İletişim bilgileri varsa, varsayılan değerleri güncelle
    if iletisim_bilgisi:
        default_iletisim.update({
            'email': iletisim_bilgisi.email,
            'telefon': iletisim_bilgisi.telefon,
            'adres': iletisim_bilgisi.adres,
            'calisma_saatleri': getattr(iletisim_bilgisi, 'calisma_saatleri', default_iletisim['calisma_saatleri'])
        })
    
    # Sosyal medya hesapları varsa, varsayılan değerleri güncelle
    if sosyal_medya_hesaplari:
        # Sosyal medya hesaplarını platform adına göre sözlük olarak hazırla
        sosyal_medya_dict = {}
        for hesap in sosyal_medya_hesaplari:
            platform = hesap.platform.lower()
            sosyal_medya_dict[platform] = {
                'platform': platform,
                'url': hesap.url,
                'icon': f'bi-{platform}'
            }
        
        # Varsayılan sosyal medya listesini güncelle
        for i, hesap in enumerate(default_sosyal_medya):
            platform = hesap['platform']
            if platform in sosyal_medya_dict:
                default_sosyal_medya[i] = sosyal_medya_dict[platform]
    
    return {
        'iletisim': default_iletisim,
        'sosyal_medya': default_sosyal_medya
    }


def get_user_works(user, limit=4):
    """
    Kullanıcının son çalışmalarını döndürür.
    
    Args:
        user: User instance
        limit: Döndürülecek çalışma sayısı
    
    Returns:
        list: Çalışma ve fotoğraf bilgileri
    """
    son_calismalar = []
    try:
        # calismalar uygulamasından Calisma modelini import et
        from calismalar.models import Calisma, CalismaFotograf
        
        if user:
            calismalar = Calisma.objects.filter(user=user).order_by('-olusturma_tarihi')[:limit]
            
            # Her çalışma için ilk fotoğrafı al
            for calisma in calismalar:
                fotograf = CalismaFotograf.objects.filter(calisma=calisma).first()
                if fotograf:
                    son_calismalar.append({
                        'calisma': calisma,
                        'fotograf': fotograf
                    })
    except Exception as e:
        logger.error("Kullanıcı çalışmaları alınırken hata", extra={
            'user_id': user.id if user else None,
            'error': str(e),
            'limit': limit
        })

    return son_calismalar
