<!DOCTYPE html>
<html lang="tr">

<head>
    {% load static %}
    <!-- Favicons (öncelikli olarak en üstte) -->
    <link rel="icon" href="{% static 'assets/img/favicon/favicon.svg' %}?v=1">
    <link rel="icon" href="{% static 'assets/img/favicon/favicon.ico' %}?v=1">
    <link rel="shortcut icon" href="{% static 'assets/img/favicon/favicon.ico' %}?v=1">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'assets/img/favicon/favicon-32x32.png' %}?v=1">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'assets/img/favicon/favicon-16x16.png' %}?v=1">

    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Test Sayfası - Tüm B<PERSON>şenler</title>
    <meta name="description" content="Küp Cadısı - UI Bileşenleri Test Sayfası">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">

    <!-- CSS Değişkenleri -->
    <style>
        :root {
            /* Sonbahar Renk Paleti */
            --color-navy: #42708C; /* Rocky Mountain Mavi - Ana renk */
            --color-navy-rgb: 66, 112, 140;
            --color-sky: #80A7BF; /* Göl Mavisi - İkincil renk */
            --color-sky-rgb: 128, 167, 191;
            --color-moss: #657351; /* Orman Yeşili - Vurgu rengi */
            --color-moss-rgb: 101, 115, 81;
            --color-brown: #A66F3F; /* Toprak Kahvesi - Aksan rengi */
            --color-brown-rgb: 166, 111, 63;
            --color-sand: #D9A273; /* Kumlu Bej - Hafif vurgu */
            --color-sand-rgb: 217, 162, 115;

            /* Beyaz Tonları */
            --soft-white: #f8f5f0; /* Sayfa arkaplanı - Açık krem */
            --soft-white-rgb: 248, 245, 240;
            --soft-light: #f2ede2; /* Kart arkaplanı - Yumuşak bej */
            --off-white: #f5f0e8; /* Hafif bej tonu */
            --white: #ffffff;

            /* Genel Kullanım - Sonbahar Teması */
            --primary-color: var(--color-brown); /* Ana renk - Toprak Kahvesi */
            --primary-color-rgb: var(--color-brown-rgb);
            --secondary-color: var(--color-navy); /* İkincil renk - Rocky Mountain Mavi */
            --secondary-color-rgb: var(--color-navy-rgb);
            --accent-color: var(--color-moss); /* Vurgu rengi - Orman Yeşili */
            --accent-color-rgb: var(--color-moss-rgb);
            --light-accent: var(--color-sand); /* Hafif vurgu - Kumlu Bej */
            --light-accent-rgb: var(--color-sand-rgb);
            --highlight-color: var(--color-sky); /* Belirginleştirme - Göl Mavisi */
            --highlight-color-rgb: var(--color-sky-rgb);

            /* Metin Renkleri - Sonbahar Teması */
            --text-color: #5a3d2b; /* Koyu kahve - Toprak kahvesinin koyu tonu */
            --text-soft: #6e4c36; /* Ana metin - Orta kahve */
            --text-muted: #8c6952; /* İkincil metin - Açık kahve */

            /* Font Aileleri */
            --heading-font: 'Poppins', sans-serif;
            --body-font: 'Roboto', sans-serif;
        }

        body {
            font-family: var(--body-font);
            color: var(--text-soft); /* Ana metin rengi - Orta kahve */
            background-color: var(--soft-white); /* Modern tema - Açık krem */
            position: relative;
            background-image: linear-gradient(135deg,
                              rgba(var(--color-sand-rgb), 0.1) 0%,
                              rgba(var(--color-brown-rgb), 0.05) 50%,
                              rgba(var(--color-moss-rgb), 0.08) 100%);
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('{% static "assets/img/pattern.svg" %}');
            background-repeat: repeat;
            background-size: 200px;
            opacity: 0.03;
            z-index: -1;
            pointer-events: none;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: var(--heading-font);
            color: var(--color-brown); /* Başlık rengi - Toprak Kahvesi */
            letter-spacing: -0.02em;
            font-weight: 600;
            line-height: 1.3;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(to right,
                        rgba(var(--color-brown-rgb), 1),
                        rgba(var(--color-brown-rgb), 0.3));
            border-radius: 3px;
        }

        h2 {
            font-size: 2rem;
            margin-bottom: 1.2rem;
            position: relative;
            display: inline-block;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: -0.4rem;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(to right,
                        rgba(var(--color-brown-rgb), 1),
                        rgba(var(--color-brown-rgb), 0.3));
            border-radius: 2px;
        }

        .text-center h1::after,
        .text-center h2::after {
            left: 50%;
            transform: translateX(-50%);
        }

        p {
            line-height: 1.7;
            margin-bottom: 1rem;
        }

        /* Bootstrap renk değişkenleri override */
        :root {
            --bs-primary: var(--color-brown);
            --bs-primary-rgb: var(--color-brown-rgb);
            --bs-secondary: var(--color-navy);
            --bs-secondary-rgb: var(--color-navy-rgb);
            --bs-success: var(--color-moss);
            --bs-success-rgb: var(--color-moss-rgb);
            --bs-info: var(--color-sky);
            --bs-info-rgb: var(--color-sky-rgb);
            --bs-warning: var(--color-sand);
            --bs-warning-rgb: var(--color-sand-rgb);
            --bs-danger: #dc3545;
            --bs-danger-rgb: 220, 53, 69;
            --bs-light: var(--soft-light);
            --bs-light-rgb: var(--soft-light-rgb);
            --bs-dark: var(--color-navy);
            --bs-dark-rgb: var(--color-navy-rgb);
        }

        /* Bootstrap bileşen renkleri override */
        .bg-primary {
            background-color: var(--color-brown) !important;
        }

        .bg-secondary {
            background-color: var(--color-navy) !important; /* Rocky Mountain Mavi */
        }

        .bg-success {
            background-color: var(--color-moss) !important;
        }

        .bg-info {
            background-color: var(--color-sky) !important; /* Göl Mavisi */
        }

        .bg-warning {
            background-color: var(--color-sand) !important;
        }

        .bg-dark {
            background-color: var(--color-navy) !important; /* Rocky Mountain Mavi */
        }

        .text-primary {
            color: var(--color-brown) !important;
        }

        .text-secondary {
            color: var(--color-navy) !important;
        }

        .text-success {
            color: var(--color-moss) !important;
        }

        .text-info {
            color: var(--color-sky) !important;
        }

        .text-warning {
            color: var(--color-sand) !important;
        }

        .text-dark {
            color: var(--color-navy) !important;
        }

        .btn-primary {
            background-color: var(--color-brown) !important;
            border-color: var(--color-brown) !important;
        }

        .btn-primary:hover, .btn-primary:focus, .btn-primary:active {
            background-color: rgba(var(--color-brown-rgb), 0.9) !important;
            border-color: var(--color-brown) !important;
        }

        .btn-secondary {
            background-color: var(--color-navy) !important;
            border-color: var(--color-navy) !important;
        }

        .btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
            background-color: rgba(var(--color-navy-rgb), 0.9) !important;
            border-color: var(--color-navy) !important;
        }

        .btn-success {
            background-color: var(--color-moss) !important;
            border-color: var(--color-moss) !important;
        }

        .btn-success:hover, .btn-success:focus, .btn-success:active {
            background-color: rgba(var(--color-moss-rgb), 0.9) !important;
            border-color: var(--color-moss) !important;
        }

        .btn-info {
            background-color: var(--color-sky) !important;
            border-color: var(--color-sky) !important;
            color: white !important;
        }

        .btn-info:hover, .btn-info:focus, .btn-info:active {
            background-color: rgba(var(--color-sky-rgb), 0.9) !important;
            border-color: var(--color-sky) !important;
        }

        .btn-warning {
            background-color: var(--color-sand) !important;
            border-color: var(--color-sand) !important;
            color: white !important;
        }

        .btn-warning:hover, .btn-warning:focus, .btn-warning:active {
            background-color: rgba(var(--color-sand-rgb), 0.9) !important;
            border-color: var(--color-sand) !important;
        }

        .btn-dark {
            background-color: var(--color-navy) !important;
            border-color: var(--color-navy) !important;
        }

        .btn-dark:hover, .btn-dark:focus, .btn-dark:active {
            background-color: rgba(var(--color-navy-rgb), 0.9) !important;
            border-color: var(--color-navy) !important;
        }

        .btn-outline-primary {
            color: var(--color-brown) !important;
            border-color: var(--color-brown) !important;
        }

        .btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
            background-color: var(--color-brown) !important;
            color: white !important;
        }

        .btn-outline-secondary {
            color: var(--color-navy) !important;
            border-color: var(--color-navy) !important;
        }

        .btn-outline-secondary:hover, .btn-outline-secondary:focus, .btn-outline-secondary:active {
            background-color: var(--color-navy) !important;
            color: white !important;
        }

        .btn-outline-success {
            color: var(--color-moss) !important;
            border-color: var(--color-moss) !important;
        }

        .btn-outline-success:hover, .btn-outline-success:focus, .btn-outline-success:active {
            background-color: var(--color-moss) !important;
            color: white !important;
        }

        .btn-outline-info {
            color: var(--color-sky) !important;
            border-color: var(--color-sky) !important;
        }

        .btn-outline-info:hover, .btn-outline-info:focus, .btn-outline-info:active {
            background-color: var(--color-sky) !important;
            color: white !important;
        }

        .btn-outline-warning {
            color: var(--color-sand) !important;
            border-color: var(--color-sand) !important;
        }

        .btn-outline-warning:hover, .btn-outline-warning:focus, .btn-outline-warning:active {
            background-color: var(--color-sand) !important;
            color: white !important;
        }

        .btn-outline-dark {
            color: var(--color-navy) !important;
            border-color: var(--color-navy) !important;
        }

        .btn-outline-dark:hover, .btn-outline-dark:focus, .btn-outline-dark:active {
            background-color: var(--color-navy) !important;
            color: white !important;
        }

        /* Swiper navigasyon ve pagination renkleri */
        .swiper-button-next, .swiper-button-prev {
            color: var(--color-brown) !important;
        }

        .swiper-pagination-bullet-active {
            background-color: var(--color-brown) !important;
        }

        /* Sonbahar teması için özel stiller */
        .modern-section {
            border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
            padding: 3rem 0;
        }

        .modern-card {
            background-color: var(--soft-light);
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid rgba(var(--color-brown-rgb), 0.1);
            box-shadow: 0 10px 30px rgba(var(--color-brown-rgb), 0.1),
                        0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            height: 100%;
            margin-bottom: 1rem;
            position: relative;
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15),
                        0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .modern-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 16px;
            box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.2);
            opacity: 0;
            transition: opacity 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            pointer-events: none;
        }

        .modern-card:hover::after {
            opacity: 1;
        }

        /* Mavi kartlar */
        .modern-card-navy {
            border: 1px solid rgba(var(--color-navy-rgb), 0.1);
            box-shadow: 0 10px 30px rgba(var(--color-navy-rgb), 0.1),
                        0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .modern-card-navy:hover {
            box-shadow: 0 15px 35px rgba(var(--color-navy-rgb), 0.15),
                        0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .modern-card-navy::after {
            box-shadow: 0 5px 15px rgba(var(--color-navy-rgb), 0.2);
        }

        .modern-card-sky {
            border: 1px solid rgba(var(--color-sky-rgb), 0.1);
            box-shadow: 0 10px 30px rgba(var(--color-sky-rgb), 0.1),
                        0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .modern-card-sky:hover {
            box-shadow: 0 15px 35px rgba(var(--color-sky-rgb), 0.15),
                        0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .modern-card-sky::after {
            box-shadow: 0 5px 15px rgba(var(--color-sky-rgb), 0.2);
        }

        .modern-card-gradient {
            background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-sky) 100%);
            border: none;
            color: white;
            box-shadow: 0 10px 30px rgba(var(--color-navy-rgb), 0.2),
                        0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .modern-card-gradient:hover {
            box-shadow: 0 15px 35px rgba(var(--color-navy-rgb), 0.25),
                        0 8px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-8px);
        }

        .modern-card-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
            position: relative;
        }

        .modern-card-body {
            padding: 1.5rem;
        }

        .modern-card-image {
            position: relative;
            overflow: hidden;
        }

        .modern-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            transition: all 0.5s ease;
        }

        .modern-card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(var(--color-brown-rgb), 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .modern-card:hover .modern-card-overlay {
            opacity: 1;
        }

        .modern-badge-corner {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--color-brown);
            color: var(--soft-white);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .modern-btn {
            display: inline-block;
            background: var(--soft-white);
            color: var(--color-brown);
            border: 2px solid var(--color-brown);
            padding: 0.75rem 2rem;
            border-radius: 30px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.2),
                        0 2px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            z-index: 1;
            text-decoration: none;
        }

        .modern-btn:hover {
            background: var(--color-brown);
            color: var(--soft-white);
            border-color: var(--color-brown);
            box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.4),
                        0 4px 10px rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
            text-decoration: none;
        }

        .modern-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transform: scale(2);
            transition: opacity 0.4s ease, transform 0.6s ease;
            pointer-events: none;
        }

        .modern-btn:hover::after {
            opacity: 0.15;
            transform: scale(1);
        }

        .modern-btn-success {
            background: var(--color-moss);
            color: var(--soft-white);
            border: 2px solid var(--color-moss);
            box-shadow: 0 4px 12px rgba(var(--color-moss-rgb), 0.3),
                        0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .modern-btn-success:hover {
            background: var(--soft-white);
            color: var(--color-moss);
            border-color: var(--color-moss);
            box-shadow: 0 8px 25px rgba(var(--color-moss-rgb), 0.4),
                        0 4px 10px rgba(0, 0, 0, 0.15);
        }

        .modern-btn-outline {
            background: transparent;
            color: var(--color-brown);
            border: 2px solid var(--color-brown);
        }

        .modern-btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Mavi butonlar */
        .modern-btn-navy {
            background: var(--soft-white);
            color: var(--color-navy);
            border: 2px solid var(--color-navy);
            box-shadow: 0 4px 12px rgba(var(--color-navy-rgb), 0.3),
                        0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .modern-btn-navy:hover {
            background: var(--color-navy);
            color: var(--soft-white);
            border-color: var(--color-navy);
            box-shadow: 0 8px 25px rgba(var(--color-navy-rgb), 0.4),
                        0 4px 10px rgba(0, 0, 0, 0.15);
        }

        .modern-btn-sky {
            background: var(--soft-white);
            color: var(--color-sky);
            border: 2px solid var(--color-sky);
            box-shadow: 0 4px 12px rgba(var(--color-sky-rgb), 0.3),
                        0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .modern-btn-sky:hover {
            background: var(--color-sky);
            color: var(--soft-white);
            border-color: var(--color-sky);
            box-shadow: 0 8px 25px rgba(var(--color-sky-rgb), 0.4),
                        0 4px 10px rgba(0, 0, 0, 0.15);
        }

        .modern-btn-circle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--color-brown);
            color: var(--soft-white);
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 10px rgba(var(--color-brown-rgb), 0.3),
                        0 2px 5px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .modern-btn-circle:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 20px rgba(var(--color-brown-rgb), 0.4),
                        0 4px 10px rgba(0, 0, 0, 0.15);
            background: var(--color-moss);
            color: var(--soft-white);
            text-decoration: none;
        }

        .modern-btn-circle::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transform: scale(2);
            transition: opacity 0.4s ease, transform 0.6s ease;
            pointer-events: none;
        }

        .modern-btn-circle:hover::after {
            opacity: 0.2;
            transform: scale(1);
        }

        .modern-icon-box {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: rgba(var(--color-brown-rgb), 0.1);
            color: var(--color-brown);
        }

        /* Mavi icon box */
        .modern-icon-box-navy {
            background: rgba(var(--color-navy-rgb), 0.1);
            color: var(--color-navy);
        }

        .modern-icon-box-sky {
            background: rgba(var(--color-sky-rgb), 0.1);
            color: var(--color-sky);
        }

        .filter-btn {
            padding: 0.7rem 1.5rem;
            background: var(--soft-white);
            color: var(--text-soft);
            border: none;
            border-radius: 50px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(var(--color-brown-rgb), 0.05);
        }

        .filter-btn:hover {
            background: rgba(var(--color-brown-rgb), 0.1);
            transform: translateY(-3px);
        }

        .filter-btn.active {
            background: var(--color-brown);
            color: var(--soft-white);
        }

        /* Mavi filtre butonları */
        .filter-btn-navy {
            background: rgba(var(--color-navy-rgb), 0.05);
            color: var(--color-navy);
            box-shadow: 0 3px 10px rgba(var(--color-navy-rgb), 0.1);
        }

        .filter-btn-navy:hover {
            background: rgba(var(--color-navy-rgb), 0.15);
        }

        .filter-btn-navy.active {
            background: var(--color-navy);
            color: var(--soft-white);
            box-shadow: 0 4px 15px rgba(var(--color-navy-rgb), 0.2);
        }

        .modern-logo {
            color: var(--color-brown);
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            font-family: var(--heading-font);
        }

        .modern-nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .modern-nav ul li {
            margin: 0 15px;
        }

        .modern-nav ul li a {
            color: var(--text-soft);
            font-weight: 500;
            text-decoration: none;
            padding: 10px 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .modern-nav ul li a:hover,
        .modern-nav ul li a.active {
            color: var(--color-brown);
            text-decoration: none;
        }

        .modern-nav ul li a::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--color-brown);
            transition: all 0.3s ease;
        }

        .modern-nav ul li a:hover::after,
        .modern-nav ul li a.active::after {
            width: 100%;
        }

        .modern-footer {
            background-color: rgba(var(--color-brown-rgb), 0.08);
            background-image: linear-gradient(to bottom,
                              rgba(var(--color-brown-rgb), 0.05) 0%,
                              rgba(var(--color-brown-rgb), 0.12) 100%);
            padding: 3rem 0 1rem;
            border-top: 1px solid rgba(var(--color-brown-rgb), 0.15);
            box-shadow: inset 0 15px 15px -15px rgba(var(--color-brown-rgb), 0.1);
            position: relative;
            overflow: hidden;
        }

        .modern-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('{% static "assets/img/pattern.svg" %}');
            background-repeat: repeat;
            background-size: 200px;
            opacity: 0.03;
            z-index: 0;
            pointer-events: none;
        }

        .modern-footer-title {
            color: var(--color-brown);
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .modern-footer-text {
            color: var(--text-soft);
            margin-bottom: 1rem;
        }

        .modern-footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .modern-footer-links li {
            margin-bottom: 0.5rem;
        }

        .modern-footer-links li a {
            color: var(--color-moss);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .modern-footer-links li a:hover {
            color: var(--color-brown);
            text-decoration: none;
        }

        .modern-social-links {
            display: flex;
            gap: 10px;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--color-brown);
            color: var(--soft-white);
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 10px rgba(var(--color-brown-rgb), 0.2),
                        0 2px 5px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .social-link:hover {
            background-color: var(--color-moss);
            transform: translateY(-5px) scale(1.1);
            text-decoration: none;
            color: var(--soft-white);
            box-shadow: 0 8px 20px rgba(var(--color-moss-rgb), 0.3),
                        0 4px 10px rgba(0, 0, 0, 0.15);
        }

        .social-link::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transform: scale(2);
            transition: opacity 0.4s ease, transform 0.6s ease;
            pointer-events: none;
        }

        .social-link:hover::after {
            opacity: 0.2;
            transform: scale(1);
        }

        .modern-footer-bottom {
            border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
            margin-top: 2rem;
            padding-top: 1.5rem;
        }

        .scroll-top {
            position: fixed;
            right: 20px;
            bottom: 20px;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background-color: var(--color-brown);
            color: var(--soft-white);
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.3),
                        0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 99;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(15px);
            overflow: hidden;
        }

        .scroll-top.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .scroll-top:hover {
            background-color: var(--color-moss);
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 8px 25px rgba(var(--color-moss-rgb), 0.4),
                        0 4px 12px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            color: var(--soft-white);
        }

        .scroll-top::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transform: scale(2);
            transition: opacity 0.4s ease, transform 0.6s ease;
            pointer-events: none;
        }

        .scroll-top:hover::after {
            opacity: 0.2;
            transform: scale(1);
        }

        .modern-link {
            color: var(--color-brown);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-bottom: 1px dashed var(--color-brown);
            padding-bottom: 2px;
        }

        .modern-link:hover {
            color: var(--color-moss);
            border-color: var(--color-moss);
            text-decoration: none;
        }

        /* Mavi Tema Bölümü */
        .blue-section {
            background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-sky) 100%);
            color: white;
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(var(--color-navy-rgb), 0.2),
                        0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .blue-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('{% static "assets/img/pattern.svg" %}');
            background-repeat: repeat;
            background-size: 200px;
            opacity: 0.05;
            z-index: 0;
        }

        .blue-section .container {
            position: relative;
            z-index: 1;
        }

        .feature-box {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            height: 100%;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            background-color: rgba(255, 255, 255, 0.15);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .feature-box h4 {
            color: white;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .feature-box p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        .blue-btn {
            background-color: white;
            color: var(--color-navy);
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .blue-btn:hover {
            background-color: rgba(255, 255, 255, 0.9);
            color: var(--color-navy);
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .blue-card {
            background-color: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1),
                        0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .blue-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15),
                        0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .blue-card-header {
            background: linear-gradient(to right, var(--color-navy), var(--color-sky));
            color: white;
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .blue-card-header h3 {
            color: white;
            margin: 0;
            font-size: 1.5rem;
        }

        .blue-card-body {
            padding: 1.5rem;
        }

        .blue-progress-container {
            margin-top: 1.5rem;
        }

        .blue-progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .blue-progress {
            height: 8px;
            background-color: rgba(var(--color-navy-rgb), 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .blue-progress-bar {
            height: 100%;
            background: linear-gradient(to right, var(--color-navy), var(--color-sky));
            border-radius: 4px;
            transition: width 1.5s cubic-bezier(0.1, 0.5, 0.2, 1);
        }

        /* Animasyonlu Bileşenler */

        /* Sayaç */
        .counter-box {
            font-size: 3rem;
            font-weight: 700;
            color: var(--color-brown);
            line-height: 1;
            display: inline-block;
            position: relative;
        }

        .counter-box::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background: linear-gradient(to right,
                        rgba(var(--color-brown-rgb), 1),
                        rgba(var(--color-brown-rgb), 0.3));
            border-radius: 3px;
        }

        .counter-number {
            display: inline-block;
        }

        .counter-suffix {
            font-size: 2.5rem;
            color: var(--color-brown);
            opacity: 0.8;
        }

        /* Yetenek Çubukları */
        .skill-item {
            margin-bottom: 1.5rem;
        }

        .skill-name {
            font-weight: 600;
            color: var(--text-color);
        }

        .skill-percentage {
            font-weight: 600;
            color: var(--color-brown);
        }

        .skill-bar {
            height: 8px;
            background-color: rgba(var(--color-brown-rgb), 0.1);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .skill-progress {
            height: 100%;
            background: linear-gradient(to right, var(--color-brown), var(--color-sand));
            border-radius: 4px;
            width: 0;
            transition: width 1.5s cubic-bezier(0.1, 0.5, 0.2, 1);
            position: relative;
        }

        /* Zaman Çizelgesi */
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 7px;
            height: 100%;
            width: 2px;
            background: linear-gradient(to bottom,
                        var(--color-brown),
                        rgba(var(--color-brown-rgb), 0.3));
        }

        .timeline-item {
            position: relative;
            padding-bottom: 25px;
        }

        .timeline-item:last-child {
            padding-bottom: 0;
        }

        .timeline-dot {
            position: absolute;
            left: -30px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--color-brown);
            border: 3px solid var(--soft-white);
            box-shadow: 0 0 0 3px rgba(var(--color-brown-rgb), 0.2);
            z-index: 1;
        }

        .timeline-content {
            padding-left: 10px;
            position: relative;
        }

        .timeline-content h4 {
            color: var(--color-brown);
            margin-bottom: 5px;
            font-weight: 600;
        }
    </style>

    <!-- Vendor CSS Files -->
    <link href="{% static 'assets/vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/bootstrap-icons/bootstrap-icons.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/aos/aos.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/glightbox/css/glightbox.min.css' %}" rel="stylesheet">

    <!-- Test Page CSS -->
    <link href="{% static 'assets/css/test-page.css' %}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
</head>

<body class="index-page">

    <header id="header" class="header modern-header sticky-top">
        <div class="container-fluid position-relative d-flex align-items-center justify-content-between">
            <a href="{% url 'anasayfa:ana_sayfa' %}" class="logo d-flex align-items-center me-auto me-xl-0">
                <h1 class="modern-logo">KüpCadısı</h1>
            </a>

            <nav id="navmenu" class="modern-nav">
                <ul>
                    <li><a href="{% url 'anasayfa:ana_sayfa' %}">Ana Sayfa</a></li>
                    <li><a href="{% url 'anasayfa:test' %}" class="active">Test Sayfası</a></li>
                </ul>
                <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
            </nav>
        </div>
    </header>

    <style>
        /* Header için özel stiller */
        .modern-header {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.1);
            border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.08);
            transition: all 0.4s ease;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .modern-header.scrolled {
            background-color: rgba(255, 255, 255, 0.98);
            box-shadow: 0 6px 25px rgba(var(--color-brown-rgb), 0.15);
        }

        .modern-logo {
            text-shadow: 2px 2px 4px rgba(var(--color-brown-rgb), 0.2);
            position: relative;
            display: inline-block;
        }

        .modern-logo::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right,
                        rgba(var(--color-brown-rgb), 0.7),
                        rgba(var(--color-brown-rgb), 0.3) 70%,
                        rgba(var(--color-brown-rgb), 0));
            border-radius: 2px;
        }
    </style>

    <script>
        // Header scroll efekti
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('.modern-header');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
        });
    </script>

    <main id="main">
        <!-- Başlık Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <div class="modern-header text-center">
                    <h1 class="display-4 fw-bold">UI Bileşenleri Test Sayfası</h1>
                    <p class="lead">Bu sayfa, projede kullanılan tüm UI bileşenlerini göstermek için oluşturulmuştur.</p>
                </div>
            </div>
        </section>

        <!-- Slider Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <h2 class="mb-4">Slider Bileşeni</h2>
                <div class="modern-card overflow-hidden mb-5">
                    <div class="swiper test-slider">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="position-relative test-slider-container">
                                    <img src="{% static 'assets/img/hero-bg.jpg' %}" alt="Slider Görsel 1" class="w-100 h-100 object-fit-cover" />
                                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white p-4 test-slider-overlay">
                                            <h3>Slider Başlık 1</h3>
                                            <p>Slider açıklama metni burada yer alacak.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="position-relative test-slider-container">
                                    <img src="{% static 'assets/img/hero-bg.jpg' %}" alt="Slider Görsel 2" class="w-100 h-100 object-fit-cover" />
                                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white p-4 test-slider-overlay">
                                            <h3>Slider Başlık 2</h3>
                                            <p>Slider açıklama metni burada yer alacak.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="position-relative test-slider-container">
                                    <img src="{% static 'assets/img/hero-bg.jpg' %}" alt="Slider Görsel 3" class="w-100 h-100 object-fit-cover" />
                                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center text-white p-4 test-slider-overlay">
                                            <h3>Slider Başlık 3</h3>
                                            <p>Slider açıklama metni burada yer alacak.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Kartlar Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <h2 class="mb-4">Kart Bileşenleri</h2>
                <div class="row g-4">
                    <!-- Standart Kart -->
                    <div class="col-md-3">
                        <div class="modern-card h-100">
                            <div class="modern-card-header">
                                <h3 class="fs-5">Standart Kart</h3>
                            </div>
                            <div class="modern-card-body">
                                <p>Bu bir standart kart bileşenidir. İçerik burada yer alır.</p>
                                <a href="#" class="modern-btn">Detaylar</a>
                            </div>
                        </div>
                    </div>

                    <!-- Resimli Kart -->
                    <div class="col-md-3">
                        <div class="modern-card h-100">
                            <div class="modern-card-image">
                                <img src="{% static 'assets/img/hero-bg.jpg' %}" alt="Kart Resmi" class="modern-image">
                                <div class="modern-card-overlay">
                                    <a href="#" class="modern-btn-circle">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </div>
                                <span class="modern-badge-corner">Kategori</span>
                            </div>
                            <div class="modern-card-body">
                                <h3 class="fs-5">Resimli Kart</h3>
                                <p>Bu bir resimli kart bileşenidir. İçerik burada yer alır.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Özellikli Kart -->
                    <div class="col-md-3">
                        <div class="modern-card h-100">
                            <div class="modern-card-header" style="background-color: var(--color-brown); color: var(--soft-white);">
                                <h3 class="fs-5" style="color: var(--soft-white);">Özellikli Kart</h3>
                            </div>
                            <div class="modern-card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="modern-icon-box me-3">
                                        <i class="bi bi-star-fill"></i>
                                    </div>
                                    <div>
                                        <h4 class="fs-6 mb-0">Özellik Başlığı</h4>
                                        <p class="small mb-0">Özellik açıklaması</p>
                                    </div>
                                </div>
                                <p>Bu bir özellikli kart bileşenidir. İçerik burada yer alır.</p>
                                <a href="#" class="modern-btn modern-btn-success">Detaylar</a>
                            </div>
                        </div>
                    </div>

                    <!-- Mavi Kart -->
                    <div class="col-md-3">
                        <div class="modern-card modern-card-navy h-100">
                            <div class="modern-card-header" style="background-color: var(--color-navy); color: var(--soft-white);">
                                <h3 class="fs-5" style="color: var(--soft-white);">Mavi Kart</h3>
                            </div>
                            <div class="modern-card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="modern-icon-box modern-icon-box-navy me-3">
                                        <i class="bi bi-water"></i>
                                    </div>
                                    <div>
                                        <h4 class="fs-6 mb-0">Mavi Tema</h4>
                                        <p class="small mb-0">Rocky Mountain Mavi</p>
                                    </div>
                                </div>
                                <p>Bu bir mavi kart bileşenidir. Rocky Mountain Mavi (#42708C) rengi kullanılmıştır.</p>
                                <a href="#" class="modern-btn modern-btn-navy">Detaylar</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- İkinci Sıra Kartlar -->
                <div class="row g-4 mt-4">
                    <!-- Açık Mavi Kart -->
                    <div class="col-md-6">
                        <div class="modern-card modern-card-sky h-100">
                            <div class="modern-card-header" style="background-color: var(--color-sky); color: var(--soft-white);">
                                <h3 class="fs-5" style="color: var(--soft-white);">Açık Mavi Kart</h3>
                            </div>
                            <div class="modern-card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="modern-icon-box modern-icon-box-sky me-3">
                                        <i class="bi bi-cloud"></i>
                                    </div>
                                    <div>
                                        <h4 class="fs-6 mb-0">Göl Mavisi</h4>
                                        <p class="small mb-0">Açık Mavi Tema</p>
                                    </div>
                                </div>
                                <p>Bu bir açık mavi kart bileşenidir. Göl Mavisi (#80A7BF) rengi kullanılmıştır.</p>
                                <div class="d-flex gap-2">
                                    <a href="#" class="modern-btn modern-btn-sky">Detaylar</a>
                                    <a href="#" class="modern-btn-circle" style="background-color: var(--color-sky);">
                                        <i class="bi bi-info"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gradyan Kart -->
                    <div class="col-md-6">
                        <div class="modern-card modern-card-gradient h-100">
                            <div class="modern-card-body p-5">
                                <h3 class="fs-4 mb-4 text-white">Mavi Gradyan Kart</h3>
                                <p class="text-white mb-4">Bu kartta Rocky Mountain Mavi ve Göl Mavisi renkleri arasında gradyan kullanılmıştır.</p>
                                <div class="d-flex gap-2">
                                    <a href="#" class="modern-btn" style="background-color: white; color: var(--color-navy); border-color: white;">
                                        Detaylar
                                    </a>
                                    <a href="#" class="modern-btn-circle" style="background-color: white; color: var(--color-navy);">
                                        <i class="bi bi-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Butonlar Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <h2 class="mb-4">Buton Bileşenleri</h2>
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="modern-card">
                            <div class="modern-card-header">
                                <h3 class="fs-5">Standart Butonlar</h3>
                            </div>
                            <div class="modern-card-body">
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <a href="#" class="modern-btn">Standart Buton</a>
                                    <a href="#" class="modern-btn modern-btn-success">Başarı Butonu</a>
                                    <a href="#" class="modern-btn modern-btn-outline">Outline Buton</a>
                                    <a href="#" class="modern-btn modern-btn-sm">Küçük Buton</a>
                                </div>
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="#" class="modern-btn-circle">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="#" class="modern-btn-circle">
                                        <i class="bi bi-heart"></i>
                                    </a>
                                    <a href="#" class="modern-btn-circle">
                                        <i class="bi bi-share"></i>
                                    </a>
                                </div>

                                <div class="mt-4">
                                    <h4 class="fs-6 mb-3">Mavi Butonlar</h4>
                                    <div class="d-flex flex-wrap gap-2">
                                        <a href="#" class="modern-btn modern-btn-navy">Mavi Buton</a>
                                        <a href="#" class="modern-btn modern-btn-sky">Açık Mavi</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="modern-card">
                            <div class="modern-card-header">
                                <h3 class="fs-5">Filtre Butonları</h3>
                            </div>
                            <div class="modern-card-body">
                                <div class="d-flex flex-wrap gap-2 mb-4">
                                    <button class="filter-btn active">Tümü</button>
                                    <button class="filter-btn">Kategori 1</button>
                                    <button class="filter-btn">Kategori 2</button>
                                    <button class="filter-btn">Kategori 3</button>
                                </div>

                                <h4 class="fs-6 mb-3">Mavi Filtreler</h4>
                                <div class="d-flex flex-wrap gap-2">
                                    <button class="filter-btn filter-btn-navy active">Tümü</button>
                                    <button class="filter-btn filter-btn-navy">Kategori 1</button>
                                    <button class="filter-btn filter-btn-navy">Kategori 2</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Animasyonlu Bileşenler Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <h2 class="mb-4">Animasyonlu Bileşenler</h2>
                <div class="row g-4 mb-5">
                    <!-- Sayaç Kartı -->
                    <div class="col-md-4">
                        <div class="modern-card h-100">
                            <div class="modern-card-body text-center p-5">
                                <div class="counter-box mb-3">
                                    <span class="counter-number" data-target="250">0</span>
                                    <span class="counter-suffix">+</span>
                                </div>
                                <h3 class="fs-5">Tamamlanan Proje</h3>
                                <p>Bugüne kadar tamamladığımız başarılı projeler.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Yetenek Çubuğu -->
                    <div class="col-md-4">
                        <div class="modern-card h-100">
                            <div class="modern-card-body p-4">
                                <h3 class="fs-5 mb-4">Yeteneklerimiz</h3>

                                <div class="skill-item mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="skill-name">İç Mimari</span>
                                        <span class="skill-percentage">90%</span>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-width="90"></div>
                                    </div>
                                </div>

                                <div class="skill-item mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="skill-name">Tasarım</span>
                                        <span class="skill-percentage">85%</span>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-width="85"></div>
                                    </div>
                                </div>

                                <div class="skill-item">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span class="skill-name">Danışmanlık</span>
                                        <span class="skill-percentage">95%</span>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-progress" data-width="95"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Zaman Çizelgesi -->
                    <div class="col-md-4">
                        <div class="modern-card h-100">
                            <div class="modern-card-body p-4">
                                <h3 class="fs-5 mb-4">Proje Süreci</h3>

                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-dot"></div>
                                        <div class="timeline-content">
                                            <h4 class="fs-6">Planlama</h4>
                                            <p class="small mb-0">Proje gereksinimlerinin belirlenmesi</p>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-dot"></div>
                                        <div class="timeline-content">
                                            <h4 class="fs-6">Tasarım</h4>
                                            <p class="small mb-0">Konsept ve tasarım çalışmaları</p>
                                        </div>
                                    </div>

                                    <div class="timeline-item">
                                        <div class="timeline-dot"></div>
                                        <div class="timeline-content">
                                            <h4 class="fs-6">Uygulama</h4>
                                            <p class="small mb-0">Tasarımın hayata geçirilmesi</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mavi Tema Bölümü -->
        <section class="modern-section py-5 blue-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h2 class="text-white mb-4">Mavi Tema Örnekleri</h2>
                        <p class="text-white mb-4">Bu bölümde Rocky Mountain Mavi (#42708C) ve Göl Mavisi (#80A7BF) renklerini kullanarak oluşturulmuş bir tasarım örneği görebilirsiniz.</p>

                        <div class="row g-4 mb-4">
                            <div class="col-6">
                                <div class="feature-box">
                                    <div class="feature-icon">
                                        <i class="bi bi-droplet-fill"></i>
                                    </div>
                                    <h4>Su Teması</h4>
                                    <p>Mavi tonlar su ve gökyüzü temalarında mükemmel çalışır.</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-box">
                                    <div class="feature-icon">
                                        <i class="bi bi-cloud-fill"></i>
                                    </div>
                                    <h4>Gökyüzü</h4>
                                    <p>Açık ve koyu mavi tonlar gökyüzü temasını yansıtır.</p>
                                </div>
                            </div>
                        </div>

                        <a href="#" class="modern-btn blue-btn">Daha Fazla</a>
                    </div>
                    <div class="col-md-6">
                        <div class="blue-card">
                            <div class="blue-card-header">
                                <h3>Mavi Kart Örneği</h3>
                            </div>
                            <div class="blue-card-body">
                                <p>Bu kart, mavi renk temasını kullanarak oluşturulmuştur. Mavi tonlar, sakinlik ve güven duygusunu yansıtır.</p>
                                <div class="blue-progress-container">
                                    <div class="blue-progress-label">
                                        <span>Tasarım Tamamlandı</span>
                                        <span>75%</span>
                                    </div>
                                    <div class="blue-progress">
                                        <div class="blue-progress-bar" style="width: 75%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bootstrap Butonlar Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <h2 class="mb-4">Bootstrap Butonları</h2>
                <div class="modern-card">
                    <div class="modern-card-body">
                        <h4 class="mb-3">Normal Butonlar</h4>
                        <div class="d-flex flex-wrap gap-2 mb-4">
                            <button class="btn btn-primary">Primary</button>
                            <button class="btn btn-secondary">Secondary</button>
                            <button class="btn btn-success">Success</button>
                            <button class="btn btn-info">Info</button>
                            <button class="btn btn-warning">Warning</button>
                            <button class="btn btn-danger">Danger</button>
                            <button class="btn btn-dark">Dark</button>
                            <button class="btn btn-light">Light</button>
                        </div>

                        <h4 class="mb-3">Outline Butonlar</h4>
                        <div class="d-flex flex-wrap gap-2 mb-4">
                            <button class="btn btn-outline-primary">Primary</button>
                            <button class="btn btn-outline-secondary">Secondary</button>
                            <button class="btn btn-outline-success">Success</button>
                            <button class="btn btn-outline-info">Info</button>
                            <button class="btn btn-outline-warning">Warning</button>
                            <button class="btn btn-outline-danger">Danger</button>
                            <button class="btn btn-outline-dark">Dark</button>
                        </div>

                        <h4 class="mb-3">Buton Boyutları</h4>
                        <div class="d-flex flex-wrap gap-2 align-items-center mb-4">
                            <button class="btn btn-primary btn-lg">Büyük Buton</button>
                            <button class="btn btn-primary">Normal Buton</button>
                            <button class="btn btn-primary btn-sm">Küçük Buton</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Yazı Stilleri Bölümü -->
        <section class="modern-section py-5">
            <div class="container">
                <h2 class="mb-4">Yazı Stilleri</h2>
                <div class="modern-card">
                    <div class="modern-card-body">
                        <h1>H1 Başlık</h1>
                        <h2>H2 Başlık</h2>
                        <h3>H3 Başlık</h3>
                        <h4>H4 Başlık</h4>
                        <h5>H5 Başlık</h5>
                        <h6>H6 Başlık</h6>
                        <p class="lead">Lead paragraf stili. Daha büyük ve dikkat çekici.</p>
                        <p>Normal paragraf stili. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                        <p class="text-muted">Soluk metin stili. Lorem ipsum dolor sit amet.</p>
                        <p><a href="#" class="modern-link">Modern Link Stili</a></p>

                        <h4 class="mt-4 mb-3">Metin Renkleri</h4>
                        <p class="text-primary">Primary renk metni (Orman Yeşili)</p>
                        <p class="text-secondary">Secondary renk metni (Rocky Mountain Mavi)</p>
                        <p class="text-success">Success renk metni (Orman Yeşili)</p>
                        <p class="text-info">Info renk metni (Göl Mavisi)</p>
                        <p class="text-warning">Warning renk metni (Kumlu Bej)</p>
                        <p class="text-danger">Danger renk metni</p>
                        <p class="text-dark">Dark renk metni (Toprak Kahvesi)</p>

                        <h4 class="mt-4 mb-3">Arka Plan Renkleri</h4>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <div class="p-3 bg-primary text-white">Primary</div>
                            <div class="p-3 bg-secondary text-white">Secondary</div>
                            <div class="p-3 bg-success text-white">Success</div>
                            <div class="p-3 bg-info text-white">Info</div>
                            <div class="p-3 bg-warning text-white">Warning</div>
                            <div class="p-3 bg-danger text-white">Danger</div>
                            <div class="p-3 bg-dark text-white">Dark</div>
                            <div class="p-3 bg-light">Light</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer id="footer" class="modern-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h3 class="modern-footer-title">Küp Cadısı</h3>
                    <p class="modern-footer-text">"İç Mimarlık ve Tasarım Hizmetleri"</p>
                </div>
                <div class="col-md-4 mb-4 mb-md-0">
                    <h3 class="modern-footer-title">Hızlı Bağlantılar</h3>
                    <ul class="modern-footer-links">
                        <li><a href="{% url 'anasayfa:ana_sayfa' %}">Ana Sayfa</a></li>
                        <li><a href="{% url 'anasayfa:test' %}">Test Sayfası</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h3 class="modern-footer-title">Takip Edin</h3>
                    <div class="modern-social-links">
                        <a href="#" class="social-link"><i class="bi bi-twitter-x"></i></a>
                        <a href="#" class="social-link"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="social-link"><i class="bi bi-instagram"></i></a>
                        <a href="#" class="social-link"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>
            </div>
            <div class="modern-footer-bottom">
                <div class="copyright text-center">
                    <p>&copy; <span id="copyright-year">2025</span> <strong>Küp Cadısı</strong> | Tüm Hakları Saklıdır</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll Top -->
    <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center">
        <i class="bi bi-arrow-up-short"></i>
    </a>

    <!-- Vendor JS Files -->
    <script src="{% static 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'assets/vendor/aos/aos.js' %}"></script>
    <script src="{% static 'assets/vendor/glightbox/js/glightbox.min.js' %}"></script>
    <script src="{% static 'assets/vendor/swiper/swiper-bundle.min.js' %}"></script>

    <!-- Template Main JS File -->
    <script src="{% static 'assets/js/main.js' %}"></script>

    <script>
        document.getElementById('copyright-year').textContent = new Date().getFullYear();

        // Slider başlatma, scroll-top butonu ve animasyonlu bileşenler
        document.addEventListener('DOMContentLoaded', function() {
            // Slider başlatma
            const testSlider = new Swiper('.test-slider', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
            });

            // Scroll-top butonu
            const scrollTopBtn = document.querySelector('.scroll-top');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    scrollTopBtn.classList.add('active');
                } else {
                    scrollTopBtn.classList.remove('active');
                }
            });

            scrollTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Sayaç animasyonu
            const counterElements = document.querySelectorAll('.counter-number');

            function animateCounter(counter) {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000; // ms cinsinden süre
                const stepTime = 20; // her adım arasındaki ms cinsinden süre
                const totalSteps = duration / stepTime;
                const stepValue = target / totalSteps;
                let current = 0;

                const timer = setInterval(() => {
                    current += stepValue;
                    counter.textContent = Math.round(current);

                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    }
                }, stepTime);
            }

            // Yetenek çubukları animasyonu
            const skillBars = document.querySelectorAll('.skill-progress');

            function animateSkillBars() {
                skillBars.forEach(bar => {
                    const width = bar.getAttribute('data-width');
                    bar.style.width = width + '%';
                });
            }

            // Görünürlük kontrolü için Intersection Observer
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Sayaç animasyonu
                        if (entry.target.classList.contains('counter-number')) {
                            animateCounter(entry.target);
                        }

                        // Yetenek çubukları animasyonu
                        if (entry.target.classList.contains('skill-bar')) {
                            setTimeout(() => {
                                entry.target.querySelector('.skill-progress').style.width =
                                    entry.target.querySelector('.skill-progress').getAttribute('data-width') + '%';
                            }, 300);
                        }

                        // Gözlemlemeyi durdur
                        animationObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.2
            });

            // Sayaçları gözlemle
            counterElements.forEach(counter => {
                animationObserver.observe(counter);
            });

            // Yetenek çubuklarını gözlemle
            document.querySelectorAll('.skill-bar').forEach(bar => {
                animationObserver.observe(bar);
            });
        });
    </script>
</body>

</html>
