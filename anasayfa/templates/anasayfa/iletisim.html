{% extends 'base.html' %}
{% load static %}

{% block title %}İletişim - Küp Cadısı{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<main class="modern-section py-5 iletisim-section">
  <div class="container modern-container">
    <!-- CSRF Token for AJAX requests -->
    {% csrf_token %}

    <!-- Başlık Bölümü -->
    <div class="modern-header mb-5">
      <h1 class="gradient-heading display-4 fw-bold mb-3"><PERSON><PERSON><PERSON>ş<PERSON></h1>
      <p class="lead text-muted"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, önerileriniz veya iş birliği teklifleriniz için benimle iletişime geçebilirsiniz.</p>
    </div>

    <!-- Mavi <PERSON>ölümü - İletişim -->
    <div class="blue-section py-5 mb-5">
      <div class="container">
        <div class="row align-items-center">
          <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Bilgileri -->
          <div class="col-lg-5">
            <h2 class="text-white mb-4">İletişim Bilgileri</h2>
            <p class="text-white mb-4">Sorularınız, önerileriniz veya iş birliği teklifleriniz için aşağıdaki kanallardan benimle iletişime geçebilirsiniz.</p>

            <div class="contact-info-box mb-4">
              <div class="info-item d-flex align-items-center mb-3">
                <div class="info-icon me-3">
                  <i class="bi bi-geo-alt"></i>
                </div>
                <div class="info-content">
                  <span class="info-title d-block text-white-50">Adres</span>
                  <div class="d-flex align-items-center">
                    <span class="info-value text-white me-2">{{ iletisim.adres }}</span>
                    {% if can_edit %}
                    <a href="#" class="edit-btn" data-type="iletisim_bilgisi" data-id="0" data-value="{{ iletisim.adres }}">
                      <i class="bi bi-pencil-square text-white-50"></i>
                    </a>
                    {% endif %}
                  </div>
                </div>
              </div>

              <div class="info-item d-flex align-items-center mb-3">
                <div class="info-icon me-3">
                  <i class="bi bi-envelope"></i>
                </div>
                <div class="info-content">
                  <span class="info-title d-block text-white-50">E-posta</span>
                  <div class="d-flex align-items-center">
                    <span class="info-value text-white me-2">{{ iletisim.email }}</span>
                    {% if can_edit %}
                    <a href="#" class="edit-btn" data-type="iletisim_bilgisi" data-id="1" data-value="{{ iletisim.email }}">
                      <i class="bi bi-pencil-square text-white-50"></i>
                    </a>
                    {% endif %}
                  </div>
                </div>
              </div>

              <div class="info-item d-flex align-items-center mb-3">
                <div class="info-icon me-3">
                  <i class="bi bi-telephone"></i>
                </div>
                <div class="info-content">
                  <span class="info-title d-block text-white-50">Telefon</span>
                  <div class="d-flex align-items-center">
                    <span class="info-value text-white me-2">{{ iletisim.telefon }}</span>
                    {% if can_edit %}
                    <a href="#" class="edit-btn" data-type="iletisim_bilgisi" data-id="2" data-value="{{ iletisim.telefon }}">
                      <i class="bi bi-pencil-square text-white-50"></i>
                    </a>
                    {% endif %}
                  </div>
                </div>
              </div>

              <div class="info-item d-flex align-items-center mb-3">
                <div class="info-icon me-3">
                  <i class="bi bi-clock"></i>
                </div>
                <div class="info-content">
                  <span class="info-title d-block text-white-50">Çalışma Saatleri</span>
                  <div class="d-flex align-items-center">
                    <span class="info-value text-white me-2">{{ iletisim.calisma_saatleri }}</span>
                    {% if can_edit %}
                    <a href="#" class="edit-btn" data-type="iletisim_bilgisi" data-id="3" data-value="{{ iletisim.calisma_saatleri }}">
                      <i class="bi bi-pencil-square text-white-50"></i>
                    </a>
                    {% endif %}
                  </div>
                </div>
              </div>

              <!-- WhatsApp İletişim Butonu -->
              <div class="info-item d-flex align-items-center">
                <div class="info-icon me-3 whatsapp-icon-bg">
                  <i class="bi bi-whatsapp"></i>
                </div>
                <div class="info-content">
                  <span class="info-title d-block text-white-50">WhatsApp</span>
                  <div class="d-flex align-items-center">
                    <a href="https://wa.me/{{ iletisim.telefon|cut:' '|cut:'+'|cut:'('|cut:')'|cut:'-' }}" target="_blank" class="text-white me-2 whatsapp-link">
                      <span class="info-value">Hemen Mesaj Gönder</span>
                      <i class="bi bi-arrow-right-circle ms-2"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sosyal Medya -->
            <div class="mt-4">
              <h5 class="text-white mb-3"><i class="bi bi-share me-2"></i>Sosyal Medya</h5>
              <div class="social-media-links">
                {% for hesap in sosyal_medya %}
                  {% if hesap.url != '#' or can_edit %}
                  <div class="social-media-item">
                    <a href="{{ hesap.url }}" class="social-link bg-moss" target="_blank">
                      <i class="bi {{ hesap.icon }}"></i>
                    </a>
                    {% if can_edit %}
                    <a href="#" class="edit-btn social-edit-btn" data-type="sosyal_medya_url" data-id="{{ forloop.counter0 }}" data-value="{{ hesap.url }}">
                      <i class="bi bi-pencil-square text-white-50"></i>
                    </a>
                    {% endif %}
                  </div>
                  {% endif %}
                {% endfor %}

                {% if can_edit %}
                <a href="{% url 'anasayfa:sosyal_medya_ekle' %}" class="social-link bg-moss admin-only">
                  <i class="bi bi-plus-lg"></i>
                </a>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- İletişim Formu -->
          <div class="col-lg-7">
            <div class="blue-card">
              <div class="blue-card-header">
                <h3><i class="bi bi-send me-2"></i>Mesaj Gönder</h3>
              </div>
              <div class="blue-card-body">
                <form id="contactForm" class="form">
                  {% csrf_token %}
                  <div class="row g-4">
                    <div class="col-md-6">
                      <div class="form__group">
                        <label for="name" class="form__label">Adınız</label>
                        <input type="text" class="form__input" id="name" name="name" placeholder="Adınızı giriniz" required>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form__group">
                        <label for="email" class="form__label">E-posta Adresiniz</label>
                        <input type="email" class="form__input" id="email" name="email" placeholder="<EMAIL>" required>
                      </div>
                    </div>
                    <div class="col-12">
                      <div class="form__group">
                        <label for="subject" class="form__label">Konu</label>
                        <input type="text" class="form__input" id="subject" name="subject" placeholder="Konu başlığı" required>
                      </div>
                    </div>
                    <div class="col-12">
                      <div class="form__group">
                        <label for="message" class="form__label">Mesajınız</label>
                        <textarea class="form__textarea" id="message" name="message" rows="5" placeholder="Mesajınızı buraya yazınız..." required></textarea>
                      </div>
                    </div>
                    <div class="col-12 text-end">
                      <button type="submit" class="modern-btn" style="background-color: #8b5a2b; color: white; border: none; padding: 0.6rem 1.5rem; border-radius: 4px; font-weight: 500; transition: all 0.2s ease;">
                        <i class="bi bi-send me-2"></i>Gönder
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="section-divider">
      <div class="section-divider-icon">
        <i class="bi bi-geo-alt"></i>
      </div>
    </div>

    <!-- Harita -->
    <div class="row">
      <div class="col-12 wow fadeInUp" data-wow-delay="0.3s">
        <div class="modern-card">
          <div class="modern-card-header">
            <h3><i class="bi bi-map me-2"></i>Konum</h3>
          </div>
          <div class="modern-card-body p-0">
            <div class="map-container">
              <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d96690.80542089987!2d29.01219284863276!3d40.99132839296343!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14cab7650656bd63%3A0x8ca058b28c20b6c3!2zS2FkxLFrw7Z5LCDEsHN0YW5idWw!5e0!3m2!1str!2str!4v1625761432548!5m2!1str!2str" class="map-iframe" allowfullscreen="" loading="lazy"></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
{% endblock %}

{% block extra_js %}
<script>
  // Form gönderme işlemi
  document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Form verileri
    const formData = new FormData(this);

    // Gönder butonunu devre dışı bırak ve yükleniyor göster
    const submitButton = this.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Gönderiliyor...';

    // AJAX isteği
    fetch('/iletisim/mesaj-gonder/', {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Başarı mesajı
        alert('Mesajınız başarıyla gönderildi!');
        document.getElementById('contactForm').reset();
      } else {
        // Hata mesajı
        alert('Mesaj gönderilirken bir hata oluştu: ' + data.error);
      }
    })
    .catch(error => {
      console.error('Hata:', error);
      alert('Mesaj gönderilirken bir hata oluştu.');
    })
    .finally(() => {
      // Gönder butonunu tekrar aktif et
      submitButton.disabled = false;
      submitButton.innerHTML = originalButtonText;
    });
  });

  {% if can_edit %}
  // Düzenlenebilir alanlar için kod
  document.addEventListener('DOMContentLoaded', function() {
    // Düzenleme butonlarını seç
    const editButtons = document.querySelectorAll('.edit-btn');

    // Her düzenleme butonu için olay dinleyicisi ekle
    editButtons.forEach(button => {
      // Düzenleme butonuna tıklama olayı ekle
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const type = this.getAttribute('data-type');
        const id = this.getAttribute('data-id');
        const currentValue = this.getAttribute('data-value');

        // Düzenleme modalını göster
        showEditModal(type, id, currentValue);
      });
    });

    // Düzenleme modalını göster
    function showEditModal(type, id, currentValue) {
      // Modal başlığını belirle
      let modalTitle = "Düzenle";
      let inputLabel = "İçerik";

      if (type === 'iletisim_bilgisi') {
        switch(id) {
          case '0':
            modalTitle = "Adres Düzenle";
            inputLabel = "Adres";
            break;
          case '1':
            modalTitle = "E-posta Düzenle";
            inputLabel = "E-posta";
            break;
          case '2':
            modalTitle = "Telefon Düzenle";
            inputLabel = "Telefon";
            break;
          case '3':
            modalTitle = "Çalışma Saatleri Düzenle";
            inputLabel = "Çalışma Saatleri";
            break;
        }
      } else if (type === 'sosyal_medya_url') {
        modalTitle = "Sosyal Medya Linki Düzenle";
        inputLabel = "URL";
      }

      // Modal HTML'i oluştur
      const modalHTML = `
        <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal__content">
              <div class="modal__header">
                <h5 class="modal__title" id="editModalLabel">
                  <i class="bi bi-pencil-square me-2"></i>${modalTitle}
                </h5>
                <button type="button" class="modal__close" data-bs-dismiss="modal" aria-label="Kapat"><i class="bi bi-x-lg"></i></button>
              </div>
              <div class="modal__body">
                <form id="editForm" class="form">
                  <div class="form__field mb-3">
                    <label for="editInput" class="form__label">${inputLabel}</label>
                    <div class="input-group">
                      <span class="input-group-text"><i class="bi bi-pencil"></i></span>
                      <input type="text" class="form__input" id="editInput" value="${currentValue}">
                    </div>
                    <input type="hidden" id="editType" value="${type}">
                    <input type="hidden" id="editId" value="${id}">
                  </div>
                </form>
              </div>
              <div class="modal__footer">
                <button type="button" class="button button--outline" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="button button--primary" id="saveChanges">Kaydet</button>
              </div>
            </div>
          </div>
        </div>
      `;

      // Modal'ı sayfaya ekle
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Modal'ı göster
      const modal = new bootstrap.Modal(document.getElementById('editModal'));
      modal.show();

      // Kaydet butonuna tıklama olayı ekle
      document.getElementById('saveChanges').addEventListener('click', function() {
        const newValue = document.getElementById('editInput').value.trim();
        const type = document.getElementById('editType').value;
        const id = document.getElementById('editId').value;

        // AJAX isteği gönder
        updateContent(type, id, newValue);

        // Modal'ı kapat
        modal.hide();

        // Modal'ı sayfadan kaldır
        document.getElementById('editModal').remove();
      });

      // Modal kapatıldığında temizle
      document.getElementById('editModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('editModal').remove();
      });
    }

    // İçeriği güncelle
    function updateContent(type, id, content) {
      // CSRF token al
      const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;

      // AJAX isteği gönder
      fetch('{% url "anasayfa:iletisim_inline_update" %}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrftoken
        },
        body: JSON.stringify({
          content_type: type,
          content_id: id,
          content: content
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Başarılı ise sayfadaki içeriği güncelle
          if (type === 'iletisim_bilgisi') {
            // İletişim bilgilerini güncelle
            const infoItems = document.querySelectorAll('.info-content');
            infoItems.forEach(item => {
              const editBtn = item.querySelector(`.edit-btn[data-type="${type}"][data-id="${id}"]`);
              if (editBtn) {
                const infoValue = item.querySelector('.info-value');
                if (infoValue) {
                  infoValue.textContent = content;
                  editBtn.setAttribute('data-value', content);
                }
              }
            });
          } else if (type === 'sosyal_medya_url') {
            // Sosyal medya linklerini güncelle
            const socialItems = document.querySelectorAll('.social-media-item');
            socialItems.forEach(item => {
              const editBtn = item.querySelector(`.edit-btn[data-type="${type}"][data-id="${id}"]`);
              if (editBtn) {
                const socialLink = item.querySelector('.social-link');
                if (socialLink) {
                  socialLink.href = content;
                  editBtn.setAttribute('data-value', content);
                }
              }
            });
          }

          // Başarı mesajı göster
          showAlert('Başarıyla güncellendi!', 'success');
        } else {
          // Hata mesajı göster
          showAlert('Güncelleme başarısız: ' + (data.error || 'Bilinmeyen hata'), 'danger');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showAlert('Güncelleme başarısız: ' + error.message, 'danger');
      });
    }

    // Uyarı mesajı göster
    function showAlert(message, type) {
      const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Kapat"></button>
        </div>
      `;

      // Uyarıyı sayfaya ekle
      document.querySelector('.blue-section').insertAdjacentHTML('afterbegin', alertHTML);

      // 5 saniye sonra uyarıyı kaldır
      setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
          alert.remove();
        }
      }, 5000);
    }
  });
  {% endif %}
</script>

{% endblock %}
