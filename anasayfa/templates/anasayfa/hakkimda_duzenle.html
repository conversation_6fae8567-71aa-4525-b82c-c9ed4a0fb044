{% extends 'base.html' %}
{% load static %}

{% block title %}Hakkımda Bilgilerini Düzenle - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modal-styles.css' %}">
<style>
.education-item {
    border-left: 4px solid var(--color-brown);
    transition: all 0.3s ease;
}

.education-item:hover {
    border-left-color: var(--color-moss);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
}

.education-content h6 {
    color: var(--color-brown);
    margin-bottom: 0.5rem;
}

.education-actions .btn {
    transition: all 0.3s ease;
}

.education-actions .btn:hover {
    transform: scale(1.1);
}

.icon-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--color-brown);
    color: white;
    margin-right: 10px;
}

.icon-circle-sm {
    width: 30px;
    height: 30px;
}
</style>
{% endblock %}

{% block content %}
<main class="main-content">
  <div class="container py-5">
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="modern-card">
          <div class="modern-card-header">
            <h3><i class="bi bi-person-circle"></i>Hakkımda Bilgilerini Düzenle</h3>
          </div>
          <div class="modern-card-body">
            <form method="post" enctype="multipart/form-data">
              {% csrf_token %}

              <div class="row g-4">
                <div class="col-md-4">
                  <!-- Mevcut Resim -->
                  <div class="text-center mb-4">
                    {% if hakkimda.resim %}
                      <img src="{{ hakkimda.resim.url }}" alt="Mevcut Profil Resmi" class="profile-image rounded-circle">
                    {% else %}
                      <div class="profile-placeholder rounded-circle d-flex align-items-center justify-content-center">
                        <i class="bi bi-person"></i>
                      </div>
                    {% endif %}
                    <p class="text-muted mt-2">Mevcut Profil Resmi</p>
                  </div>

                  <!-- Resim Yükleme Alanı -->
                  <div class="mb-4">
                    <label for="{{ form.resim.id_for_label }}" class="form-label">Yeni Profil Resmi</label>
                    {{ form.resim }}
                    {% if form.resim.errors %}
                    <div class="invalid-feedback d-block">
                      {{ form.resim.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">
                      Önerilen boyut: 300x300 piksel. Maksimum dosya boyutu: 2MB.
                    </div>
                  </div>
                </div>

                <div class="col-md-8">
                  <!-- Kişisel Bilgiler -->
                  <div class="mb-3">
                    <label for="{{ form.baslik.id_for_label }}" class="form-label">Başlık</label>
                    {{ form.baslik }}
                    {% if form.baslik.errors %}
                    <div class="invalid-feedback d-block">
                      {{ form.baslik.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">
                      Örneğin: Seramik Sanatçısı, Seramik Eğitmeni, vb.
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="{{ form.aciklama.id_for_label }}" class="form-label">Açıklama</label>
                    {{ form.aciklama }}
                    {% if form.aciklama.errors %}
                    <div class="invalid-feedback d-block">
                      {{ form.aciklama.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">
                      Kendiniz ve sanatınız hakkında kısa bir açıklama yazın.
                    </div>
                  </div>
                </div>
              </div>

              <!-- Seramik Sanatına Başlangıç -->
              <div class="mt-4">
                <h5 class="mb-3">
                  <span class="icon-circle icon-circle-sm">
                    <i class="bi bi-palette"></i>
                  </span>
                  Seramik Sanatına Başlangıç
                </h5>
                <div class="mb-3">
                  <label for="{{ form.sanat_baslangic.id_for_label }}" class="form-label">Seramik sanatına nasıl başladığınızı anlatın</label>
                  {{ form.sanat_baslangic }}
                  {% if form.sanat_baslangic.errors %}
                  <div class="invalid-feedback d-block">
                    {{ form.sanat_baslangic.errors }}
                  </div>
                  {% endif %}
                </div>
              </div>

              <!-- Kullandığım Teknikler -->
              <div class="mt-4">
                <h5 class="mb-3">
                  <span class="icon-circle icon-circle-sm">
                    <i class="bi bi-tools"></i>
                  </span>
                  Kullandığım Teknikler
                </h5>
                <div class="mb-3">
                  <label for="{{ form.kullandigi_teknikler.id_for_label }}" class="form-label">Kullandığınız teknikleri anlatın</label>
                  {{ form.kullandigi_teknikler }}
                  {% if form.kullandigi_teknikler.errors %}
                  <div class="invalid-feedback d-block">
                    {{ form.kullandigi_teknikler.errors }}
                  </div>
                  {% endif %}
                </div>
              </div>

              <!-- Atölye Bilgileri -->
              <div class="mt-4">
                <h5 class="mb-3">
                  <span class="icon-circle icon-circle-sm">
                    <i class="bi bi-house-door"></i>
                  </span>
                  Atölye Bilgileri
                </h5>
                <div class="mb-3">
                  <label for="{{ form.atolye_bilgileri.id_for_label }}" class="form-label">Atölyeniz hakkında bilgi verin</label>
                  {{ form.atolye_bilgileri }}
                  {% if form.atolye_bilgileri.errors %}
                  <div class="invalid-feedback d-block">
                    {{ form.atolye_bilgileri.errors }}
                  </div>
                  {% endif %}
                </div>
              </div>

              <!-- Eğitim Bilgileri -->
              <div class="mt-4">
                <h5 class="mb-3">
                  <span class="icon-circle icon-circle-sm">
                    <i class="bi bi-mortarboard"></i>
                  </span>
                  Eğitim Bilgileri
                  <button type="button" class="modern-btn modern-btn-outline btn-sm ms-2" data-bs-toggle="modal" data-bs-target="#egitimEkleModal" title="Yeni Eğitim Ekle">
                    <i class="bi bi-plus-lg"></i> Ekle
                  </button>
                </h5>

                {% if hakkimda.egitimler.exists %}
                  <div class="education-list">
                    {% for egitim in hakkimda.egitimler.all %}
                    <div class="education-item modern-card mb-3">
                      <div class="modern-card-body">
                        <div class="d-flex justify-content-between align-items-start">
                          <div class="education-content">
                            <h6 class="fw-bold text-primary">{{ egitim.bolum }}</h6>
                            <p class="text-muted mb-1">{{ egitim.okul }}</p>
                            <small class="text-muted">
                              {{ egitim.baslangic_tarihi|date:"Y" }} -
                              {% if egitim.bitis_tarihi %}{{ egitim.bitis_tarihi|date:"Y" }}{% else %}Devam Ediyor{% endif %}
                            </small>
                            {% if egitim.aciklama %}
                            <p class="mt-2 small">{{ egitim.aciklama }}</p>
                            {% endif %}
                          </div>
                          <div class="education-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                    onclick="egitimDuzenle({{ egitim.pk }}, '{{ egitim.okul|escapejs }}', '{{ egitim.bolum|escapejs }}', '{{ egitim.baslangic_tarihi|date:"Y-m-d" }}', '{% if egitim.bitis_tarihi %}{{ egitim.bitis_tarihi|date:"Y-m-d" }}{% endif %}', '{{ egitim.aciklama|escapejs }}')"
                                    title="Düzenle">
                              <i class="bi bi-pencil"></i>
                            </button>
                            <a href="{% url 'anasayfa:egitim_sil' egitim.pk %}" class="btn btn-sm btn-outline-danger"
                               onclick="return confirm('Bu eğitim bilgisini silmek istediğinizden emin misiniz?')" title="Sil">
                              <i class="bi bi-trash"></i>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                    {% endfor %}
                  </div>
                {% else %}
                  <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    Henüz eğitim bilgisi eklenmemiş. Yukarıdaki "Ekle" butonunu kullanarak yeni eğitim bilgisi ekleyebilirsiniz.
                  </div>
                {% endif %}
              </div>

              <!-- Butonlar -->
              <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'anasayfa:hakkimda' %}" class="modern-btn modern-btn-outline">
                  <i class="bi bi-arrow-left me-2"></i>Geri Dön
                </a>
                <button type="submit" class="modern-btn modern-btn-moss">
                  <i class="bi bi-check-lg me-2"></i>Kaydet
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- Eğitim Ekleme Modal -->
<div class="modal fade" id="egitimEkleModal" tabindex="-1" aria-labelledby="egitimEkleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header modal-header-gradient">
        <h5 class="modal-title" id="egitimEkleModalLabel">
          <i class="bi bi-mortarboard me-2"></i>Yeni Eğitim Bilgisi Ekle
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="egitimEkleForm" method="post" action="{% url 'anasayfa:egitim_ekle' %}">
        <div class="modal-body">
          {% csrf_token %}
          <div class="row g-3">
            <div class="col-md-6">
              <label for="okul" class="form-label">Okul/Kurum</label>
              <input type="text" class="form-control" id="okul" name="okul" required>
            </div>
            <div class="col-md-6">
              <label for="bolum" class="form-label">Bölüm/Program</label>
              <input type="text" class="form-control" id="bolum" name="bolum" required>
            </div>
            <div class="col-md-6">
              <label for="baslangic_tarihi" class="form-label">Başlangıç Tarihi</label>
              <input type="date" class="form-control" id="baslangic_tarihi" name="baslangic_tarihi" required>
            </div>
            <div class="col-md-6">
              <label for="bitis_tarihi" class="form-label">Bitiş Tarihi</label>
              <input type="date" class="form-control" id="bitis_tarihi" name="bitis_tarihi">
              <div class="form-text">Devam ediyorsa boş bırakabilirsiniz</div>
            </div>
            <div class="col-12">
              <label for="aciklama" class="form-label">Açıklama</label>
              <textarea class="form-control" id="aciklama" name="aciklama" rows="3"></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>İptal
          </button>
          <button type="submit" class="modern-btn modern-btn-primary">
            <i class="bi bi-check-lg me-1"></i>Kaydet
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Eğitim Düzenleme Modal -->
<div class="modal fade" id="egitimDuzenleModal" tabindex="-1" aria-labelledby="egitimDuzenleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header modal-header-gradient">
        <h5 class="modal-title" id="egitimDuzenleModalLabel">
          <i class="bi bi-pencil-square me-2"></i>Eğitim Bilgisi Düzenle
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="egitimDuzenleForm" method="post">
        <div class="modal-body">
          {% csrf_token %}
          <div class="row g-3">
            <div class="col-md-6">
              <label for="edit_okul" class="form-label">Okul/Kurum</label>
              <input type="text" class="form-control" id="edit_okul" name="okul" required>
            </div>
            <div class="col-md-6">
              <label for="edit_bolum" class="form-label">Bölüm/Program</label>
              <input type="text" class="form-control" id="edit_bolum" name="bolum" required>
            </div>
            <div class="col-md-6">
              <label for="edit_baslangic_tarihi" class="form-label">Başlangıç Tarihi</label>
              <input type="date" class="form-control" id="edit_baslangic_tarihi" name="baslangic_tarihi" required>
            </div>
            <div class="col-md-6">
              <label for="edit_bitis_tarihi" class="form-label">Bitiş Tarihi</label>
              <input type="date" class="form-control" id="edit_bitis_tarihi" name="bitis_tarihi">
              <div class="form-text">Devam ediyorsa boş bırakabilirsiniz</div>
            </div>
            <div class="col-12">
              <label for="edit_aciklama" class="form-label">Açıklama</label>
              <textarea class="form-control" id="edit_aciklama" name="aciklama" rows="3"></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="modern-btn modern-btn-outline" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>İptal
          </button>
          <button type="submit" class="modern-btn modern-btn-primary">
            <i class="bi bi-check-lg me-1"></i>Güncelle
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function egitimDuzenle(id, okul, bolum, baslangicTarihi, bitisTarihi, aciklama) {
    // Form action'ını güncelle
    document.getElementById('egitimDuzenleForm').action = "{% url 'anasayfa:egitim_duzenle' 0 %}".replace('0', id);

    // Form alanlarını doldur
    document.getElementById('edit_okul').value = okul;
    document.getElementById('edit_bolum').value = bolum;
    document.getElementById('edit_baslangic_tarihi').value = baslangicTarihi;
    document.getElementById('edit_bitis_tarihi').value = bitisTarihi;
    document.getElementById('edit_aciklama').value = aciklama;

    // Modal'ı aç
    var modal = new bootstrap.Modal(document.getElementById('egitimDuzenleModal'));
    modal.show();
}
</script>

{% endblock %}
