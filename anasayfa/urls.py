from django.urls import path
from . import views

app_name = 'anasayfa'

urlpatterns = [
    path('', views.ana_sayfa, name='ana_sayfa'),
    path('hakkimda/', views.hakkimda, name='hakkimda'),
    path('hakkimda/duzenle/', views.hakkimda_duzenle, name='hakkimda_duzenle'),
    path('hakkimda/egitim/ekle/', views.egitim_ekle, name='egitim_ekle'),
    path('hakkimda/egitim/<int:pk>/duzenle/', views.egitim_duzenle, name='egitim_duzenle'),
    path('hakkimda/egitim/<int:pk>/sil/', views.egitim_sil, name='egitim_sil'),
    path('iletisim/', views.iletisim, name='iletisim'),
    path('iletisim/duzenle/', views.iletisim_duzenle, name='iletisim_duzenle'),
    path('iletisim/sosyal-medya/ekle/', views.sosyal_medya_ekle, name='sosyal_medya_ekle'),
    path('iletisim/sosyal-medya/<int:pk>/duzenle/', views.sosyal_medya_duzenle, name='sosyal_medya_duzenle'),
    path('iletisim/sosyal-medya/<int:pk>/sil/', views.sosyal_medya_sil, name='sosyal_medya_sil'),

    # Inline editing endpoints
    path('hakkimda/inline-update/', views.hakkimda_inline_update, name='hakkimda_inline_update'),
    path('iletisim/inline-update/', views.iletisim_inline_update, name='iletisim_inline_update'),
    path('test/', views.test, name='test'),
]

