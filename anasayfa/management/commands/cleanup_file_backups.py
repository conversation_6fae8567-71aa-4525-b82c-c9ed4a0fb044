"""
Eski dosya yedeklerini temizlemek için management command.
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from utils.file_management import file_manager
from utils.logging import get_logger

logger = get_logger(__name__)


class Command(BaseCommand):
    help = 'Eski dosya yedeklerini temizler'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='<PERSON><PERSON> günden eski yedekler silinsin (varsayılan: 30)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Gerçekte silmeden sadece ne silineceğini göster'
        )

    def handle(self, *args, **options):
        days_to_keep = options['days']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'Dosya yedekleri temizleniyor...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN modu - hiçbir dosya silinmeyecek')
            )
        
        try:
            if dry_run:
                # Dry run için sadece sayım yap
                import time
                from pathlib import Path
                
                backup_dir = Path(settings.MEDIA_ROOT) / 'backups'
                if not backup_dir.exists():
                    self.stdout.write('Yedek dizini bulunamadı.')
                    return
                
                cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
                count = 0
                total_size = 0
                
                for backup_file in backup_dir.rglob('*'):
                    if backup_file.is_file() and backup_file.stat().st_mtime < cutoff_time:
                        count += 1
                        total_size += backup_file.stat().st_size
                
                self.stdout.write(
                    f'Silinecek dosya sayısı: {count}'
                )
                self.stdout.write(
                    f'Toplam boyut: {total_size / 1024 / 1024:.2f} MB'
                )
                
            else:
                # Gerçek temizlik
                deleted_count = file_manager.cleanup_old_backups(days_to_keep)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Başarıyla tamamlandı! {deleted_count} dosya silindi.'
                    )
                )
                
                logger.info("Yedek temizleme komutu çalıştırıldı", extra={
                    'days_to_keep': days_to_keep,
                    'deleted_count': deleted_count,
                    'operation': 'cleanup_command'
                })
                
        except Exception as e:
            error_msg = f'Hata oluştu: {str(e)}'
            self.stdout.write(
                self.style.ERROR(error_msg)
            )
            
            logger.error("Yedek temizleme komutunda hata", extra={
                'error': str(e),
                'days_to_keep': days_to_keep,
                'operation': 'cleanup_command'
            })
            
            raise CommandError(error_msg)
