from django.contrib.auth.models import User
from .models import SosyalMedya

def sosyal_medya(request):
    """
    Tüm şablonlarda kullanılabilecek sosyal medya hesaplarını içeren context processor.
    """
    # Yönetici kullanıcılarını bul
    admin_users = User.objects.filter(is_staff=True)

    # Sosyal medya hesaplarını al
    sosyal_medya_hesaplari = []

    # Önce giriş yapmış kullanıcının hesaplarını kontrol et (eğer yöneticiyse)
    if request.user.is_authenticated and request.user.is_staff:
        # <PERSON><PERSON><PERSON> yapmış yöneticinin birincil sosyal medya hesaplarını kontrol et
        primary_sosyal_medya = SosyalMedya.objects.filter(user=request.user, is_primary_user=True, aktif=True)
        if primary_sosyal_medya.exists():
            sosyal_medya_hesaplari = primary_sosyal_medya
        else:
            # <PERSON>iri<PERSON><PERSON> hesaplar yoksa, kull<PERSON><PERSON><PERSON>ının tüm aktif sosyal medya hesaplarını al
            sosyal_medya_hesaplari = SosyalMedya.objects.filter(user=request.user, aktif=True)

    # Eğer giriş yapmış kullanıcının hesapları yoksa, birincil olarak işaretlenmiş herhangi bir kullanıcının sosyal medya hesaplarını kontrol et
    if not sosyal_medya_hesaplari:
        # Birincil olarak işaretlenmiş sosyal medya hesaplarını bul
        primary_sosyal_medya = SosyalMedya.objects.filter(is_primary_user=True, aktif=True)
        if primary_sosyal_medya.exists():
            # Birincil kullanıcıyı bul
            primary_user = primary_sosyal_medya.first().user
            # Bu kullanıcının tüm aktif sosyal medya hesaplarını al
            sosyal_medya_hesaplari = SosyalMedya.objects.filter(user=primary_user, aktif=True)

    # Eğer birincil sosyal medya seti yoksa, diğer yöneticilerin hesaplarını kontrol et
    if not sosyal_medya_hesaplari:
        for admin in admin_users:
            sosyal_medya_hesaplari = SosyalMedya.objects.filter(user=admin, aktif=True)
            if sosyal_medya_hesaplari:
                break

    # Varsayılan sosyal medya hesapları
    default_sosyal_medya = [
        {'platform': 'instagram', 'url': '#', 'icon': 'bi-instagram'},
        {'platform': 'facebook', 'url': '#', 'icon': 'bi-facebook'},
        {'platform': 'twitter', 'url': '#', 'icon': 'bi-twitter'},
        {'platform': 'youtube', 'url': '#', 'icon': 'bi-youtube'},
        {'platform': 'linkedin', 'url': '#', 'icon': 'bi-linkedin'}
    ]

    # Sosyal medya hesapları varsa, varsayılan değerleri güncelle
    if sosyal_medya_hesaplari:
        # Sosyal medya hesaplarını platform adına göre sözlük olarak hazırla
        sosyal_medya_dict = {}
        for hesap in sosyal_medya_hesaplari:
            platform = hesap.platform.lower()
            sosyal_medya_dict[platform] = {
                'platform': platform,
                'url': hesap.url,
                'icon': f'bi-{platform}'
            }

        # Varsayılan sosyal medya listesini güncelle
        for i, hesap in enumerate(default_sosyal_medya):
            platform = hesap['platform']
            if platform in sosyal_medya_dict:
                default_sosyal_medya[i] = sosyal_medya_dict[platform]

    return {
        'sosyal_medya': default_sosyal_medya
    }
