# Generated by Django 5.2 on 2025-06-08 01:58

import anasayfa.models
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Hakkim<PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('baslik', models.CharField(max_length=200, verbose_name='Başlık')),
                ('aciklama', models.TextField(verbose_name='Açıklama')),
                ('resim', models.ImageField(blank=True, null=True, upload_to=anasayfa.models.hakkimda_resim_yolu, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'webp'])], verbose_name='Profil <PERSON>smi')),
                ('sanat_baslangic', models.TextField(blank=True, null=True, verbose_name='Seramik Sanatına Başlangıç')),
                ('kullandigi_teknikler', models.TextField(blank=True, null=True, verbose_name='Kullandığı Teknikler')),
                ('atolye_bilgileri', models.TextField(blank=True, null=True, verbose_name='Atölye Bilgileri')),
                ('is_primary', models.BooleanField(default=False, help_text='Bu profil, site genelinde görüntülenecek birincil profil olarak kullanılacak.', verbose_name='Birincil Profil')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('guncelleme_tarihi', models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hakkimda', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Hakkımda',
                'verbose_name_plural': 'Hakkımda',
            },
        ),
        migrations.CreateModel(
            name='Egitim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('okul', models.CharField(max_length=200, verbose_name='Okul')),
                ('bolum', models.CharField(max_length=200, verbose_name='Bölüm')),
                ('baslangic_tarihi', models.DateField(verbose_name='Başlangıç Tarihi')),
                ('bitis_tarihi', models.DateField(blank=True, null=True, verbose_name='Bitiş Tarihi')),
                ('aciklama', models.TextField(blank=True, verbose_name='Açıklama')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('guncelleme_tarihi', models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')),
                ('hakkimda', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='egitimler', to='anasayfa.hakkimda')),
            ],
            options={
                'verbose_name': 'Eğitim',
                'verbose_name_plural': 'Eğitimler',
            },
        ),
        migrations.CreateModel(
            name='IletisimBilgisi',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, verbose_name='E-posta')),
                ('telefon', models.CharField(max_length=20, verbose_name='Telefon')),
                ('adres', models.TextField(verbose_name='Adres')),
                ('calisma_saatleri', models.CharField(default='Pazartesi - Cumartesi: 10:00 - 18:00', max_length=200, verbose_name='Çalışma Saatleri')),
                ('is_primary', models.BooleanField(default=False, help_text='Bu iletişim bilgisi, site genelinde görüntülenecek birincil iletişim bilgisi olarak kullanılacak.', verbose_name='Birincil İletişim Bilgisi')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('guncelleme_tarihi', models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='iletisim_bilgileri', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'İletişim Bilgisi',
                'verbose_name_plural': 'İletişim Bilgileri',
            },
        ),
        migrations.CreateModel(
            name='SosyalMedya',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('platform', models.CharField(choices=[('instagram', 'Instagram'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('whatsapp', 'WhatsApp'), ('youtube', 'YouTube'), ('linkedin', 'LinkedIn')], max_length=20, verbose_name='Platform')),
                ('url', models.URLField(verbose_name='URL')),
                ('aktif', models.BooleanField(default=True, verbose_name='Aktif')),
                ('is_primary_set', models.BooleanField(default=False, help_text='Bu kullanıcının sosyal medya hesapları, site genelinde görüntülenecek birincil sosyal medya hesapları olarak kullanılacak.', verbose_name='Birincil Sosyal Medya Seti')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('guncelleme_tarihi', models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sosyal_medya', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sosyal Medya',
                'verbose_name_plural': 'Sosyal Medya',
            },
        ),
    ]
