# Generated by Django 4.2 on 2025-06-14 12:14

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('anasayfa', '0003_iletisimbilgisi_baslik'),
    ]

    operations = [
        migrations.AlterField(
            model_name='sosyalmedya',
            name='platform',
            field=models.CharField(choices=[('instagram', 'Instagram'), ('facebook', 'Facebook'), ('twitter', 'Twitter'), ('whatsapp', 'WhatsApp'), ('youtube', 'YouTube'), ('linkedin', 'LinkedIn'), ('tiktok', 'TikTok'), ('pinterest', 'Pinterest'), ('telegram', 'Telegram'), ('discord', 'Discord')], max_length=20, verbose_name='Platform'),
        ),
        migrations.AlterUniqueTogether(
            name='sosyalmedya',
            unique_together={('user', 'platform')},
        ),
        migrations.AddIndex(
            model_name='sosyalmedya',
            index=models.Index(fields=['user', 'aktif'], name='sm_user_aktif_idx'),
        ),
        migrations.AddIndex(
            model_name='sosyalmedya',
            index=models.Index(fields=['platform', 'aktif'], name='sm_platform_aktif_idx'),
        ),
        migrations.AddIndex(
            model_name='sosyalmedya',
            index=models.Index(fields=['is_primary_user'], name='sm_primary_idx'),
        ),
    ]
