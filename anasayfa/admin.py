from django.contrib import admin
from django.contrib import messages
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from .models import Hakkimda, Egitim, IletisimBilgisi, SosyalMedya

class EgitimInline(admin.TabularInline):
    model = Egitim
    extra = 1
    fields = ('okul', 'bolum', 'baslangic_tarihi', 'bitis_tarihi', 'aciklama')
    verbose_name = "Eğitim Bilgisi"
    verbose_name_plural = "Eğitim Bilgileri"

@admin.register(Hakkimda)
class HakkimdaAdmin(admin.ModelAdmin):
    list_display = ('baslik', 'user_display', 'primary_status', 'education_count', 'updated_display')
    search_fields = ('baslik', 'aciklama', 'user__username')
    list_filter = ('is_primary', 'guncelleme_tarihi', 'user')
    list_editable = ()
    inlines = [EgitimInline]
    actions = ['make_primary']
    list_per_page = 20

    fieldsets = (
        ('👤 Profil Bilgileri', {
            'fields': ('user', 'baslik', 'aciklama'),
            'description': 'Hakkımda sayfasında görünecek temel bilgiler'
        }),
        ('📸 Görsel', {
            'fields': ('resim',),
            'description': 'Profil fotoğrafı (isteğe bağlı)'
        }),
        ('🎨 Sanat Bilgileri', {
            'fields': ('sanat_baslangic', 'kullandigi_teknikler', 'atolye_bilgileri'),
            'classes': ('collapse',),
            'description': 'Seramik sanatı ile ilgili detaylı bilgiler'
        }),
        ('⭐ Öncelik', {
            'fields': ('is_primary',),
            'description': 'Bu profil ana sayfa için birincil profil mi?'
        }),
        ('📅 Tarih Bilgileri', {
            'fields': ('olusturma_tarihi', 'guncelleme_tarihi'),
            'classes': ('collapse',),
            'description': 'Profil oluşturma ve güncelleme tarihleri'
        })
    )
    readonly_fields = ('olusturma_tarihi', 'guncelleme_tarihi')

    def user_display(self, obj):
        return format_html(
            '<span style="background: linear-gradient(45deg, #667eea, #764ba2); '
            'color: white; padding: 3px 8px; border-radius: 8px; font-size: 11px; '
            'font-weight: bold;">👤 {}</span>',
            obj.user.username
        )
    user_display.short_description = "Kullanıcı"
    user_display.admin_order_field = 'user__username'

    def primary_status(self, obj):
        if obj.is_primary:
            return format_html(
                '<span style="background: #27ae60; color: white; padding: 3px 8px; '
                'border-radius: 8px; font-size: 11px; font-weight: bold;">⭐ Birincil</span>'
            )
        return format_html(
            '<span style="background: #95a5a6; color: white; padding: 3px 8px; '
            'border-radius: 8px; font-size: 11px;">Yedek</span>'
        )
    primary_status.short_description = "Durum"
    primary_status.admin_order_field = 'is_primary'

    def education_count(self, obj):
        count = obj.egitimler.count()
        if count > 0:
            return format_html(
                '<span style="color: #27ae60; font-weight: bold;">🎓 {} eğitim</span>',
                count
            )
        return format_html('<span style="color: #e74c3c;">🎓 Eğitim yok</span>')
    education_count.short_description = "Eğitimler"

    def updated_display(self, obj):
        return obj.guncelleme_tarihi.strftime('%d.%m.%Y %H:%M')
    updated_display.short_description = "Son Güncelleme"
    updated_display.admin_order_field = 'guncelleme_tarihi'

    def make_primary(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(request, "❌ Sadece bir profil birincil olarak işaretlenebilir.", messages.ERROR)
            return

        # Önce tüm profilleri yedek yap
        Hakkimda.objects.update(is_primary=False)
        # Seçileni birincil yap
        queryset.update(is_primary=True)
        self.message_user(request, "✅ Seçilen profil birincil olarak işaretlendi.")

    make_primary.short_description = "⭐ Seçilen profili birincil olarak işaretle"

@admin.register(IletisimBilgisi)
class IletisimBilgisiAdmin(admin.ModelAdmin):
    list_display = ('user', 'baslik', 'email', 'telefon', 'is_primary', 'guncelleme_tarihi')
    search_fields = ('baslik', 'email', 'telefon', 'adres')
    list_filter = ('is_primary', 'guncelleme_tarihi')
    list_editable = ('is_primary',)
    fields = ('user', 'baslik', 'email', 'telefon', 'adres', 'calisma_saatleri', 'is_primary')
    actions = ['make_primary']

    def make_primary(self, request, queryset):
        if queryset.count() > 1:
            self.message_user(request, "Sadece bir iletişim bilgisi birincil olarak işaretlenebilir.", messages.ERROR)
            return

        queryset.update(is_primary=True)
        self.message_user(request, "Seçilen iletişim bilgisi birincil olarak işaretlendi.")

    make_primary.short_description = "Seçilen iletişim bilgisini birincil olarak işaretle"

@admin.register(SosyalMedya)
class SosyalMedyaAdmin(admin.ModelAdmin):
    list_display = ('user', 'platform', 'url', 'aktif', 'is_primary_user', 'guncelleme_tarihi')
    list_filter = ('platform', 'aktif', 'is_primary_user', 'guncelleme_tarihi')
    search_fields = ('url',)
    list_editable = ('aktif', 'is_primary_user')
    actions = ['make_primary_user']

    def make_primary_user(self, request, queryset):
        # Aynı kullanıcıya ait olmayan sosyal medya hesapları seçildiyse uyarı ver
        users = queryset.values_list('user', flat=True).distinct()
        if users.count() > 1:
            self.message_user(request, "Sadece aynı kullanıcıya ait sosyal medya hesapları birincil olarak işaretlenebilir.", messages.ERROR)
            return

        # Seçilen sosyal medya hesaplarının kullanıcısını birincil olarak işaretle
        user_id = users.first()
        SosyalMedya.objects.exclude(user_id=user_id).update(is_primary_user=False)
        SosyalMedya.objects.filter(user_id=user_id).update(is_primary_user=True)

        self.message_user(request, f"Seçilen kullanıcının tüm sosyal medya hesapları birincil olarak işaretlendi.")

    make_primary_user.short_description = "Seçilen kullanıcının sosyal medya hesaplarını birincil olarak işaretle"
