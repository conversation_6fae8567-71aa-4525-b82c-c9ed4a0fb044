from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.core.validators import FileExtensionValidator
from django.db.models.signals import pre_delete, post_delete
from django.dispatch import receiver
import os
import uuid

def hakkimda_resim_yolu(instance, filename):
    """Profil resimlerini güvenli bir şekilde yüklemek için özel bir yol oluşturur."""
    # Dosya uzantısını al
    ext = filename.split('.')[-1]
    # Benzersiz bir dosya adı oluştur
    filename = f"{uuid.uuid4().hex}.{ext}"
    # Kullanıcı ID'sine göre klasörleme yap
    return os.path.join('hakkimda', str(instance.user.id), filename)


class HakkimdaManager(models.Manager):
    """Hakkimda modeli için özel manager."""

    def get_primary_or_fallback(self, current_user=None):
        """
        Birincil Hakkimda nesnesini döndürür. Yoksa yönetici kullanıcılarından birini döndürür.

        Args:
            current_user: Mevcut kullanıcı (eğer yöneticiyse öncelik verilir)

        Returns:
            Hakkimda instance veya None
        """
        # 1. Eğer mevcut kullanıcı yöneticiyse, onun bilgilerini kontrol et
        if current_user and current_user.is_authenticated and current_user.is_staff:
            # Önce birincil profilini kontrol et
            hakkimda = self.filter(user=current_user, is_primary=True).first()
            if hakkimda:
                return hakkimda

            # Birincil profil yoksa herhangi bir profilini al
            hakkimda = self.filter(user=current_user).first()
            if hakkimda:
                return hakkimda

        # 2. Birincil olarak işaretlenmiş herhangi bir profili kontrol et
        hakkimda = self.filter(is_primary=True).first()
        if hakkimda:
            return hakkimda

        # 3. Herhangi bir yöneticinin profilini al
        admin_users = User.objects.filter(is_staff=True)
        for admin in admin_users:
            hakkimda = self.filter(user=admin).first()
            if hakkimda:
                return hakkimda

        # 4. Hiçbir profil yoksa varsayılan oluştur
        if admin_users.exists():
            admin_user = admin_users.first()
            hakkimda = self.create(
                user=admin_user,
                baslik="Seramik Sanatçısı",
                aciklama="Seramik sanatı ile ilgileniyorum ve çeşitli tekniklerle özgün eserler üretiyorum.",
                is_primary=True
            )
            return hakkimda

        return None


class IletisimBilgisiManager(models.Manager):
    """IletisimBilgisi modeli için özel manager."""

    def get_primary_or_fallback(self, current_user=None):
        """
        Birincil İletişim Bilgisi nesnesini döndürür. Yoksa yönetici kullanıcılarından birini döndürür.

        Args:
            current_user: Mevcut kullanıcı (eğer yöneticiyse öncelik verilir)

        Returns:
            IletisimBilgisi instance veya None
        """
        # 1. Eğer mevcut kullanıcı yöneticiyse, onun bilgilerini kontrol et
        if current_user and current_user.is_authenticated and current_user.is_staff:
            # Önce birincil iletişim bilgisini kontrol et
            iletisim = self.filter(user=current_user, is_primary=True).first()
            if iletisim:
                return iletisim

            # Birincil iletişim bilgisi yoksa herhangi birini al
            iletisim = self.filter(user=current_user).first()
            if iletisim:
                return iletisim

        # 2. Birincil olarak işaretlenmiş herhangi bir iletişim bilgisini kontrol et
        iletisim = self.filter(is_primary=True).first()
        if iletisim:
            return iletisim

        # 3. Herhangi bir yöneticinin iletişim bilgisini al
        admin_users = User.objects.filter(is_staff=True)
        for admin in admin_users:
            iletisim = self.filter(user=admin).first()
            if iletisim:
                return iletisim

        return None


class SosyalMedyaManager(models.Manager):
    """SosyalMedya modeli için özel manager."""

    def get_primary_user_accounts(self, current_user=None):
        """
        Birincil kullanıcının sosyal medya hesaplarını döndürür.

        Args:
            current_user: Mevcut kullanıcı (eğer yöneticiyse öncelik verilir)

        Returns:
            QuerySet: Sosyal medya hesapları
        """
        # 1. Eğer mevcut kullanıcı yöneticiyse, onun hesaplarını döndür
        if current_user and current_user.is_authenticated and current_user.is_staff:
            accounts = self.filter(user=current_user, aktif=True)
            if accounts.exists():
                return accounts

        # 2. Birincil olarak işaretlenmiş kullanıcının hesaplarını kontrol et
        primary_accounts = self.filter(is_primary_user=True, aktif=True)
        if primary_accounts.exists():
            primary_user = primary_accounts.first().user
            return self.filter(user=primary_user, aktif=True)

        # 3. Herhangi bir yöneticinin hesaplarını al
        admin_users = User.objects.filter(is_staff=True)
        for admin in admin_users:
            accounts = self.filter(user=admin, aktif=True)
            if accounts.exists():
                return accounts

        # 4. Boş QuerySet döndür
        return self.none()


class Hakkimda(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hakkimda')
    baslik = models.CharField(_("Başlık"), max_length=200)
    aciklama = models.TextField(_("Açıklama"))
    resim = models.ImageField(
        _("Profil Resmi"),
        upload_to=hakkimda_resim_yolu,
        null=True,
        blank=True,
        validators=[
            FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'webp']),
        ]
    )
    sanat_baslangic = models.TextField(_("Seramik Sanatına Başlangıç"), blank=True, null=True)
    kullandigi_teknikler = models.TextField(_("Kullandığı Teknikler"), blank=True, null=True)
    atolye_bilgileri = models.TextField(_("Atölye Bilgileri"), blank=True, null=True)
    is_primary = models.BooleanField(_("Birincil Profil"), default=False, help_text=_("Bu profil, site genelinde görüntülenecek birincil profil olarak kullanılacak."))
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    # Özel manager
    objects = HakkimdaManager()

    class Meta:
        verbose_name = _("Hakkımda")
        verbose_name_plural = _("Hakkımda")

    def __str__(self):
        return self.baslik

    def save(self, *args, **kwargs):
        # Eğer bu kayıt birincil olarak işaretlendiyse, diğer kayıtları birincil olmaktan çıkar
        if self.is_primary:
            # Sadece kendisi dışındaki kayıtları güncelle (gereksiz self güncellemesini önle)
            Hakkimda.objects.filter(is_primary=True).exclude(pk=self.pk).update(is_primary=False)
        # Eğer hiç birincil kayıt yoksa ve bu yeni bir kayıtsa, bunu birincil yap
        elif not Hakkimda.objects.filter(is_primary=True).exists() and not self.pk:
            self.is_primary = True
        super().save(*args, **kwargs)

class Egitim(models.Model):
    hakkimda = models.ForeignKey(Hakkimda, on_delete=models.CASCADE, related_name='egitimler')
    okul = models.CharField(_("Okul"), max_length=200)
    bolum = models.CharField(_("Bölüm"), max_length=200)
    baslangic_tarihi = models.DateField(_("Başlangıç Tarihi"))
    bitis_tarihi = models.DateField(_("Bitiş Tarihi"), null=True, blank=True)
    aciklama = models.TextField(_("Açıklama"), blank=True)
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    class Meta:
        verbose_name = _("Eğitim")
        verbose_name_plural = _("Eğitimler")

    def __str__(self):
        return f"{self.okul} - {self.bolum}"

class IletisimBilgisi(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='iletisim_bilgileri')
    baslik = models.CharField(_("Başlık"), max_length=200, default="İletişim", help_text=_("İletişim sayfasında görüntülenecek başlık"))
    email = models.EmailField(_("E-posta"))
    telefon = models.CharField(_("Telefon"), max_length=20)
    adres = models.TextField(_("Adres"))
    calisma_saatleri = models.CharField(_("Çalışma Saatleri"), max_length=200, default="Pazartesi - Cumartesi: 10:00 - 18:00")
    is_primary = models.BooleanField(_("Birincil İletişim Bilgisi"), default=False, help_text=_("Bu iletişim bilgisi, site genelinde görüntülenecek birincil iletişim bilgisi olarak kullanılacak."))
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    # Özel manager
    objects = IletisimBilgisiManager()

    class Meta:
        verbose_name = _("İletişim Bilgisi")
        verbose_name_plural = _("İletişim Bilgileri")

    def __str__(self):
        return f"{self.user.username} - İletişim Bilgileri"

    def save(self, *args, **kwargs):
        # Eğer bu kayıt birincil olarak işaretlendiyse, diğer kayıtları birincil olmaktan çıkar
        if self.is_primary:
            # Sadece kendisi dışındaki kayıtları güncelle (gereksiz self güncellemesini önle)
            IletisimBilgisi.objects.filter(is_primary=True).exclude(pk=self.pk).update(is_primary=False)
        # Eğer hiç birincil kayıt yoksa ve bu yeni bir kayıtsa, bunu birincil yap
        elif not IletisimBilgisi.objects.filter(is_primary=True).exists() and not self.pk:
            self.is_primary = True
        super().save(*args, **kwargs)

class SosyalMedya(models.Model):
    PLATFORM_CHOICES = [
        ('instagram', 'Instagram'),
        ('facebook', 'Facebook'),
        ('twitter', 'Twitter'),
        ('whatsapp', 'WhatsApp'),
        ('youtube', 'YouTube'),
        ('linkedin', 'LinkedIn'),
        ('tiktok', 'TikTok'),
        ('pinterest', 'Pinterest'),
        ('telegram', 'Telegram'),
        ('discord', 'Discord'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sosyal_medya')
    platform = models.CharField(_("Platform"), max_length=20, choices=PLATFORM_CHOICES)
    url = models.URLField(_("URL"))
    aktif = models.BooleanField(_("Aktif"), default=True)
    is_primary_user = models.BooleanField(_("Birincil Kullanıcı"), default=False, help_text=_("Bu kullanıcının sosyal medya hesapları, site genelinde görüntülenecek birincil sosyal medya hesapları olarak kullanılacak."))
    olusturma_tarihi = models.DateTimeField(_("Oluşturma Tarihi"), auto_now_add=True)
    guncelleme_tarihi = models.DateTimeField(_("Güncelleme Tarihi"), auto_now=True)

    # Özel manager
    objects = SosyalMedyaManager()

    class Meta:
        verbose_name = _("Sosyal Medya")
        verbose_name_plural = _("Sosyal Medya")
        # Bir kullanıcının aynı platformda birden fazla hesabı olamaz
        unique_together = [['user', 'platform']]
        indexes = [
            models.Index(fields=['user', 'aktif'], name='sm_user_aktif_idx'),
            models.Index(fields=['platform', 'aktif'], name='sm_platform_aktif_idx'),
            models.Index(fields=['is_primary_user'], name='sm_primary_idx'),
        ]

    def save(self, *args, **kwargs):
        # Eğer bu kullanıcı birincil olarak işaretlendiyse, diğer kullanıcıları birincil olmaktan çıkar
        if self.is_primary_user:
            # Önce diğer kullanıcıların tüm sosyal medya hesaplarını birincil olmaktan çıkar
            SosyalMedya.objects.exclude(user=self.user).update(is_primary_user=False)
            # Bu kullanıcının diğer hesaplarını da birincil yap (aynı kullanıcının tüm hesapları birincil olmalı)
            SosyalMedya.objects.filter(user=self.user).exclude(pk=self.pk).update(is_primary_user=True)
        # Eğer hiç birincil kullanıcı yoksa ve bu yeni bir kayıtsa, bunu birincil yap
        elif not SosyalMedya.objects.filter(is_primary_user=True).exists() and not self.pk:
            self.is_primary_user = True
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.username} - {self.get_platform_display()}"


# Signal handlers for safe file deletion
@receiver(pre_delete, sender=Hakkimda)
def hakkimda_delete_file(sender, instance, **kwargs):
    """Hakkımda nesnesi silinirken resim dosyasını güvenli bir şekilde sil."""
    if instance.resim:
        try:
            from utils.file_management import safe_delete_file
            from utils.logging import get_logger

            logger = get_logger(__name__)

            success, error_msg = safe_delete_file(instance.resim.path, create_backup=True)

            if success:
                logger.info("Hakkımda resmi güvenli bir şekilde silindi", extra={
                    'user': instance.user.username,
                    'file_path': instance.resim.path,
                    'operation': 'model_delete_signal',
                    'model': 'Hakkimda'
                })
            else:
                logger.error("Hakkımda resmi silinemedi", extra={
                    'user': instance.user.username,
                    'file_path': instance.resim.path,
                    'error': error_msg,
                    'operation': 'model_delete_signal',
                    'model': 'Hakkimda'
                })
        except Exception as e:
            # Signal içinde hata olursa model silme işlemini durdurmayalım
            try:
                from utils.logging import get_logger
                logger = get_logger(__name__)
                logger.error("Hakkımda resmi silme signal'ında hata", extra={
                    'user': instance.user.username if instance.user else 'Unknown',
                    'error': str(e),
                    'operation': 'model_delete_signal',
                    'model': 'Hakkimda'
                })
            except:
                pass  # Logging bile başarısızsa sessizce devam et
