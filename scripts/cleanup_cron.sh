#!/bin/bash

# Geçici dosya temizleme cron job script'i
# Bu script'i crontab'a ekleyerek otomatik temizlik yapabilirsiniz
# <PERSON>rne<PERSON> crontab girişi (her gün gece 2:00'da çalışır):
# 0 2 * * * /path/to/your/project/scripts/cleanup_cron.sh

# Proje dizinine git
cd "$(dirname "$0")/.."

# Virtual environment'ı aktifleştir (eğer kullanıyorsanız)
# source venv/bin/activate

# Log dosyası
LOG_FILE="logs/cleanup_cron.log"

# Log dizinini oluştur
mkdir -p logs

# Tarih ve saat bilgisi
echo "$(date): Geçici dosya temizleme başlatılıyor..." >> "$LOG_FILE"

# Geçici dosyaları temizle (7 günden eski)
python manage.py cleanup_temp_files --days 7 >> "$LOG_FILE" 2>&1

# Yet<PERSON> da temizle (opsi<PERSON><PERSON> - di<PERSON><PERSON><PERSON>)
# python manage.py cleanup_temp_files --days 7 --include-orphaned >> "$LOG_FILE" 2>&1

# Dosya yedeklerini temizle (30 günden eski)
python manage.py cleanup_file_backups --days 30 >> "$LOG_FILE" 2>&1

echo "$(date): Temizleme tamamlandı." >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"

# Log dosyasını döndür (çok büyürse)
if [ -f "$LOG_FILE" ] && [ $(wc -l < "$LOG_FILE") -gt 1000 ]; then
    tail -500 "$LOG_FILE" > "${LOG_FILE}.tmp"
    mv "${LOG_FILE}.tmp" "$LOG_FILE"
fi
