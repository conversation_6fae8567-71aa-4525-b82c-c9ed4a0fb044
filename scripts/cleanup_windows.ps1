# Windows için geçici dosya temizleme script'i
# Bu script'i Windows Task Scheduler ile otomatikleştirebilirsiniz

param(
    [int]$Days = 7,
    [switch]$IncludeOrphaned = $false,
    [switch]$DryRun = $false
)

# Proje dizinine git
$ProjectPath = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectPath

# Log dosyası
$LogPath = Join-Path $ProjectPath "logs"
$LogFile = Join-Path $LogPath "cleanup_windows.log"

# Log dizinini oluştur
if (!(Test-Path $LogPath)) {
    New-Item -ItemType Directory -Path $LogPath -Force
}

# Tarih ve saat bilgisi
$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Add-Content -Path $LogFile -Value "$Timestamp : Windows geçici dosya temizleme başlatılıyor..."

try {
    # Virtual environment'ı aktifleştir (eğer varsa)
    $VenvPath = Join-Path $ProjectPath "venv\Scripts\Activate.ps1"
    if (Test-Path $VenvPath) {
        & $VenvPath
        Add-Content -Path $LogFile -Value "$Timestamp : Virtual environment aktifleştirildi"
    }

    # Geçici dosyaları temizle
    $CleanupArgs = @("manage.py", "cleanup_temp_files", "--days", $Days)
    
    if ($IncludeOrphaned) {
        $CleanupArgs += "--include-orphaned"
    }
    
    if ($DryRun) {
        $CleanupArgs += "--dry-run"
    }

    $Result = & python $CleanupArgs 2>&1
    Add-Content -Path $LogFile -Value $Result

    # Dosya yedeklerini temizle (30 günden eski)
    if (!$DryRun) {
        $BackupResult = & python "manage.py" "cleanup_file_backups" "--days" "30" 2>&1
        Add-Content -Path $LogFile -Value $BackupResult
    }

    Add-Content -Path $LogFile -Value "$Timestamp : Temizleme başarıyla tamamlandı"

} catch {
    $ErrorMsg = "$Timestamp : Hata oluştu: $($_.Exception.Message)"
    Add-Content -Path $LogFile -Value $ErrorMsg
    Write-Error $ErrorMsg
    exit 1
}

# Log dosyasını döndür (çok büyürse)
if ((Get-Content $LogFile | Measure-Object -Line).Lines -gt 1000) {
    Get-Content $LogFile | Select-Object -Last 500 | Set-Content $LogFile
}

Add-Content -Path $LogFile -Value "----------------------------------------"
