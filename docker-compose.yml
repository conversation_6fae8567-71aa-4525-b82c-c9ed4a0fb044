version: '3.8'

services:
  db:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=kupcadisi
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    container_name: kupcadisi-db

  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - DEBUG=1
      - POSTGRES_NAME=kupcadisi
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
    restart: unless-stopped
    container_name: kupcadisi-web
    depends_on:
      - db

volumes:
  postgres_data:
