# Test Dosyaları

B<PERSON> klasör, projenin çeşitli sistemlerini test etmek için kullanılan yardımcı script'leri içerir.

## Test Dosyaları

### `test_field_mapping.py`
**Amaç**: Field mapping sistemini test eder
**Kullanım**: 
```bash
python tests/test_field_mapping.py
```
**Test Ettiği Özellikler**:
- Hakkimda modelindeki field mapping'lerin doğruluğu
- Geçerli/geçersiz mapping'lerin tespiti
- Model alanlar<PERSON>na değer atama/okuma işlemleri

### `test_file_management.py`
**Amaç**: Dosya yönetimi sistemini test eder
**Kullanım**:
```bash
python tests/test_file_management.py
```
**Test Ettiği Özellikler**:
- Güvenli dosya silme işlemleri
- Dosya taşıma işlemleri
- <PERSON><PERSON> dosya oluşturma
- Hata durumlarının yönetimi

### `test_logging.py`
**Amaç**: Logging sistemini test eder
**Kullanım**:
```bash
python tests/test_logging.py
```
**Test Ettiği Özellikler**:
- Farklı log seviyelerinin çalışması
- Structured logging
- Performance logging
- User action logging
- Exception logging

## Kullanım Notları

1. **Django Environment**: Tüm test dosyaları Django environment'ını otomatik olarak yükler
2. **Veritabanı**: Testler mevcut veritabanını kullanır, test verileri oluşturabilir
3. **Log Dosyaları**: Logging testleri `logs/` klasöründe dosyalar oluşturur
4. **Geçici Dosyalar**: File management testleri geçici dosyalar oluşturur ve temizler

## Test Çıktıları

Testler başarılı/başarısız durumları şu şekilde gösterir:
- ✓ Başarılı işlemler
- ✗ Başarısız işlemler
- ? Belirsiz/atlanmış işlemler

## Geliştirme Notları

Bu test dosyaları:
- Production ortamında çalıştırılmamalıdır
- Geliştirme ve debug amaçlıdır
- Sistem bileşenlerinin doğru çalıştığını doğrulamak için kullanılır
- Yeni özellikler eklendiğinde güncellenmelidir
