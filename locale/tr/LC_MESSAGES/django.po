msgid ""
msgstr ""
"Project-Id-Version: <PERSON><PERSON><PERSON>\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-08 14:50+0300\n"
"PO-Revision-Date: 2025-06-08 14:50+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: tr\n"
"Language-Team: Turkish\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: urunler/apps.py:9
msgid "<PERSON>rün Yönetim<PERSON>"
msgstr ""

#: urunler/apps.py:20
msgid "Etiket"
msgstr ""

#: urunler/apps.py:21
msgid "Etiketler"
msgstr ""

#: urunler/apps.py:22
msgid "Etiketlenmiş Öğe"
msgstr ""

#: urunler/apps.py:23
msgid "Etiketlenmiş Öğeler"
msgstr ""

#: urunler/admin.py:12
msgid "Tarihler"
msgstr ""

#: urunler/admin.py:45
msgid "Etiketler"
msgstr ""

#: urunler/admin.py:47
msgid "Etiketleri virgülle ayırarak girin. Örn: ahşap, el yapımı, vintage"
msgstr ""

#: urunler/admin.py:77
msgid "Etiketleri renkli rozetler olarak göster"
msgstr ""

#: urunler/admin.py:100
msgid "Etiket Adı"
msgstr ""

#: urunler/admin.py:101
msgid "URL Uzantısı"
msgstr ""

#: urunler/admin.py:108
msgid "Seçili etiketleri birleştir"
msgstr ""

#: urunler/admin.py:135
msgid "Etiketlerin kullanım sayısını hesapla"
msgstr ""

#: urunler/admin.py:141
msgid "Etiketin kaç üründe kullanıldığını göster"
msgstr ""

#: urunler/admin.py:147
msgid "Ürün Sayısı"
msgstr ""

#: urunler/admin.py:152
msgid "Etiket için hızlı eylemler"
msgstr ""

#: urunler/admin.py:164
msgid "Seçili etiketleri birleştir"
msgstr ""

#: urunler/admin.py:165
msgid "Birleştirmek için en az 2 etiket seçmelisiniz."
msgstr ""

#: urunler/admin.py:196
msgid "Etiket istatistiklerini hesapla ve şablona gönder"
msgstr ""

#: urunler/admin.py:246
msgid "Etiket detay sayfasını özelleştir"
msgstr ""

#: urunler/admin.py:248
msgid "Bu etiketi kullanan ürünleri bul"
msgstr ""

#: urunler/admin.py:250
msgid "Benzer etiketleri bul (isim benzerliği)"
msgstr ""

#: urunler/admin.py:255
msgid "Etiket adının parçalarını al"
msgstr ""

#: urunler/admin.py:260
msgid "Her parça için benzer etiketleri ara"
msgstr ""

#: urunler/admin.py:264
msgid "Çok kısa parçaları atla"
msgstr ""

#: urunler/admin.py:267
msgid "Kendisi hariç benzer etiketleri bul"
msgstr ""
