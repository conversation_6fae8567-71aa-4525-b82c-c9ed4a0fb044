# 🔐 Küp Cadısı - Yönetici İzin Sistemi Kılavuzu

Bu kılavuz, site yöneticilerinin kullanıcı izinlerini nasıl yöneteceğini açıklar. **Programlama bilgisi gerektirmez.**

## 📋 İzin Grupları Nedir?

İzin grupları, kullanıcıların sitede hangi işlemleri yapabileceğini belirler. Her grup belirli yetkiler içerir.

### 🎯 Mevcut İzin Grupları

#### 📝 **İçerik Editörü** (En Geniş Yetki)
**Kim olmalı:** Site içeriğinden tamamen sorumlu kişiler
**Yapabilecekleri:**
- ✅ Ürün ekleme, düzenleme, silme
- ✅ Ürün resimlerini yönetme
- ✅ Video ekleme, düzenleme, silme
- ✅ Kategori oluşturma ve düzenleme
- ✅ Tüm içerik türlerini yönetme

**Örnek:** Site editörü, içerik sorumlusu

---

#### 🎬 **Video Yöneticisi** (Sadece Video)
**Kim olmalı:** Video içeriklerinden sorumlu kişiler
**Yapabilecekleri:**
- ✅ "Nasıl Yapılır" videoları ekleme
- ✅ Video düzenleme ve silme
- ❌ Ürün yönetimi yapamaz
- ❌ Çalışma yönetimi yapamaz

**Örnek:** Video editörü, YouTube sorumlusu

---

#### 🛍️ **Ürün Yöneticisi** (Sadece Ürün)
**Kim olmalı:** Ürün katalogunu yöneten kişiler
**Yapabilecekleri:**
- ✅ Ürün ekleme, düzenleme, silme
- ✅ Ürün resimlerini yönetme
- ✅ Ürün kategorilerini yönetme
- ❌ Video yönetimi yapamaz
- ❌ Çalışma yönetimi yapamaz

**Örnek:** Ürün sorumlusu, katalog yöneticisi

---

#### 🎨 **Çalışma Yöneticisi** (Kullanıcı Çalışmaları)
**Kim olmalı:** Kullanıcı çalışmalarını moderasyona tabi tutan kişiler
**Yapabilecekleri:**
- ✅ Kullanıcı çalışmalarını görüntüleme
- ✅ Çalışma ekleme, düzenleme, silme
- ✅ Çalışma fotoğraflarını yönetme
- ❌ Ürün yönetimi yapamaz
- ❌ Video yönetimi yapamaz

**Örnek:** Topluluk moderatörü, çalışma sorumlusu

---

#### 💬 **İçerik Moderatörü** (Yorum ve Moderasyon)
**Kim olmalı:** Yorumları ve kullanıcı içeriklerini moderasyona tabi tutan kişiler
**Yapabilecekleri:**
- ✅ Yorumları onaylama ve silme
- ✅ Kullanıcı çalışmalarını moderasyona tabi tutma
- ✅ İçerik denetimi
- ❌ Yeni içerik oluşturamaz

**Örnek:** Moderatör, topluluk yöneticisi

---

#### 👀 **İçerik Görüntüleyici** (Sadece Okuma)
**Kim olmalı:** Sadece içerikleri görüntülemesi gereken kişiler
**Yapabilecekleri:**
- ✅ Tüm içerikleri görüntüleme
- ❌ Hiçbir değişiklik yapamaz
- ❌ İçerik ekleyemez, düzenleyemez

**Örnek:** Misafir editör, denetim sorumlusu

## 🚀 Kullanıcılara İzin Verme Adımları

### 1. Adım: Admin Paneline Giriş
1. Tarayıcınızda `http://siteniz.com/admin/` adresine gidin
2. Yönetici hesabınızla giriş yapın

### 2. Adım: Kullanıcı Bulma
1. Sol menüden **"Kullanıcılar"** seçeneğine tıklayın
2. İzin vermek istediğiniz kullanıcıyı bulun
3. Kullanıcı adına tıklayın

### 3. Adım: Grup Atama
1. Kullanıcı düzenleme sayfasında **"Gruplar"** bölümünü bulun
2. **"Mevcut gruplar"** listesinden uygun grubu seçin
3. Sağ oka (→) tıklayarak **"Seçilen gruplar"** listesine taşıyın
4. Sayfanın altındaki **"Kaydet"** butonuna tıklayın

### 4. Adım: Kontrol
1. Kullanıcının profil sayfasında **"İzin Özeti"** bölümünü kontrol edin
2. Doğru grup ve izin sayısının göründüğünden emin olun

## ⚠️ Önemli Güvenlik Notları

### 🔴 Dikkat Edilmesi Gerekenler:
- **Süper Kullanıcı** yetkisi sadece teknik ekibe verilmeli
- **İçerik Editörü** yetkisi sadece güvenilir kişilere verilmeli
- Kullanıcıları **en az yetki** prensibiyle yetkilendirin
- Düzenli olarak kullanıcı izinlerini gözden geçirin

### 🟡 İyi Uygulamalar:
- Yeni kullanıcılara önce **İçerik Görüntüleyici** yetkisi verin
- Deneme süresinden sonra gerekli yetkiyi verin
- Ayrılan çalışanların izinlerini hemen kaldırın
- İzin değişikliklerini kayıt altına alın

## 🔧 Sorun Giderme

### Kullanıcı İzin Alamıyor
1. Kullanıcının doğru gruba atandığından emin olun
2. Kullanıcının hesabının aktif olduğunu kontrol edin
3. Tarayıcı önbelleğini temizleyin
4. Kullanıcıdan çıkış yapıp tekrar giriş yapmasını isteyin

### Grup Görünmüyor
1. İzin gruplarının kurulu olduğundan emin olun
2. Terminal'de şu komutu çalıştırın: `python manage.py setup_permissions`
3. Admin panelini yenileyin

### İzin Çalışmıyor
1. Kullanıcının birden fazla grubu varsa çakışma olabilir
2. Süper kullanıcı dışındaki hesaplarla test edin
3. Gerekirse teknik ekiple iletişime geçin

## 📞 Destek

Teknik sorunlar için:
- **E-posta:** <EMAIL>
- **Telefon:** 0XXX XXX XX XX

Bu kılavuzla ilgili sorularınız için site yöneticisiyle iletişime geçin.

---
*Bu kılavuz, Küp Cadısı İzin Sistemi v1.0 için hazırlanmıştır.*
