/**
 * Service Worker for Atolye PWA
 * Provides offline support and caching
 */

const CACHE_NAME = 'atolye-v1.0.0';
const STATIC_CACHE = 'atolye-static-v1';
const DYNAMIC_CACHE = 'atolye-dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
    '/static/assets/css/main.css',
    '/static/assets/css/main.css',
    '/static/assets/vendor/bootstrap/css/bootstrap.min.css',
    '/static/assets/vendor/bootstrap-icons/bootstrap-icons.css',
    '/static/assets/img/favicon/favicon.ico',
    '/static/assets/img/favicon/favicon-32x32.png',
    '/static/assets/img/favicon/favicon-16x16.png'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .catch((error) => {
                console.error('Service Worker: Failed to cache static files', error);
            })
    );
    
    // Force activation
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // Take control of all pages
    self.clients.claim();
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests
    if (url.origin !== location.origin) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }
                
                // Otherwise fetch from network
                return fetch(request)
                    .then((networkResponse) => {
                        // Don't cache if not successful
                        if (!networkResponse || networkResponse.status !== 200) {
                            return networkResponse;
                        }
                        
                        // Clone response for caching
                        const responseToCache = networkResponse.clone();
                        
                        // Determine cache strategy
                        if (shouldCacheDynamically(request)) {
                            caches.open(DYNAMIC_CACHE)
                                .then((cache) => {
                                    cache.put(request, responseToCache);
                                });
                        }
                        
                        return networkResponse;
                    })
                    .catch(() => {
                        // Return offline fallback for HTML pages
                        if (request.headers.get('accept').includes('text/html')) {
                            return caches.match('/offline.html') || 
                                   new Response('Offline - İnternet bağlantınızı kontrol edin', {
                                       status: 200,
                                       headers: { 'Content-Type': 'text/html' }
                                   });
                        }
                        
                        // Return placeholder for images
                        if (request.headers.get('accept').includes('image')) {
                            return new Response('', {
                                status: 200,
                                headers: { 'Content-Type': 'image/svg+xml' }
                            });
                        }
                    });
            })
    );
});

// Determine if request should be cached dynamically
function shouldCacheDynamically(request) {
    const url = new URL(request.url);
    
    // Cache atolye pages and API responses
    if (url.pathname.startsWith('/atolye/')) {
        return true;
    }
    
    // Cache images
    if (request.headers.get('accept').includes('image')) {
        return true;
    }
    
    // Cache CSS and JS files
    if (url.pathname.includes('.css') || url.pathname.includes('.js')) {
        return true;
    }
    
    return false;
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    // Handle offline actions when back online
    console.log('Service Worker: Performing background sync');
    
    // You can implement offline like/share actions here
    // For now, just log that sync happened
}

// Push notification handling
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push received', event);
    
    const options = {
        body: event.data ? event.data.text() : 'Yeni atolye içeriği!',
        icon: '/static/assets/img/favicon/favicon-32x32.png',
        badge: '/static/assets/img/favicon/favicon-16x16.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'Görüntüle',
                icon: '/static/assets/img/favicon/favicon-16x16.png'
            },
            {
                action: 'close',
                title: 'Kapat',
                icon: '/static/assets/img/favicon/favicon-16x16.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('Küp Cadısı Atolye', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // Open atolye page
        event.waitUntil(
            clients.openWindow('/atolye/')
        );
    } else if (event.action === 'close') {
        // Just close notification
        return;
    } else {
        // Default action - open app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

console.log('Service Worker: Loaded successfully');
