/**
 * <PERSON><PERSON><PERSON> Design System
 * Tutarlı tasarım bileşenleri ve utility classes
 * Son güncelleme: 2025-06-14
 */

/*--------------------------------------------------------------
# Design System - Utility Classes
--------------------------------------------------------------*/

/* Spacing Utilities */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-xxs) !important; }
.p-2 { padding: var(--space-xs) !important; }
.p-3 { padding: var(--space-sm) !important; }
.p-4 { padding: var(--space-md) !important; }
.p-5 { padding: var(--space-lg) !important; }
.p-6 { padding: var(--space-xl) !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-xxs) !important; }
.m-2 { margin: var(--space-xs) !important; }
.m-3 { margin: var(--space-sm) !important; }
.m-4 { margin: var(--space-md) !important; }
.m-5 { margin: var(--space-lg) !important; }
.m-6 { margin: var(--space-xl) !important; }

/* Typography Utilities */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-md { font-size: var(--font-size-md) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-xxl { font-size: var(--font-size-xxl) !important; }

.font-light { font-weight: var(--font-weight-light) !important; }
.font-regular { font-weight: var(--font-weight-regular) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

.font-heading { font-family: var(--heading-font) !important; }
.font-body { font-family: var(--body-font) !important; }

/* Color Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-soft { color: var(--text-soft) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-accent { background-color: var(--accent-color) !important; }
.bg-surface { background-color: var(--surface-color) !important; }
.bg-soft { background-color: var(--soft-light) !important; }

/*--------------------------------------------------------------
# Modern Card System
--------------------------------------------------------------*/
.card-modern {
    background: var(--surface-color) !important;
    border-radius: var(--border-radius-lg) !important;
    box-shadow: 0 2px 12px rgba(var(--color-brown-rgb), 0.08) !important;
    border: 1px solid rgba(var(--color-brown-rgb), 0.06) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    overflow: hidden !important;
}

.card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
    border-color: rgba(var(--color-brown-rgb), 0.12);
}

.card-modern .card-header {
    background: linear-gradient(135deg, 
        rgba(var(--color-brown-rgb), 0.03), 
        rgba(var(--color-navy-rgb), 0.03));
    border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.08);
    padding: var(--space-lg);
}

.card-modern .card-body {
    padding: var(--space-lg);
}

.card-modern .card-footer {
    background: rgba(var(--color-brown-rgb), 0.02);
    border-top: 1px solid rgba(var(--color-brown-rgb), 0.06);
    padding: var(--space-md) var(--space-lg);
}

/* Card Variants */
.card-elevated {
    box-shadow: 0 8px 32px rgba(var(--color-brown-rgb), 0.12);
}

.card-flat {
    box-shadow: none;
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.card-gradient {
    background: linear-gradient(135deg, 
        var(--surface-color), 
        rgba(var(--color-sand-rgb), 0.1));
}

/*--------------------------------------------------------------
# Modern Button System
--------------------------------------------------------------*/
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--border-radius-md);
    font-family: var(--heading-font);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-modern:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover:before {
    left: 100%;
}

/* Button Variants */
.btn-primary-modern {
    background: linear-gradient(135deg, var(--primary-color), var(--color-brown));
    color: var(--contrast-color);
    box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.3);
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--color-brown-rgb), 0.4);
    color: var(--contrast-color);
}

.btn-secondary-modern {
    background: linear-gradient(135deg, var(--secondary-color), var(--color-navy));
    color: var(--contrast-color);
    box-shadow: 0 4px 12px rgba(var(--color-navy-rgb), 0.3);
}

.btn-secondary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--color-navy-rgb), 0.4);
    color: var(--contrast-color);
}

.btn-outline-modern {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(var(--color-brown-rgb), 0.1);
}

.btn-outline-modern:hover {
    background: var(--primary-color);
    color: var(--contrast-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(var(--color-brown-rgb), 0.3);
}

.btn-ghost-modern {
    background: rgba(var(--color-brown-rgb), 0.08);
    color: var(--primary-color);
    border: 1px solid rgba(var(--color-brown-rgb), 0.15);
}

.btn-ghost-modern:hover {
    background: rgba(var(--color-brown-rgb), 0.15);
    transform: translateY(-1px);
    color: var(--primary-color);
}

/* Button Sizes */
.btn-sm-modern {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
}

.btn-lg-modern {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-lg);
}

/*--------------------------------------------------------------
# Modern Form Elements
--------------------------------------------------------------*/
.form-modern {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}



.form-label-modern {
    font-family: var(--heading-font);
    font-weight: var(--font-weight-medium);
    color: var(--heading-color);
    font-size: var(--font-size-sm);
}

.form-input-modern {
    padding: var(--space-sm) var(--space-md);
    border: 2px solid rgba(var(--color-brown-rgb), 0.15);
    border-radius: var(--border-radius-md);
    background: var(--surface-color);
    font-family: var(--body-font);
    font-size: var(--font-size-md);
    color: var(--default-color);
    transition: all 0.3s ease;
}

.form-input-modern:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(var(--color-moss-rgb), 0.1);
    background: var(--white);
}

.form-input-modern::placeholder {
    color: var(--text-muted);
}

/*--------------------------------------------------------------
# Modern Grid System
--------------------------------------------------------------*/
.grid-modern {
    display: grid;
    gap: var(--space-lg);
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .grid-3, .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/*--------------------------------------------------------------
# Modern Animations
--------------------------------------------------------------*/
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
    animation: fadeInScale 0.4s ease-out;
}

/*--------------------------------------------------------------
# Modern Shadows
--------------------------------------------------------------*/
.shadow-soft {
    box-shadow: 0 2px 12px rgba(var(--color-brown-rgb), 0.08);
}

.shadow-medium {
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.12);
}

.shadow-strong {
    box-shadow: 0 8px 32px rgba(var(--color-brown-rgb), 0.16);
}

.shadow-colored {
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.2);
}

/*--------------------------------------------------------------
# Modern Borders
--------------------------------------------------------------*/
.border-soft {
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.border-medium {
    border: 1px solid rgba(var(--color-brown-rgb), 0.2);
}

.border-accent {
    border: 2px solid var(--accent-color);
}

.border-radius-sm { border-radius: var(--border-radius-sm); }
.border-radius-md { border-radius: var(--border-radius-md); }
.border-radius-lg { border-radius: var(--border-radius-lg); }
.border-radius-xl { border-radius: var(--border-radius-xl); }

/*--------------------------------------------------------------
# Modern Layout Utilities
--------------------------------------------------------------*/
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.flex-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.gap-1 { gap: var(--space-xxs); }
.gap-2 { gap: var(--space-xs); }
.gap-3 { gap: var(--space-sm); }
.gap-4 { gap: var(--space-md); }
.gap-5 { gap: var(--space-lg); }
.gap-6 { gap: var(--space-xl); }
