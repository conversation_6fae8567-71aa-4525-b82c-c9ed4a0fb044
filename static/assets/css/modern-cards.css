/**
 * Modern Card Components - Unified Card System
 * Tutarlı kart tasarımları tüm uygulamalar için
 * Proje genelinde standardize edilmiş kart sistemi
 */

/* =============================================
   # TEMEL KART YAPISI - UNIFIED SYSTEM
   ============================================= */

/* Ana Kart Sınıfı - Tüm uygulamalarda kullanılacak */
.card,
.modern-card {
    /* Temel kart özellikleri */
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    box-shadow: 0 2px 12px rgba(var(--color-brown-rgb), 0.08);
    height: auto;
    display: flex;
    flex-direction: column;
    margin-bottom: var(--space-md);
    position: relative;
}

.card:hover,
.modern-card:hover {
    box-shadow: 0 8px 32px rgba(var(--color-brown-rgb), 0.15);
    transform: translateY(-4px);
    border-color: rgba(var(--color-brown-rgb), 0.2);
}

/* Kartlardaki yeşil çizgi efektini kaldır */
.modern-card::before,
.modern-card::after {
    content: none !important;
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    background: none !important;
}

/* Kartın kendi stillerini koru */
.modern-card {
    background: var(--surface-color) !important;
    border-radius: var(--border-radius-lg) !important;
    border: 1px solid rgba(var(--color-brown-rgb), 0.15) !important;
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.15) !important;
    transition: all 0.3s ease !important;
}

.modern-card:hover {
    box-shadow: 0 12px 40px rgba(var(--color-brown-rgb), 0.25) !important;
    transform: translateY(-6px) !important;
    border-color: rgba(var(--color-brown-rgb), 0.25) !important;
}

/* Kart Bileşenleri - Unified System - Kompakt */
.card__header,
.modern-card-header {
    padding: var(--space-md);
    border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.2);
    background: linear-gradient(135deg,
        rgba(var(--color-brown-rgb), 0.08),
        rgba(var(--color-sand-rgb), 0.12));
}

.card__body,
.modern-card-body {
    padding: var(--space-md);
    flex: 1;
}

.card__footer,
.modern-card-footer {
    padding: var(--space-sm) var(--space-md);
    border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
    background: rgba(var(--color-brown-rgb), 0.02);
}

.card__title,
.modern-card-header h3 {
    font-family: var(--heading-font);
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-brown);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card__title i,
.modern-card-header h3 i {
    color: var(--color-brown);
    background-color: rgba(var(--color-brown-rgb), 0.15);
    border-radius: 50%;
    padding: 6px;
    width: 28px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.card__subtitle {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0 0 1rem;
}

.card__text {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Kart Görsel Alanı */
.card__image {
    position: relative;
    overflow: hidden;
    width: 100%;
}

.card__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.card:hover .card__image img {
    transform: scale(1.05);
}

/* Kart Üstü Kaplama (Overlay) */
.card__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--color-brown-rgb), 0.08),
        rgba(var(--color-navy-rgb), 0.08)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.card:hover .card__overlay {
    opacity: 1;
}

/* Kart Aksiyon Butonları */
.card__actions {
    display: flex;
    gap: var(--space-xs);
    margin-top: auto; /* Footer'da değilse, body'nin en altına iter */
    flex-wrap: wrap;
}

.card__btn {
    padding: var(--space-xs) var(--space-md);
    border: none;
    border-radius: var(--border-radius-md);
    background: var(--color-brown);
    color: var(--soft-white);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.card__btn:hover {
    background: var(--color-navy);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--color-brown-rgb), 0.3);
    color: var(--soft-white);
}

/* Kart Badge/Etiket */
.card__badge {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    background: var(--color-brown);
    color: var(--soft-white);
    padding: var(--space-xxs) var(--space-xs);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    z-index: 2;
}

/* Tag Cloud System */
.modern-tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
    margin-bottom: 0;
}

.modern-tag {
    display: inline-block;
    padding: 6px 14px;
    background-color: rgba(var(--color-moss-rgb), 0.15);
    color: var(--color-moss);
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(var(--color-moss-rgb), 0.2);
}

.modern-tag:hover {
    background-color: var(--color-moss);
    color: var(--soft-white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--color-moss-rgb), 0.3);
}

.modern-tag.active {
    background-color: var(--color-moss);
    color: var(--soft-white);
    border-color: var(--color-moss);
}

/* Autumn themed tags */
.autumn-tag {
    background-color: rgba(var(--color-brown-rgb), 0.15);
    color: var(--color-brown);
    border: 1px solid rgba(var(--color-brown-rgb), 0.2);
}

.autumn-tag:hover {
    background-color: var(--color-brown);
    color: var(--soft-white);
    border-color: var(--color-brown);
}

/* Detay Sayfaları İçin Kompakt Tasarım */
.calisma-bilgi-karti .card__body,
.urun-bilgi-karti .card__body {
    padding: var(--space-sm);
}

.etiketler-karti .card__body {
    padding: var(--space-sm);
}

.benzer-calismalar-karti .card__body,
.benzer-urunler-karti .card__body {
    padding: var(--space-sm);
}

.admin-panel-card .card__body {
    padding: 0;
}

/* Responsive Kart Düzenlemeleri */
@media (max-width: 768px) {
    .card,
    .modern-card {
        margin-bottom: var(--space-sm);
    }

    .card__header,
    .card__body {
        padding: var(--space-sm);
    }
}

/* =============================================
   # KART VARYASYONLARI
   ============================================= */

/* Ürün Listesi Kartları */
.product-card {
    background: var(--surface-color) !important;
    border-radius: var(--border-radius-lg) !important;
    border: 1px solid rgba(var(--color-brown-rgb), 0.15) !important;
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.15) !important;
    transition: all 0.3s ease !important;
    overflow: hidden;
}

.product-card:hover {
    box-shadow: 0 12px 40px rgba(var(--color-brown-rgb), 0.25) !important;
    transform: translateY(-6px) !important;
    border-color: rgba(var(--color-brown-rgb), 0.25) !important;
}

/* Ürün Kartı & Çalışma Kartı (Benzer stiller) */
.card--product,
.card--work {
    position: relative;
    border: 1px solid rgba(var(--color-brown-rgb), 0.15) !important;
    border-radius: var(--border-radius-lg) !important;
    overflow: hidden;
    transition: all 0.3s ease !important;
    background: var(--surface-color) !important;
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.15) !important;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card--product:hover,
.card--work:hover {
    transform: translateY(-6px) !important;
    box-shadow: 0 12px 40px rgba(var(--color-brown-rgb), 0.25) !important;
    border-color: rgba(var(--color-brown-rgb), 0.25) !important;
}

.card--product .card__image {
    aspect-ratio: 1;
}

.card--work .card__image {
    aspect-ratio: 4/3;
}

.card--product .card__price {
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--accent-color);
    margin: 1rem 0;
}

/* Blog Kartı */
.card--blog {
    border: 1px solid rgba(var(--color-navy-rgb), 0.1);
}

.card--blog .card__image {
    aspect-ratio: 16/9;
}

.card--blog .card__meta {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.75rem;
}

/* Video Kartı */
.card--video {
    border: 1px solid rgba(var(--color-red-rgb), 0.1);
}

.card--video .card__image {
    aspect-ratio: 16/9;
    position: relative;
    background: var(--color-navy);
}

.card--video .card__play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(var(--color-brown-rgb), 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--accent-color);
    font-size: 1.5rem;
    transition: all 0.3s ease;
    z-index: 1;
}

.card--video:hover .card__play-button {
    transform: translate(-50%, -50%) scale(1.1);
    background: var(--accent-color);
    color: white;
}

/* Testimonial Kartı */
.card--testimonial {
    text-align: center;
    padding: 2rem;
    border: 1px solid rgba(var(--color-gold-rgb), 0.1);
}

.card--testimonial .card__avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    overflow: hidden;
    border: 3px solid var(--color-gold);
}

.card--testimonial .card__quote {
    font-style: italic;
    margin-bottom: 1.5rem;
    position: relative;
}

.card--testimonial .card__author {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.card--testimonial .card__position {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* =============================================
   # DURUM SINIFLARI
   ============================================= */

.card--featured {
    border: 2px solid var(--accent-color);
}

.card--disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* =============================================
   # RESPONSIVE DÜZENLEMELER
   ============================================= */

@media (max-width: 768px) {
    .card__body,
    .card__header,
    .card__footer {
        padding: 1rem;
    }

    .card__title {
        font-size: 1.1rem;
    }
}
