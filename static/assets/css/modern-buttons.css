/*!
 * MODERN BUTON STYLLERI (BEM Yapısı)
 * Küp Cadısı projesi için modern ve erişilebilir buton tasarımları
 * BEM metodolojisi kullanılarak yeniden düzenlenmiştir
 *
 * Kullanım:
 * <button class="button">Temel Buton</button>
 * <button class="button button--primary">Birincil <PERSON>on</button>
 * <button class="button button--secondary">İkincil Buton</button>
 * <button class="button button--outline">Çerçeveli Buton</button>
 * <button class="button button--ghost">Şeffaf Buton</button>
 * <button class="button button--link">Bağlantı Butonu</button>
 * <button class="button button--danger"><PERSON><PERSON><PERSON></button>
 * <button class="button button--success">Başarı Butonu</button>
 *
 * Boyutlar:
 * <button class="button button--small">Küçük Buton</button>
 * <button class="button">Varsayılan Buton</button>
 * <button class="button button--large">Büyük Buton</button>
 *
 * İkonlu Butonlar:
 * <button class="button">
 *   <span class="button__icon">
 *     <i class="fas fa-plus"></i>
 *   </span>
 *   <span class="button__text">İkonlu Buton</span>
 * </button>
 *
 * İkon Sağda:
 * <button class="button button--icon-right">
 *   <span class="button__text">İkon Sağda</span>
 *   <span class="button__icon">
 *     <i class="fas fa-arrow-right"></i>
 *   </span>
 * </button>
 *
 * Yükleme Durumu:
 * <button class="button button--loading">Yükleniyor...</button>
 *
 * Devre Dışı Durum:
 * <button class="button" disabled>Devre Dışı</button>
 */

@import url('./_variables.css');

/* TEMEL BUTON YAPISI
   ========================================================================== */

/**
 * 1. Duyarlı tıklama alanı sağlar
 * 2. Erişilebilirlik için yeterli kontrast
 * 3. Yumuşak geçişler için tutarlı animasyon
 */
.button {
  /* Temel düzen */
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 0.75rem 1.5rem;
  min-width: 120px;
  
  /* Tipografi */
  font-family: var(--heading-font);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  
  /* Görünüm */
  border: none;
  border-radius: var(--border-radius-md);
  background: transparent;
  color: var(--text-color);
  cursor: pointer;
  user-select: none;
  
  /* Etkileşim */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-tap-highlight-color: transparent;
  
  /* Erişilebilirlik */
  overflow: hidden;
}

/* Fokus durumu */
.button:focus {
  outline: 2px solid rgba(var(--color-brown-rgb), 0.5);
  outline-offset: 2px;
}

/* Devre dışı durumu */
.button:disabled,
.button.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  box-shadow: none;
  transform: none !important;
}

/* BOYUTLAR
   ========================================================================== */

.button--small {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-sm);
  min-width: 100px;
}

.button--large {
  padding: 1rem 2rem;
  font-size: var(--font-size-lg);
  min-width: 140px;
}

/* RENK VARYASYONLARI
   ========================================================================== */

/* PRIMARY BUTTON */
.button--primary {
  background: linear-gradient(135deg, var(--color-brown) 0%, rgba(var(--color-brown-rgb), 0.8) 100%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.3);
}

.button--primary:hover:not(:disabled):not(.is-disabled) {
  background: linear-gradient(135deg, rgba(var(--color-brown-rgb), 0.9) 0%, var(--color-brown) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.4);
  color: var(--white);
}

.button--primary:active:not(:disabled):not(.is-disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(var(--color-brown-rgb), 0.3);
}

/* SECONDARY BUTTON */
.button--secondary {
  background-color: var(--secondary-color);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(var(--secondary-color-rgb), 0.2);
}

.button--secondary:hover:not(:disabled):not(.is-disabled) {
  background-color: var(--secondary-color-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--secondary-color-rgb), 0.3);
  color: var(--white);
}

.button--secondary:active:not(:disabled):not(.is-disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(var(--secondary-color-rgb), 0.2);
}

/* OUTLINE BUTTON */
.button--outline {
  background: transparent;
  border: 2px solid var(--color-brown);
  color: var(--color-brown);
  box-shadow: none;
}

.button--outline:hover:not(:disabled):not(.is-disabled) {
  background: rgba(var(--color-brown-rgb), 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
}

.button--outline:active:not(:disabled):not(.is-disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(var(--color-brown-rgb), 0.1);
}

/* GHOST BUTTON */
.button--ghost {
  background: transparent;
  color: var(--color-brown);
  box-shadow: none;
  border: 2px solid transparent;
}

.button--ghost:hover:not(:disabled):not(.is-disabled) {
  background: rgba(var(--color-brown-rgb), 0.05);
  transform: translateY(-2px);
}

.button--ghost:active:not(:disabled):not(.is-disabled) {
  transform: translateY(0);
  background: rgba(var(--color-brown-rgb), 0.1);
}

/* LINK BUTTON */
.button--link {
  background: transparent;
  color: var(--color-brown);
  text-decoration: underline;
  box-shadow: none;
  min-width: auto;
  padding: 0.25rem 0.5rem;
}

.button--link:hover:not(:disabled):not(.is-disabled) {
  text-decoration: none;
  color: var(--color-brown-dark);
  background: transparent;
  transform: none;
  box-shadow: none;
}

/* BUTTON WITH ICON
   ========================================================================== */

.button__icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  font-size: 1.1em;
  transition: transform 0.3s ease;
}

.button:hover:not(:disabled):not(.is-disabled) .button__icon {
  transform: translateX(2px);
}

/* Button with icon on the right */
.button--icon-right .button__icon {
  margin-right: 0;
  margin-left: 0.5rem;
  order: 1;
}

/* LOADING STATE
   ========================================================================== */

.button--loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.button--loading::after {
  content: '';
  position: absolute;
  width: 1.25em;
  height: 1.25em;
  top: 50%;
  left: 50%;
  margin: -0.625em 0 0 -0.625em;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: button-spinner 0.75s linear infinite;
}

@keyframes button-spinner {
  to {
    transform: rotate(360deg);
  }
}

/* BUTTON GROUPS
   ========================================================================== */

.button-group {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
  list-style: none;
}

.button-group--vertical {
  flex-direction: column;
  align-items: flex-start;
}

.button-group--justified {
  display: flex;
  width: 100%;
}

.button-group--justified .button {
  flex: 1;
  min-width: auto;
}

/* RESPONSIVE ADJUSTMENTS
   ========================================================================== */

@media (max-width: 768px) {
  .button {
    padding: 0.625rem 1.25rem;
    font-size: calc(var(--font-size-md) * 0.9);
  }
  
  .button--small {
    padding: 0.375rem 0.75rem;
    font-size: calc(var(--font-size-sm) * 0.9);
  }
  
  .button--large {
    padding: 0.875rem 1.75rem;
    font-size: calc(var(--font-size-lg) * 0.95);
  }
}

/* UTILITY CLASSES
   ========================================================================== */

/* Full width button */
.button--block {
  display: flex;
  width: 100%;
}

/* Rounded button */
.button--rounded {
  border-radius: 100px;
}

/* No animation */
.button--no-animation {
  transition: none !important;
}

.button--no-uppercase {
  text-transform: none;
}

/* BAŞARILI BUTON */
.button--success {
  background: linear-gradient(135deg, var(--color-moss) 0%, rgba(var(--color-moss-rgb), 0.8) 100%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(var(--color-moss-rgb), 0.3);
}

.button--success:hover:not(:disabled):not(.is-disabled) {
  background: linear-gradient(135deg, rgba(var(--color-moss-rgb), 0.9) 0%, var(--color-moss) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--color-moss-rgb), 0.4);
  color: var(--white);
}

/* TEHLİKE BUTONU */
.button--danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: var(--white);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.button--danger:hover:not(:disabled):not(.is-disabled) {
  background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
  color: var(--white);
}

/* 3D EFEKTLİ BUTON */
.button--3d {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.1s ease, box-shadow 0.1s ease;
  box-shadow: 0 4px 0 var(--color-brown-dark), 0 5px 15px rgba(0, 0, 0, 0.2);
}

.button--3d:active:not(:disabled):not(.is-disabled) {
  transform: translateY(4px);
  box-shadow: 0 1px 0 var(--color-brown-dark), 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* PARILTILI BUTON */
.button--shimmer {
  position: relative;
  overflow: hidden;
}

.button--shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200% 100%;
  animation: button-shimmer 1.5s infinite;
}

@keyframes button-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* BUTON İÇİNDEKİ METİN */
.button__text {
  position: relative;
  z-index: 1;
}

/* BUTON İÇİN EKLENEN EKLER */
.button--full-width {
  display: flex;
  width: 100%;
  justify-content: center;
}

/* BUTON GRUPLARI İÇİN EK STİLLER */
.button-group--stretch {
  display: flex;
}

.button-group--stretch .button {
  flex: 1;
  text-align: center;
}

/* EKLENEN YENİ BUTON TÜRLERİ İÇİN STİLLER */
.button--social {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.button-group {
  display: inline-flex;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.button-group .button {
  border-radius: 0;
  margin: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.button-group .button:last-child {
  border-right: none;
}

.button-group .button:first-child {
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.button-group .button:last-child {
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .button {
    padding: 10px 20px;
    font-size: var(--font-size-sm);
    min-width: 100px;
  }
  
  .btn-lg-modern {
    padding: 14px 28px;
    font-size: var(--font-size-md);
  }
  
  .btn-fab {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
    font-size: 1.2rem;
  }
}
