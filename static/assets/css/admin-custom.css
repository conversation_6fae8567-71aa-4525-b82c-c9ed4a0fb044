/*
* ADMIN CUSTOM STYLES
* Admin panel i<PERSON><PERSON>
* Son güncelleme: 15.05.2025
*/

/* Admin base site stilleri */
#site-name a {
    display: flex;
    align-items: center;
}

.home-link {
    margin-left: 20px;
    background-color: var(--color-navy);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    transition: background-color 0.3s;
}

.home-link:hover {
    background-color: color-mix(in srgb, var(--color-navy), black 20%);
    color: white;
}

/* Tag admin stilleri */
.tag-info {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tag-info h2 {
    color: var(--color-navy);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
}

.tag-info h2 .tag-badge {
    display: inline-block;
    background-color: var(--color-moss);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    margin-left: 10px;
    font-size: 0.9rem;
}

.tag-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tag-stats h2 {
    color: var(--color-navy);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid var(--color-brown);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item h3 {
    margin: 0 0 5px 0;
    color: var(--color-brown);
    font-size: 1.1rem;
}

.stat-item .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--color-navy);
    margin: 10px 0;
}

.stat-item .stat-description {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

/* Tag liste stilleri */
.tag-list-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s;
}

.tag-list-item:hover {
    background-color: #f8f9fa;
}

.tag-name {
    font-weight: bold;
    color: var(--color-brown);
}

.tag-usage-count {
    color: var(--color-navy);
    font-size: 0.9rem;
}
