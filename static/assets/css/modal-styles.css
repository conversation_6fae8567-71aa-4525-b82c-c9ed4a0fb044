/*
* MODAL STYLES CSS
* Modal ve popup stilleri
* Son güncelleme: 15.05.2025
*/

/* Modal header gradyan stilleri */
.modal-header-gradient {
    background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%);
    color: white;
}

.modal-header-gradient .modal-title {
    color: white;
}

.modal-header-gradient .btn-close-white {
    filter: brightness(0) invert(1);
}

/* Eğitim modal stilleri */
.education-item {
    border-left: 4px solid var(--color-brown);
    transition: all 0.3s ease;
}

.education-item:hover {
    border-left-color: var(--color-sand);
    background-color: var(--soft-light);
}

.education-item .card-body {
    padding: 1.5rem;
}

.education-item .education-title {
    color: var(--color-brown);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.education-item .education-school {
    color: var(--color-navy);
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.education-item .education-date {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.education-item .education-actions {
    margin-top: 1rem;
}

.education-item .btn-edit {
    background-color: var(--color-sky);
    border-color: var(--color-sky);
    color: white;
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}

.education-item .btn-edit:hover {
    background-color: color-mix(in srgb, var(--color-sky), black 15%);
    border-color: color-mix(in srgb, var(--color-sky), black 15%);
}

.education-item .btn-delete {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}

.education-item .btn-delete:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Profil resmi stilleri */
.profile-image {
    width: 150px;
    height: 150px;
    object-fit: cover;
}

.profile-placeholder {
    width: 150px;
    height: 150px;
    background-color: #f0f0f0;
    margin: 0 auto;
}

.profile-placeholder i {
    font-size: 4rem;
    color: #aaa;
}

/* Timeline stilleri */
.timeline-date {
    background-color: var(--soft-light);
    color: var(--text-color);
    padding: 5px;
    border-radius: 3px;
}

/* Silme modal uyarı stilleri */
.modal-warning-text {
    margin-top: var(--space-md) !important;
}

/* Form inline stilleri */
.form-inline-style {
    display: inline;
}

/* Admin menü açıklama metinleri */
.admin-menu-description {
    font-size: var(--font-size-sm);
}

/* Tamamlanma durumu stilleri */
.completion-status {
    margin-top: var(--space-lg) !important;
}

.completion-status h5 {
    margin-bottom: var(--space-md) !important;
}

.progress-bar-custom {
    transition: width 0.6s ease;
}

/* Paylaşım butonları stilleri */
.modern-actions {
    margin-top: var(--space-lg) !important;
}

/* Sidebar içerik stilleri */
.sidebar-date {
    font-size: var(--font-size-sm);
}

.sidebar-empty {
    padding-top: var(--space-md) !important;
    padding-bottom: var(--space-md) !important;
}

.sidebar-empty p {
    margin-top: var(--space-xs) !important;
}

/* Admin menü açıklama metinleri için ek stil */
.admin-menu-description {
    font-size: var(--font-size-sm) !important;
}

/* Modal z-index düzeltmesi - Maksimum değer */
.modal,
.modal.fade,
.modal.fade.show,
.modal.show {
    z-index: 2147483647 !important; /* JavaScript'te maksimum z-index değeri */
}

.modal-backdrop,
.modal-backdrop.fade,
.modal-backdrop.fade.show,
.modal-backdrop.show {
    z-index: 2147483646 !important;
}

/* Silme modal'ları için özel z-index */
#deleteWorkModal,
#deleteProductModal,
#deleteVideoModal {
    z-index: 2147483647 !important;
}

#deleteWorkModal *,
#deleteProductModal *,
#deleteVideoModal * {
    z-index: inherit !important;
}

/* Modal dialog'un responsive davranışı */
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}

/* Modal'ın tıklanabilir olması için */
.modal-content {
    position: relative;
    z-index: 1;
    pointer-events: auto;
}

/* Modal'ın tıklanabilir olması için ek kurallar */
.modal-dialog {
    z-index: 2147483647 !important;
    position: relative;
}

.modal-content {
    z-index: 2147483647 !important;
    position: relative;
    pointer-events: auto !important;
}

/* Modal'ın görünür olması için */
.modal.show .modal-dialog {
    transform: none !important;
}

/* Backdrop opacity */
.modal-backdrop {
    opacity: 0.5 !important;
}
