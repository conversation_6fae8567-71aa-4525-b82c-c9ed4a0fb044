/**
 * Global Components CSS
 * Çalışmalar uygulamasından alınan güzel tasarım bileşenleri
 * Tüm uygulamalarda tutarlı kullanım için
 */

/*--------------------------------------------------------------
# Modern Filter Sections
--------------------------------------------------------------*/
.filter-section {
    background-color: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    margin-bottom: var(--space-lg);
    box-shadow: 0 2px 8px var(--shadow-color);
    border: 1px solid var(--border-color);
    height: 100%;
    transition: all 0.3s ease;
}

.filter-section:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
}

.filter-title {
    color: var(--heading-color);
    font-size: var(--font-size-md);
    font-family: var(--heading-font);
    margin-bottom: var(--space-sm);
    font-weight: var(--font-weight-semibold);
    display: flex;
    align-items: center;
    padding-bottom: var(--space-xs);
    border-bottom: 1px solid var(--border-color);
}

.filter-title i {
    margin-right: var(--space-xs);
    color: var(--accent-color);
    font-size: 1.1em;
}

/*--------------------------------------------------------------
# Modern Tag Cloud
--------------------------------------------------------------*/
.modern-tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.modern-tag {
    display: inline-block;
    padding: var(--space-xs) var(--space-sm);
    background-color: var(--white);
    color: var(--default-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-family: var(--body-font);
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    margin: 2px;
    line-height: 1.5;
}

.modern-tag:hover, .modern-tag.active {
    background-color: var(--primary-color);
    color: var(--contrast-color);
    border-color: var(--primary-color);
    transform: none;
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3);
    text-decoration: none;
}

/*--------------------------------------------------------------
# Modern Form Elements
--------------------------------------------------------------*/
.form-modern {
    position: relative;
    margin-bottom: 15px;
}

.input-group-modern {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    display: flex;
}

.input-group-modern:hover, .input-group-modern:focus-within {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--accent-color-rgb), 0.25);
}

.form-control-modern {
    border: none;
    border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-sm);
    font-family: var(--body-font);
    transition: all 0.2s ease;
    background-color: var(--white);
    color: var(--default-color);
    width: 100%;
    height: auto;
    flex: 1;
}

.form-control-modern:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
}

.btn-modern {
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-sm);
    font-family: var(--heading-font);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.btn-primary-modern {
    background: var(--primary-color);
    color: var(--contrast-color);
    border: 1px solid var(--primary-color);
}

.btn-primary-modern:hover {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

.btn-search-modern {
    border: none;
    background: var(--surface-color);
    color: var(--text-muted);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 1px solid var(--border-color);
}

.btn-search-modern:hover {
    background: var(--accent-color);
    color: var(--contrast-color);
    transform: none;
}

.btn-secondary-modern {
    background: var(--surface-color);
    color: var(--default-color);
    border: 1px solid var(--border-color);
}

.btn-secondary-modern:hover {
    background: var(--highlight-color);
    border-color: var(--highlight-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--highlight-color-rgb), 0.3);
}

.btn-accent-modern {
    background: var(--accent-color);
    color: var(--contrast-color);
    border: 1px solid var(--accent-color);
}

.btn-accent-modern:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--accent-color-rgb), 0.3);
}

/* Quick Links Özel Buton Stilleri */
.btn-primary-color {
    background: var(--secondary-color);
    color: var(--contrast-color);
    border: 1px solid var(--secondary-color);
}

.btn-primary-color:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--secondary-color-rgb), 0.3);
}

.btn-secondary-color {
    background: var(--secondary-color);
    color: var(--contrast-color);
    border: 1px solid var(--secondary-color);
}

.btn-secondary-color:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--secondary-color-rgb), 0.3);
}

.btn-accent-color {
    background: var(--accent-color);
    color: var(--contrast-color);
    border: 1px solid var(--accent-color);
}

.btn-accent-color:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--accent-color-rgb), 0.3);
}

.btn-highlight-color {
    background: var(--highlight-color);
    color: var(--contrast-color);
    border: 1px solid var(--highlight-color);
}

.btn-highlight-color:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--contrast-color);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--highlight-color-rgb), 0.3);
}

/* Modern Kart stilleri modern-cards.css dosyasına taşındı. */

.modern-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--color-brown-rgb), 0.7),
        rgba(var(--color-navy-rgb), 0.6)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.modern-card:hover .modern-card-overlay {
    opacity: 1;
}

.modern-btn-circle {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modern-btn-circle:hover {
    background: white;
    color: var(--color-brown);
    transform: scale(1.1);
    text-decoration: none;
}

.modern-badge-corner {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(var(--color-brown-rgb), 0.9);
    color: var(--contrast-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.modern-card-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.modern-card-title {
    font-family: var(--heading-font);
    font-weight: var(--font-weight-semibold);
    font-size: 1.1rem;
    color: var(--heading-color);
    margin-bottom: 8px;
    line-height: 1.3;
}

.modern-card-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-card-title a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.modern-card-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 12px;
    flex: 1;
}

.modern-card-author {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.modern-card-author a {
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.85rem;
    transition: color 0.3s ease;
}

.modern-card-author a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

.modern-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-soft);
    margin-top: auto;
}

.modern-meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

/*--------------------------------------------------------------
# Modern Headers
--------------------------------------------------------------*/
.modern-header {
    margin-bottom: 2rem;
}

.gradient-heading {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: var(--heading-font);
    font-weight: var(--font-weight-bold);
}

/*--------------------------------------------------------------
# Animations
--------------------------------------------------------------*/
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animate-fade-in-up {
    opacity: 1;
    transform: translateY(0);
}

.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-4px);
}

/*--------------------------------------------------------------
# Search Highlights
--------------------------------------------------------------*/
.search-highlight {
    background-color: rgba(var(--color-brown-rgb), 0.2);
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: 500;
}

/*--------------------------------------------------------------
# Responsive Design
--------------------------------------------------------------*/
@media (max-width: 768px) {
    .filter-section {
        margin-bottom: 20px;
    }
    
    .modern-tag {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
    
    .modern-card-body {
        padding: 15px;
    }
    
    .modern-card-title {
        font-size: 1rem;
    }
}
