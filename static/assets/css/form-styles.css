/*
* FORM STYLES CSS
* Çalışma ve Video form sayfaları için ortak stiller
* Ana tasarım sistemi ile uyumlu hale getirildi
*/

/* Ana tasarım sistemi değişkenlerini içe aktar */
@import url('./_variables.css');

/* Form özel renkleri - sadece gerek<PERSON> olan<PERSON> */
:root {
    --success-color: #2E7D32;
    --error-color: #C62828;
}

/* Genel stil */
.calisma-form-section {
    background: linear-gradient(135deg, var(--soft-white) 0%, var(--color-sand) 100%);
    padding: 4rem 0;
}

/* Başlık bölümü */
.section-header {
    margin-bottom: 3rem;
}

.section-title {
    color: var(--color-brown);
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(var(--color-brown-rgb), 0.1);
}

.section-subtitle {
    color: var(--text-soft);
    font-size: 1.25rem;
    max-width: 800px;
    margin: 0 auto;
}

.title-decoration {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 8px;
}

.title-decoration span {
    height: 4px;
    width: 30px;
    border-radius: 50px;
    background-color: var(--color-sand);
}

.title-decoration span:nth-child(2) {
    width: 60px;
    background-color: var(--color-brown);
}

/* Form Kartı */
.form-card {
    border-radius: 20px;
    overflow: hidden;
    background: var(--white);
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.form-card:hover {
    box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.1);
}

.form-card .card-header {
    padding: 1.5rem;
    background: linear-gradient(to right, var(--color-sand), var(--color-sky));
    color: var(--color-navy);
    border-radius: 20px 20px 0 0;
    font-weight: 600;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5);
    border: none;
}

.form-card .card-body {
    padding: 2rem;
}

/* Fotoğraf Formset */
.photo-form-item {
    padding: 1rem;
    border-radius: 10px;
    background-color: var(--soft-white);
    transition: all 0.3s ease;
}

.photo-form-item:hover {
    background-color: var(--color-sand);
}

/* Butonlar */
.form-actions {
    display: flex;
    align-items: center;
}

.calisma-form .btn-primary {
    background: linear-gradient(to right, var(--color-brown), var(--color-navy));
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    color: var(--white);
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(var(--color-brown-rgb), 0.3);
    transition: all 0.3s ease;
}

.calisma-form .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(var(--color-brown-rgb), 0.4);
    color: var(--white);
}

.calisma-form .btn-outline-secondary {
    color: var(--text-soft);
    border-color: var(--light-gray);
    border-radius: 50px;
    padding: 0.8rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.calisma-form .btn-outline-secondary:hover {
    background-color: var(--light-gray);
    color: var(--text-soft);
}

.calisma-form .btn-danger {
    background: linear-gradient(to right, #C62828, #B71C1C);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    color: var(--white);
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(198, 40, 40, 0.3);
    transition: all 0.3s ease;
}

.calisma-form .btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(198, 40, 40, 0.4);
    color: var(--white);
}

/* Animasyonlar */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.wow {
    animation: fadeIn 0.8s ease forwards;
}

.fadeIn { animation-name: fadeIn; }
.fadeInUp { animation-name: fadeInUp; }

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
