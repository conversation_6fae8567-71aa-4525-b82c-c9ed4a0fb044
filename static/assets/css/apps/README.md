# Uy<PERSON><PERSON><PERSON>zel CSS Dosyaları

Bu klasör, her uygulama için <PERSON>zel CSS stillerini içerir. Bu ya<PERSON><PERSON><PERSON>, statik dosya organizasyonunu iyileştirmek ve tasarım tutarlılığını sağlamak için oluşturulmuştur.

## 📁 Klasör Yapısı

```
static/assets/css/apps/
├── atolye.css          # Atolye uygulaması stilleri
├── calismalar.css      # Çalışmalar uygulaması stilleri (gelecekte)
├── urunler.css         # Ürünler uygulaması stilleri (gelecekte)
└── README.md           # Bu dosya
```

## 🎯 Amaç

### Önceki Durum (Problemli)
- Atolye uygulaması: `atolye/static/atolye/css/` (ayrı klasör)
- Diğer uygulamalar: `static/calismalar/css/`, `static/urunler/js/` (dağınık)
- Renk paleti tutarsızlı<PERSON>ı
- CSS yükleme sırası sorunları

### Yeni Durum (Çözüm)
- Tüm uygulama CSS'leri: `static/assets/css/apps/` (merkezi)
- Ana renk paleti: `_variables.css` kullanımı
- Tutarlı CSS yükleme sırası
- Modüler ve bakımı kolay yapı

## 🔧 Kullanım

### Template'lerde Kullanım
```html
{% block extra_css %}
<!-- Uygulama özel stilleri -->
<link rel="stylesheet" href="{% static 'assets/css/apps/atolye.css' %}">
{% endblock %}
```

### CSS Yükleme Sırası
1. **Değişkenler**: `_variables.css`
2. **Ana tema**: `modern-theme.css`
3. **Genel bileşenler**: `modern-components.css`, vb.
4. **Uygulama özel**: `apps/[uygulama].css` (en son)

## 📋 Atolye.css İçeriği

### Birleştirilen Dosyalar
- `lightbox.css` - Lightbox bileşeni
- `video-player.css` - Video oynatıcı (Instagram Reels tarzı)
- `pwa.css` - PWA özellikleri

### Ana Bölümler
1. **Atolye Kart Stilleri** - Galeri kartları
2. **Hover Efektleri** - Etkileşim animasyonları
3. **Galeri Stilleri** - Lightbox ve galeri
4. **İçerik Animasyonları** - Infinite scroll
5. **Modern Lightbox** - Gelişmiş lightbox
6. **Video Player** - Instagram tarzı video oynatıcı
7. **PWA Stilleri** - Progressive Web App
8. **Responsive Design** - Mobil uyumluluk
9. **Accessibility** - Erişilebilirlik

### Renk Paleti Uyumluluğu
Tüm renkler `_variables.css` dosyasından alınır:
- `var(--color-brown)` - Ana renk
- `var(--color-navy)` - İkincil renk
- `var(--color-moss)` - Vurgu rengi
- `var(--soft-white)` - Arka plan
- `var(--text-soft)` - Metin rengi

## 🚀 Gelecek Planları

### Diğer Uygulamalar
- `calismalar.css` - Çalışmalar uygulaması stilleri
- `urunler.css` - Ürünler uygulaması stilleri
- `nasilyapilir.css` - Nasıl Yapılır uygulaması stilleri

### Optimizasyonlar
- CSS minification (production)
- Critical CSS extraction
- Unused CSS removal
- CSS modules (gelecekte)

## 📝 Geliştirici Notları

### Yeni Uygulama CSS'i Ekleme
1. `static/assets/css/apps/[uygulama].css` oluştur
2. `@import url('../_variables.css');` ekle
3. Template'de `{% block extra_css %}` içinde yükle
4. Ana renk paletini kullan

### CSS Yazım Kuralları
- CSS değişkenlerini kullan (hardcode renk yok)
- BEM metodolojisini takip et
- Responsive design öncelikli
- Accessibility standartlarına uy
- Performans odaklı yaz

### Tarayıcı Desteği
- Modern tarayıcılar (ES6+)
- CSS Grid ve Flexbox
- CSS Custom Properties
- Progressive enhancement

## 🔄 Migrasyon Durumu

### ✅ Tamamlanan
- [x] Atolye CSS dosyaları birleştirildi
- [x] Renk paleti standardize edildi
- [x] Template'ler güncellendi
- [x] CSS yükleme sırası düzenlendi

### 🔄 Devam Eden
- [ ] Diğer uygulamaların CSS'leri
- [ ] Eski CSS dosyalarının temizlenmesi
- [ ] Performance optimizasyonları

### 📋 Yapılacaklar
- [ ] CSS linting kuralları
- [ ] Automated testing
- [ ] Documentation expansion
- [ ] Build process optimization

---

**Son Güncelleme**: 2025-06-16  
**Geliştirici**: Küp Cadısı Development Team  
**Versiyon**: 1.0.0
