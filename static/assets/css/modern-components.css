/*
* MODERN COMPONENTS
* Küp Cadısı projesi için modern UI bileşenleri
* Loading, notifications, modals ve diğer interaktif elementler
*/

@import url('./_variables.css');

/* LOADING COMPONENTS */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(var(--color-brown-rgb), 0.2);
  border-left: 4px solid var(--color-brown);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner-sm {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner-lg {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
  align-items: center;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background: var(--color-brown);
  border-radius: 50%;
  animation: loading-dots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loading-content {
  text-align: center;
  padding: var(--space-xl);
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.loading-content h3 {
  margin-top: var(--space-md);
  color: var(--color-brown);
  font-weight: var(--font-weight-medium);
}

/* TOAST NOTIFICATIONS */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
}

.toast-modern {
  background: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  margin-bottom: var(--space-md);
  overflow: hidden;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 4px solid var(--color-brown);
}

.toast-modern.show {
  transform: translateX(0);
}

.toast-modern.hide {
  transform: translateX(100%);
  opacity: 0;
}

.toast-header-modern {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: rgba(var(--color-brown-rgb), 0.05);
  border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.toast-icon-modern {
  width: 24px;
  height: 24px;
  margin-right: var(--space-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;
  color: var(--white);
}

.toast-title-modern {
  flex: 1;
  font-weight: var(--font-weight-semibold);
  color: var(--color-brown);
  margin: 0;
}

.toast-close-modern {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-body-modern {
  padding: var(--space-lg);
  color: var(--text-soft);
  line-height: 1.5;
}

/* Toast Variants */
.toast-success {
  border-left-color: var(--color-moss);
}

.toast-success .toast-icon-modern {
  background: var(--color-moss);
}

.toast-error {
  border-left-color: #e74c3c;
}

.toast-error .toast-icon-modern {
  background: #e74c3c;
}

.toast-warning {
  border-left-color: #f39c12;
}

.toast-warning .toast-icon-modern {
  background: #f39c12;
}

.toast-info {
  border-left-color: var(--color-navy);
}

.toast-info .toast-icon-modern {
  background: var(--color-navy);
}

/* MODAL */
.modal-overlay-modern {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.modal-overlay-modern.show {
  opacity: 1;
  visibility: visible;
}

.modal-content-modern {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  transform: scale(0.9) translateY(-20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay-modern.show .modal-content-modern {
  transform: scale(1) translateY(0);
}

.modal-header-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl) var(--space-xl) var(--space-lg);
  border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.modal-title-modern {
  color: var(--color-brown);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.modal-close-modern {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close-modern:hover {
  background: rgba(var(--color-brown-rgb), 0.1);
  color: var(--color-brown);
}

.modal-body-modern {
  padding: var(--space-lg) var(--space-xl);
}

.modal-footer-modern {
  display: flex;
  gap: var(--space-md);
  justify-content: flex-end;
  padding: var(--space-lg) var(--space-xl) var(--space-xl);
  border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

/* PROGRESS BAR */
.progress-modern {
  width: 100%;
  height: 8px;
  background: rgba(var(--color-brown-rgb), 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar-modern {
  height: 100%;
  background: linear-gradient(90deg, var(--color-brown) 0%, var(--color-sand) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar-modern::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

.progress-lg {
  height: 12px;
}

.progress-sm {
  height: 4px;
}

/* BADGE */
.badge-modern {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: 20px;
  background: var(--color-brown);
  color: var(--white);
  text-decoration: none;
}

.badge-secondary {
  background: var(--color-navy);
}

.badge-success {
  background: var(--color-moss);
}

.badge-warning {
  background: #f39c12;
}

.badge-danger {
  background: #e74c3c;
}

.badge-outline {
  background: transparent;
  border: 1px solid var(--color-brown);
  color: var(--color-brown);
}

/* TOOLTIP */
.tooltip-modern {
  position: relative;
  display: inline-block;
}

.tooltip-modern::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: var(--white);
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  margin-bottom: 5px;
}

.tooltip-modern::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tooltip-modern:hover::after,
.tooltip-modern:hover::before {
  opacity: 1;
  visibility: visible;
}

/* RESPONSIVE DESIGN */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .modal-content-modern {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header-modern,
  .modal-body-modern,
  .modal-footer-modern {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }
}
