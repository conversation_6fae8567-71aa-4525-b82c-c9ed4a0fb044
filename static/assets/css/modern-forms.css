/* ====================================================================
   COMPONENTS / _FORMS.CSS
   ====================================================================

   BEM yapı<PERSON>ına uygun, projenin tamamında kullanılacak temel form stilleri.

   Yapı:
   .form__group
     .form__label
     .form__input (veya .form__textarea, .form__select)
     .form__help
     .form__feedback

   .form__check
     .form__check-input
     .form__check-label

   .form__input-group
     .form__input-group-text
     .form__input

/* ==================================================================== */

/* --- Form Grubu --- */
.form__group {
  margin-bottom: 1.5rem;
  position: relative;
}

/* --- Form Etiketi --- */
.form__label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #5a3d2b; /* Daha koyu ve belirgin etiket rengi */
  font-size: 0.95rem;
  font-family: var(--font-family-heading);
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.3px;
}

/* --- Genel Form Elemanları (Input, Textarea, Select) --- */
.form__input,
.form__textarea,
.form__select {
  display: block;
  width: 100%;
  padding: 0.8rem 1rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #2c1810; /* Daha koyu metin rengi */
  background-color: #fff9f5; /* Açık krem arka plan */
  background-clip: padding-box;
  border: 2px solid #d4b79c; /* Daha belirgin kenarlık */
  appearance: none;
  border-radius: var(--border-radius-md);
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form__input::placeholder,
.form__textarea::placeholder {
    color: #8c7a6b; /* Daha koyu ve okunaklı placeholder rengi */
    opacity: 0.9;
}

.form__input:focus,
.form__textarea:focus,
.form__select:focus {
  color: #1f1209; /* Daha koyu metin rengi */
  background-color: #fff; /* Tam beyaz arka plan */
  border-color: #b38b6d; /* Daha belirgin kenarlık rengi */
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(179, 139, 109, 0.3); /* Yumuşak gölge efekti */
}

.form__textarea {
  min-height: 150px;
  resize: vertical;
}

/* --- Yardımcı Metin --- */
.form__help {
  margin-top: 0.5rem;
  font-size: 0.875em;
  color: var(--color-gray-dark);
}

/* --- Durum Değiştiricileri (Modifiers) --- */

/* Devre Dışı Bırakılmış Durum */
.form__input:disabled,
.form__textarea:disabled,
.form__select:disabled {
    background-color: var(--disabled-bg, #e9ecef);
    color: var(--disabled-color, #6c757d);
    cursor: not-allowed;
    opacity: 1;
}

/* Geçersiz Durum */
.form__input--invalid,
.form__textarea--invalid,
.form__select--invalid {
    border-color: var(--danger-color, #dc3545);
}

.form__input--invalid:focus,
.form__textarea--invalid:focus,
.form__select--invalid:focus {
    box-shadow: 0 0 0 0.25rem rgba(var(--danger-rgb, 220, 53, 69), 0.25);
}

/* Geçerli Durum */
.form__input--valid,
.form__textarea--valid,
.form__select--valid {
    border-color: var(--success-color, #198754);
}

.form__input--valid:focus,
.form__textarea--valid:focus,
.form__select--valid:focus {
    box-shadow: 0 0 0 0.25rem rgba(var(--success-rgb, 25, 135, 84), 0.25);
}

/* --- Geri Bildirim Mesajı --- */
.form__feedback {
    display: none; /* Varsayılan olarak gizli */
    width: 100%;
    margin-top: .5rem;
    font-size: .875em;
    font-weight: 500;
}

.form__feedback--invalid {
    display: block;
    color: var(--danger-color, #dc3545);
}

.form__feedback--valid {
    display: block;
    color: var(--success-color, #198754);
}

/* --- Checkbox & Radio Elemanları --- */
.form__check {
    display: flex;
    align-items: center;
    gap: var(--space-sm, 0.5rem);
    margin-bottom: 1rem;
    cursor: pointer;
    user-select: none;
}

.form__check-input {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(var(--color-brown-rgb), 0.3);
    border-radius: var(--border-radius-sm, 0.25rem);
    background: var(--white);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    flex-shrink: 0;
}

.form__check-input:checked {
    background-color: var(--color-brown);
    border-color: var(--color-brown);
}

.form__check-input:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.form__check-input--radio {
    border-radius: 50%;
}

.form__check-input--radio:checked::after {
    content: '';
    width: 10px;
    height: 10px;
    background: var(--white);
    border-radius: 50%;
}

.form__check-label {
    font-size: var(--font-size-md, 1rem);
    color: var(--text-soft);
    margin-bottom: 0; /* .form__check hizalaması için */
}

/* --- Input Grubu --- */
.form__input-group {
    display: flex;
    align-items: stretch;
    position: relative;
}

.form__input-group .form__input {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
    border-radius: 0; /* Köşeleri grup yönetecek */
}

.form__input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
}

/* Input grubunda köşe yuvarlaklığını yönetme */
.form__input-group > :not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.form__input-group > :not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-left: -1px;
}

.form__input-group .form__input:focus {
    z-index: 3;
    box-shadow: none; /* Grup focus'u yönetecek */
}

.form__input-group:focus-within {
    z-index: 4;
    border-color: var(--color-moss);
    box-shadow: 0 0 0 0.25rem rgba(var(--color-moss-rgb), 0.25);
}
