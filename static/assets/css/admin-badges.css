/*
* ADMIN BADGES CSS
* Admin paneli için badge stilleri
* Ana tasarım sistemi ile uyumlu hale getirildi
*/

/* Ana tasarım sistemi değişkenlerini içe aktar */
@import url('./_variables.css');

/* Admin Badge Stilleri */
.admin-badge {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    color: white;
    display: inline-block;
}

/* İçerik Tip Badge'leri */
.admin-badge-galeri {
    background-color: var(--success-color, #28a745);
}

.admin-badge-duyuru {
    background-color: var(--warning-color, #ffc107);
    color: var(--color-brown) !important;
}

.admin-badge-video {
    background-color: var(--error-color, #dc3545);
}

/* Durum Badge'leri */
.admin-badge-taslak {
    background-color: var(--text-muted, #6c757d);
}

.admin-badge-yayinda {
    background-color: var(--success-color, #28a745);
}

.admin-badge-arsiv {
    background-color: var(--warning-color, #ffc107);
    color: var(--color-brown) !important;
}

/* Önemli Badge */
.admin-badge-onemli {
    background-color: var(--error-color, #dc3545);
}

/* Ürün Durum Badge'leri */
.admin-badge-aktif {
    background: var(--color-moss);
    color: white;
    padding: 3px 8px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
}

.admin-badge-pasif {
    background: var(--error-color, #e74c3c);
    color: white;
    padding: 3px 8px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
}

/* Etiket Badge'leri */
.tag-badge {
    background-color: var(--color-moss);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
    margin: 2px;
}

/* İstatistik Kartları */
.stat-card {
    background-color: white;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(var(--color-brown-rgb), 0.1);
    text-align: center;
    margin-bottom: 15px;
}

.stat-card h3 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 1rem;
    color: var(--text-soft);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--color-navy);
}

/* Etiket Bulutu */
.tag-cloud {
    margin-top: 20px;
}

.tag-cloud h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1rem;
    color: var(--text-soft);
}

.tag-item {
    display: inline-block;
    margin: 5px;
    padding: 5px 10px;
    background-color: var(--color-moss);
    color: white;
    border-radius: 15px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.2s ease;
}

.tag-item:hover {
    background-color: var(--color-brown);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.tag-item .count {
    background-color: white;
    color: var(--color-moss);
    border-radius: 10px;
    padding: 2px 6px;
    margin-left: 5px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Admin Form Stilleri */
.tag-info {
    background: rgba(var(--color-sand-rgb), 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.tag-info h2 {
    color: var(--color-brown);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tag-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

/* Responsive Düzenlemeler */
@media (max-width: 768px) {
    .tag-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 10px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .admin-badge {
        font-size: 10px;
        padding: 2px 6px;
    }
}
