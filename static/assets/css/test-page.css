/*
* TEST PAGE STYLES CSS
* Test sayfası i<PERSON><PERSON> stiller
* Son güncelleme: 15.05.2025
*/

/* Header i<PERSON>in <PERSON>zel stiller */
.modern-header {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.1);
    border-bottom: 1px solid rgba(var(--color-brown-rgb), 0.08);
    transition: all 0.4s ease;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.modern-header.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 6px 25px rgba(var(--color-brown-rgb), 0.15);
}

.modern-logo {
    text-shadow: 2px 2px 4px rgba(var(--color-brown-rgb), 0.2);
    position: relative;
    display: inline-block;
}

.modern-logo::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right,
                rgba(var(--color-brown-rgb), 0.7),
                rgba(var(--color-brown-rgb), 0.3) 70%,
                rgba(var(--color-brown-rgb), 0));
    border-radius: 2px;
}

/* Slider stilleri */
.test-slider-container {
    height: 400px;
}

.test-slider-overlay {
    background-color: rgba(0,0,0,0.5);
    border-radius: 10px;
}

/* Kart stilleri */
.test-card-header-brown {
    background-color: var(--color-brown);
    color: var(--soft-white);
}

.test-card-header-brown h3 {
    color: var(--soft-white);
}

.test-card-header-navy {
    background-color: var(--color-navy);
    color: var(--soft-white);
}

.test-card-header-navy h3 {
    color: var(--soft-white);
}

.test-card-header-sky {
    background-color: var(--color-sky);
    color: var(--soft-white);
}

.test-card-header-sky h3 {
    color: var(--soft-white);
}

/* Buton stilleri */
.test-btn-white {
    background-color: white;
    color: var(--color-navy);
    border-color: white;
}

.test-btn-circle-sky {
    background-color: var(--color-sky);
}

.test-btn-circle-white {
    background-color: white;
    color: var(--color-navy);
}

/* Progress bar stilleri */
.test-progress-bar {
    width: 75%;
}

/* Blue section stilleri */
.blue-section {
    background: linear-gradient(135deg, var(--color-navy) 0%, var(--color-sky) 100%);
    color: white;
}

.feature-box {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: white;
}

.blue-btn {
    background-color: white;
    color: var(--color-navy);
    border: 2px solid white;
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.blue-btn:hover {
    background-color: transparent;
    color: white;
}

.blue-card {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.blue-card-header h3 {
    color: white;
    margin-bottom: 1rem;
}

.blue-card-body {
    color: rgba(255, 255, 255, 0.9);
}

.blue-progress-container {
    margin-top: 1.5rem;
}

.blue-progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.blue-progress {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
}

.blue-progress-bar {
    background: linear-gradient(90deg, white 0%, rgba(255, 255, 255, 0.8) 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
    .modern-header {
        padding: 1rem 0;
    }
    
    .test-slider-container {
        height: 300px;
    }
    
    .feature-box {
        padding: 1rem;
    }
    
    .blue-card {
        padding: 1.5rem;
    }
}
