/*
* KÜP CADISI RENK PALETİ VE DEĞİŞKENLER
* Bu dosya, Küp Cadısı web sitesinin tüm renk değişkenlerini ve temel stil değişkenlerini içerir.
* Tüm CSS dosyaları bu değişkenleri kullanmalıdır.
*
* Son Güncelleme: 15.05.2025
*/

/* TEMEL STİL DEĞİŞKENLERİ */
:root {
  /* Sonbahar Renk Paleti */
  --color-navy: #42708C; /* Rocky Mountain Mavi */
  --color-navy-rgb: 66, 112, 140;
  --color-sky: #80A7BF; /* Göl Mavisi */
  --color-sky-rgb: 128, 167, 191;
  --color-moss: #657351; /* <PERSON><PERSON> */
  --color-moss-rgb: 101, 115, 81;
  --color-brown: #A66F3F; /* <PERSON>rak Kahvesi */
  --color-brown-rgb: 166, 111, 63;
  --color-sand: #D9A273; /* <PERSON><PERSON><PERSON>j */
  --color-sand-rgb: 217, 162, 115;

  /* <PERSON><PERSON>nları */
  --soft-white: #f8f5f0; /* <PERSON>fa arkaplanı - Açık krem */
  --soft-white-rgb: 248, 245, 240;
  --soft-light: #f2ede2; /* Kart arkaplanı - Yumuşak bej */
  --soft-light-rgb: 242, 237, 226;
  --off-white: #f5f0e8; /* Hafif bej tonu */
  --off-white-rgb: 245, 240, 232;
  --white: #ffffff;
  --white-rgb: 255, 255, 255;

  /* Gri Tonları */
  --light-gray: #e4e8ed; /* Açık gri-mavi */
  --light-gray-rgb: 228, 232, 237; /* Açık gri-mavi RGB değeri */
  --medium-gray: #d5d3ca; /* Nötr gri */

  /* Metin Renkleri */
  --text-color: #5a3d2b; /* Koyu kahve - Toprak kahvesinin koyu tonu */
  --text-color-rgb: 90, 61, 43; /* Koyu kahve RGB değeri */
  --text-soft: #6e4c36; /* Ana metin - Orta kahve */
  --text-muted: #8c6952; /* İkincil metin - Açık kahve */
  --light-text: #a38b7d; /* Açık metin rengi */

  /* Genel Kullanım - Sonbahar Teması */
  --primary-color: var(--color-brown); /* Ana renk - Toprak Kahvesi */
  --primary-color-rgb: var(--color-brown-rgb);
  --secondary-color: var(--color-navy); /* İkincil renk - Rocky Mountain Mavi */
  --secondary-color-rgb: var(--color-navy-rgb);
  --accent-color: var(--color-moss); /* Vurgu rengi - Orman Yeşili */
  --accent-color-rgb: var(--color-moss-rgb);
  --light-accent: var(--color-sand); /* Hafif vurgu - Kumlu Bej */
  --light-accent-rgb: var(--color-sand-rgb);
  --highlight-color: var(--color-sky); /* Belirginleştirme - Göl Mavisi */
  --highlight-color-rgb: var(--color-sky-rgb);

  /* Fonksiyonel Renkler */
  --background-color: var(--soft-white); /* Arka plan rengi */
  --default-color: var(--text-soft); /* Varsayılan metin rengi */
  --heading-color: var(--color-brown); /* Başlık rengi */
  --border-color: rgba(var(--color-brown-rgb), 0.15); /* Sınır rengi */
  --shadow-color: rgba(var(--color-brown-rgb), 0.08); /* Gölge rengi */
  --contrast-color: var(--soft-white); /* Kontrast metin rengi */
  --surface-color: var(--soft-light); /* Yüzey rengi (kartlar vb.) */

  /* Gradyantlar */
  --page-bg-top: linear-gradient(170deg, rgba(var(--color-navy-rgb), 0.08), transparent 60%); /* Sayfa üst kısım gradyantti */
  --page-bg-bottom: linear-gradient(350deg, rgba(var(--color-navy-rgb), 0.05), transparent 60%); /* Sayfa alt kısım gradyantti */
  --card-hover-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.15); /* Kartlar hover shadow */
  --section-border: rgba(var(--color-brown-rgb), 0.2); /* Bölüm sınırları */

  /* Nav Menu Renkleri */
  --nav-color: var(--color-navy);  /* Menü linkleri rengi */
  --nav-hover-color: var(--color-brown); /* Menü hover rengi */
  --nav-mobile-background-color: var(--white); /* Mobil menü arka plan */
  --nav-dropdown-background-color: var(--white); /* Dropdown menü arka plan */
  --nav-dropdown-color: var(--text-color); /* Dropdown menü metin rengi */
  --nav-dropdown-hover-color: var(--color-brown); /* Dropdown menü hover rengi */

  /* Font Aileleri - Tutarlı Tipografi */
  /*
   * Poppins: Modern, temiz ve okunabilir bir font. Başlıklar ve vurgu gerektiren yerler için.
   * Roboto: Gövde metni için mükemmel, yüksek okunabilirlik sunar.
   */
  --heading-font: 'Poppins', sans-serif; /* Tüm başlıklar için (h1-h6) */
  --body-font: 'Roboto', sans-serif;     /* Paragraflar ve genel metin için */
  --nav-font: 'Poppins', sans-serif;     /* Navigasyon menüleri için */
  --default-font: 'Roboto', system-ui, -apple-system, "Segoe UI", "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* Fallback fontlar */

  /* Font Ağırlıkları - Tutarlı kullanım için */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font Boyutları - Tutarlı kullanım için */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-md: 1rem;       /* 16px */
  --font-size-lg: 1.25rem;    /* 20px */
  --font-size-xl: 1.5rem;     /* 24px */
  --font-size-xxl: 2rem;      /* 32px */
  --font-size-xxxl: 2.5rem;   /* 40px */
  --font-size-display: 3rem;  /* 48px */

  /* Boşluk Sistemi - Tutarlı kullanım için */
  --space-unit: 0.25rem;
  --space-xxs: calc(var(--space-unit) * 1);   /* 0.25rem - 4px */
  --space-xs: calc(var(--space-unit) * 2);    /* 0.5rem - 8px */
  --space-sm: calc(var(--space-unit) * 3);    /* 0.75rem - 12px */
  --space-md: calc(var(--space-unit) * 4);    /* 1rem - 16px */
  --space-lg: calc(var(--space-unit) * 6);    /* 1.5rem - 24px */
  --space-xl: calc(var(--space-unit) * 8);    /* 2rem - 32px */
  --space-xxl: calc(var(--space-unit) * 12);  /* 3rem - 48px */
  --space-xxxl: calc(var(--space-unit) * 16); /* 4rem - 64px */

  /* Responsive Boşluklar */
  --container-padding-x: var(--space-lg);
  --container-padding-y: var(--space-xl);
  --section-spacing: var(--space-xxl);
  --card-spacing: var(--space-md);
  --element-spacing: var(--space-md);

  /* Kenar Yuvarlaklıkları */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  --border-radius-circle: 50%;
}

/* Renk Presetleri - Belirli bölümlerde farklı renk şemaları kullanmak için */
.light-background {
  --background-color: var(--soft-light);
  --surface-color: var(--light-accent);
}

.dark-background {
  --background-color: #060606;
  --default-color: var(--white);
  --heading-color: var(--white);
  --surface-color: #252525;
  --contrast-color: var(--white);
}
