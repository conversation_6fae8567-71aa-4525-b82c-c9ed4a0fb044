/*
* CUSTOM CONFIRM DIALOG CSS
* <PERSON><PERSON><PERSON> uygun <PERSON> onay penceresi
* Son güncelleme: 15.05.2025
*/

/* Overlay */
.custom-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 2147483647;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(3px);
}

.custom-confirm-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Dialog */
.custom-confirm-dialog {
    background: linear-gradient(135deg, var(--soft-white) 0%, #fefefe 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 60px rgba(var(--color-brown-rgb), 0.3);
    max-width: 450px;
    width: 90%;
    transform: scale(0.7) translateY(-50px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    overflow: hidden;
}

.custom-confirm-overlay.show .custom-confirm-dialog {
    transform: scale(1) translateY(0);
}

/* Header */
.custom-confirm-header {
    background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-sand) 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
    position: relative;
}

.custom-confirm-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.custom-confirm-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.custom-confirm-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Body */
.custom-confirm-body {
    padding: 2rem 1.5rem;
    text-align: center;
}

.custom-confirm-message {
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.custom-confirm-warning {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-top: 1rem;
    color: #721c24;
}

.custom-confirm-warning strong {
    color: #dc3545;
}

/* Footer */
.custom-confirm-footer {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    background-color: var(--soft-light);
}

/* Buttons */
.custom-confirm-btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.custom-confirm-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.custom-confirm-btn:hover::before {
    width: 300px;
    height: 300px;
}

.custom-confirm-btn-cancel {
    background: linear-gradient(135deg, var(--color-sand) 0%, var(--color-moss) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(var(--color-sand-rgb), 0.3);
}

.custom-confirm-btn-cancel:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--color-sand-rgb), 0.4);
}

.custom-confirm-btn-confirm {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.custom-confirm-btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Icon animations */
.custom-confirm-icon {
    animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive */
@media (max-width: 576px) {
    .custom-confirm-dialog {
        width: 95%;
        margin: 1rem;
    }
    
    .custom-confirm-header {
        padding: 1rem;
    }
    
    .custom-confirm-body {
        padding: 1.5rem 1rem;
    }
    
    .custom-confirm-footer {
        flex-direction: column;
        padding: 1rem;
    }
    
    .custom-confirm-btn {
        width: 100%;
    }
}
