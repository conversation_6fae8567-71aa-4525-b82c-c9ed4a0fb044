/*
* INLINE EDIT STYLES CSS
* Inline düzenleme özellikleri iç<PERSON> stiller
* Son güncelleme: 15.05.2025
*/

/* Düzenlenebilir içerik stilleri */
.editable-content {
    position: relative;
    border: 1px dashed transparent;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
}

.editable-content:hover,
.editable-content-hover {
    border-color: var(--color-brown);
    background-color: rgba(var(--color-brown-rgb), 0.05);
}

.editable-content.editing {
    border-color: var(--color-navy);
    background-color: rgba(var(--color-navy-rgb), 0.05);
}

/* Düzenleme ikonu */
.edit-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: var(--color-brown);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10;
}

.editable-content:hover .edit-icon {
    opacity: 1;
}

.edit-icon:hover {
    background-color: var(--color-navy);
    transform: scale(1.1);
}

/* Düzenleme formu stilleri */
.inline-edit-form {
    background-color: var(--soft-white);
    border: 2px solid var(--color-navy);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    margin: 0.5rem 0;
    box-shadow: 0 4px 12px rgba(var(--color-navy-rgb), 0.15);
}

.inline-edit-form textarea,
.inline-edit-form input {
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem;
    font-family: var(--body-font);
    font-size: var(--font-size-base);
    resize: vertical;
}

.inline-edit-form textarea:focus,
.inline-edit-form input:focus {
    outline: none;
    border-color: var(--color-navy);
    box-shadow: 0 0 0 2px rgba(var(--color-navy-rgb), 0.2);
}

/* Düzenleme butonları */
.inline-edit-buttons {
    margin-top: 0.75rem;
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.inline-edit-save {
    background-color: var(--color-moss);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.inline-edit-save:hover {
    background-color: color-mix(in srgb, var(--color-moss), black 15%);
    transform: translateY(-1px);
}

.inline-edit-cancel {
    background-color: var(--color-sand);
    color: var(--text-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.inline-edit-cancel:hover {
    background-color: color-mix(in srgb, var(--color-sand), black 15%);
    transform: translateY(-1px);
}

/* Yükleme durumu */
.inline-edit-loading {
    opacity: 0.6;
    pointer-events: none;
}

.inline-edit-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-brown);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Başarı/hata mesajları */
.inline-edit-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-md);
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.inline-edit-message.success {
    background-color: var(--color-moss);
}

.inline-edit-message.error {
    background-color: #dc3545;
}

/* slideIn animasyonu artık modern-theme.css'te messageSlideIn olarak tanımlı */

/* Responsive düzenlemeler */
@media (max-width: 768px) {
    .edit-icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
    
    .inline-edit-form {
        padding: 0.75rem;
    }
    
    .inline-edit-buttons {
        flex-direction: column;
    }
    
    .inline-edit-save,
    .inline-edit-cancel {
        width: 100%;
        margin-bottom: 0.25rem;
    }
}
