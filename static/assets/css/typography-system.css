/**
 * Typography System
 * Tutarlı tipografi tüm site için
 */

/*--------------------------------------------------------------
# Typography Scale
--------------------------------------------------------------*/
:root {
    /* Typography Scale - Perfect Fourth (1.333) */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-md: 1rem;       /* 16px - base */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.333rem;   /* ~21px */
    --font-size-xxl: 1.777rem;  /* ~28px */
    --font-size-3xl: 2.369rem;  /* ~38px */
    --font-size-4xl: 3.157rem;  /* ~51px */
    
    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    --line-height-loose: 1.8;
    
    /* Letter Spacing */
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
}

/*--------------------------------------------------------------
# Heading Styles
--------------------------------------------------------------*/
.heading-1 {
    font-family: var(--heading-font);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--heading-color);
    margin-bottom: var(--space-lg);
}

.heading-2 {
    font-family: var(--heading-font);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--heading-color);
    margin-bottom: var(--space-md);
}

.heading-3 {
    font-family: var(--heading-font);
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-md);
}

.heading-4 {
    font-family: var(--heading-font);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-sm);
}

.heading-5 {
    font-family: var(--heading-font);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-sm);
}

.heading-6 {
    font-family: var(--heading-font);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-xs);
}

/*--------------------------------------------------------------
# Body Text Styles
--------------------------------------------------------------*/
.body-large {
    font-family: var(--body-font);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-relaxed);
    color: var(--default-color);
}

.body-normal {
    font-family: var(--body-font);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    color: var(--default-color);
}

.body-small {
    font-family: var(--body-font);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    color: var(--text-muted);
}

.body-xs {
    font-family: var(--body-font);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    color: var(--text-soft);
}

/*--------------------------------------------------------------
# Special Text Styles
--------------------------------------------------------------*/
.lead-text {
    font-family: var(--body-font);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-relaxed);
    color: var(--text-muted);
    margin-bottom: var(--space-lg);
}

.caption {
    font-family: var(--body-font);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    color: var(--text-soft);
    letter-spacing: var(--letter-spacing-wide);
    text-transform: uppercase;
}

.quote {
    font-family: var(--heading-font);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-relaxed);
    color: var(--heading-color);
    font-style: italic;
    border-left: 4px solid var(--accent-color);
    padding-left: var(--space-lg);
    margin: var(--space-lg) 0;
}

.highlight {
    background: linear-gradient(120deg, 
        rgba(var(--color-moss-rgb), 0.2) 0%, 
        rgba(var(--color-moss-rgb), 0.2) 100%);
    padding: 0 var(--space-xs);
    border-radius: var(--border-radius-sm);
}

/*--------------------------------------------------------------
# Link Styles
--------------------------------------------------------------*/
.link-primary {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all 0.3s ease;
    position: relative;
}

.link-primary:hover {
    color: var(--color-brown);
    text-decoration: none;
}

.link-primary::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.link-primary:hover::after {
    width: 100%;
}

.link-subtle {
    color: var(--text-muted);
    text-decoration: none;
    transition: color 0.3s ease;
}

.link-subtle:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/*--------------------------------------------------------------
# List Styles
--------------------------------------------------------------*/
.list-modern {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-modern li {
    position: relative;
    padding-left: var(--space-lg);
    margin-bottom: var(--space-sm);
    font-family: var(--body-font);
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
    color: var(--default-color);
}

.list-modern li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: var(--font-weight-bold);
}

.list-check {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-check li {
    position: relative;
    padding-left: var(--space-lg);
    margin-bottom: var(--space-sm);
    font-family: var(--body-font);
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
    color: var(--default-color);
}

.list-check li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: var(--font-weight-bold);
}

/*--------------------------------------------------------------
# Text Utilities
--------------------------------------------------------------*/
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }

.text-italic { font-style: italic; }
.text-normal { font-style: normal; }

.text-underline { text-decoration: underline; }
.text-no-underline { text-decoration: none; }

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/*--------------------------------------------------------------
# Responsive Typography
--------------------------------------------------------------*/
@media (max-width: 768px) {
    .heading-1 {
        font-size: var(--font-size-3xl);
    }
    
    .heading-2 {
        font-size: var(--font-size-xxl);
    }
    
    .heading-3 {
        font-size: var(--font-size-xl);
    }
    
    .lead-text {
        font-size: var(--font-size-lg);
    }
    
    .quote {
        font-size: var(--font-size-lg);
        padding-left: var(--space-md);
    }
}

/*--------------------------------------------------------------
# Override Default HTML Elements
--------------------------------------------------------------*/
h1, .h1 {
    font-family: var(--heading-font);
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--heading-color);
    margin-bottom: var(--space-lg);
}

h2, .h2 {
    font-family: var(--heading-font);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--heading-color);
    margin-bottom: var(--space-md);
}

h3, .h3 {
    font-family: var(--heading-font);
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-md);
}

h4, .h4 {
    font-family: var(--heading-font);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-sm);
}

h5, .h5 {
    font-family: var(--heading-font);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-sm);
}

h6, .h6 {
    font-family: var(--heading-font);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--heading-color);
    margin-bottom: var(--space-xs);
}

small {
    font-family: var(--body-font);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-regular);
    line-height: var(--line-height-normal);
    color: var(--text-muted);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: var(--color-brown);
    text-decoration: none;
}

a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

a:hover::after {
    width: 100%;
}

blockquote {
    font-family: var(--heading-font);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-relaxed);
    color: var(--heading-color);
    font-style: italic;
    border-left: 4px solid var(--accent-color);
    padding-left: var(--space-lg);
    margin: var(--space-lg) 0;
}
