/* ====================================================================
   COMPONENTS / _MODALS.CSS
   ====================================================================

   BEM yapısına uygun modern modal stilleri.

   - .modal (Bootstrap'in ana sarmalayıcısı)
   - .modal__dialog (Bootstrap'in diyalog sarmalayıcısı)
   - .modal__content (Bizim ana BEM bloğumuz)
   - .modal__header
   - .modal__title
   - .modal__close
   - .modal__body
   - .modal__footer

/* ==================================================================== */

/* --- Modal Arka Planı --- */
.modal-backdrop {
  background-color: rgba(var(--color-brown-rgb), 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* --- Ana Modal Bileşeni --- */
.modal__content {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  background-color: var(--color-light-cream);
  box-shadow: 0 10px 40px rgba(var(--color-dark-rgb), 0.25);
  border: 1px solid rgba(var(--color-brown-rgb), 0.1);
  display: flex;
  flex-direction: column;
}

/* --- Modal Başlığı --- */
.modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-moss);
  color: var(--color-white);
  padding: 1rem 1.5rem;
  border-bottom: none;
  position: relative;
}

.modal__title {
  font-family: var(--font-family-heading);
  font-weight: 600;
  margin: 0;
  font-size: 1.3rem;
  color: var(--color-white);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal__close {
  background: transparent;
  border: none;
  color: var(--color-white);
  font-size: 1.2rem;
  line-height: 1;
  opacity: 0.8;
  padding: 0.5rem;
  margin: -0.5rem; /* Tıklama alanını genişletmek için */
  transition: all 0.2s ease-in-out;
}

.modal__close:hover,
.modal__close:focus {
  opacity: 1;
  transform: scale(1.1);
  color: var(--color-white);
  outline: none;
  box-shadow: none;
}

/* --- Modal Gövdesi --- */
.modal__body {
  padding: 1.5rem 2rem;
  color: var(--color-dark);
  line-height: 1.6;
  flex-grow: 1; /* İçerik az olsa bile footer'ı aşağı iter */
}

.modal__body p {
  margin-bottom: 1rem;
}

.modal__body p:last-child {
  margin-bottom: 0;
}

.modal__body .form__input {
  background-color: var(--color-white);
}

/* --- Modal Alt Bilgisi --- */
.modal__footer {
  padding: 1rem 1.5rem;
  background-color: rgba(var(--color-brown-rgb), 0.05);
  border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.75rem;
}

.modal__footer p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--color-brown);
}

.modal__footer .link-light {
  color: var(--color-moss) !important;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
}

.modal__footer .link-light:hover {
  color: var(--color-dark-moss) !important;
  text-decoration: underline;
}

/* Silme Modalı için özel stil */
.modal__body .fw-bold strong {
    color: var(--color-danger);
    font-size: 1.1rem;
}

.modal__body {
  padding: 2rem;
  background-color: var(--white);
}

.modal__footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem; /* Butonlar arası boşluk */
  background-color: var(--soft-light);
  padding: 1rem 1.5rem;
  border-top: none;
}

/* Modal içindeki form stilleri */
.modal .form__field .form__input,
.modal .form__field .form__textarea {
  background-color: var(--white) !important;
  border: 1px solid rgba(var(--color-moss-rgb), 0.3);
  color: var(--text-color);
}

.modal .form__field .form__input:focus,
.modal .form__field .form__textarea:focus {
  border-color: var(--color-moss);
  box-shadow: 0 0 0 0.25rem rgba(var(--color-moss-rgb), 0.25);
}

.modal .form__label {
  color: var(--text-color);
  font-weight: 500;
}
