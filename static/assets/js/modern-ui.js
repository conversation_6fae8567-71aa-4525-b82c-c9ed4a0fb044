/**
 * MODERN UI JAVASCRIPT
 * Küp Cadısı projesi için modern UI bileşenleri ve animasyonlar
 * Toast notifications, modals, loading states ve diğer interaktif elementler
 */

class ModernUI {
    constructor() {
        this.init();
    }

    init() {
        this.initAnimations();
        this.initToasts();
        this.initModals();
        this.initForms();
        this.initScrollAnimations();
        this.initLoadingStates();
    }

    // SAYFA YÜKLENİRKEN ANIMASYONLAR
    initAnimations() {
        // Intersection Observer ile scroll animasyonları
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    
                    // Animasyon sınıfını ekle
                    if (element.dataset.animation) {
                        element.classList.add(element.dataset.animation);
                    } else {
                        element.classList.add('animate-fade-in-up');
                    }
                    
                    // Delay varsa uygula
                    if (element.dataset.delay) {
                        element.style.animationDelay = element.dataset.delay + 'ms';
                    }
                    
                    observer.unobserve(element);
                }
            });
        }, observerOptions);

        // Animasyon yapılacak elementleri gözlemle
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Kartlar için staggered animasyon
        document.querySelectorAll('.modern-card').forEach((card, index) => {
            card.classList.add('animate-on-scroll');
            card.dataset.animation = 'animate-fade-in-up';
            card.dataset.delay = index * 100;
            observer.observe(card);
        });
    }

    // TOAST NOTIFICATION SİSTEMİ
    initToasts() {
        // Toast container oluştur
        if (!document.querySelector('.toast-container')) {
            const container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
    }

    showToast(message, type = 'info', duration = 5000) {
        const container = document.querySelector('.toast-container');
        const toast = document.createElement('div');
        
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const titles = {
            success: 'Başarılı',
            error: 'Hata',
            warning: 'Uyarı',
            info: 'Bilgi'
        };

        toast.className = `toast-modern toast-${type}`;
        toast.innerHTML = `
            <div class="toast-header-modern">
                <div class="toast-icon-modern">${icons[type]}</div>
                <h6 class="toast-title-modern">${titles[type]}</h6>
                <button class="toast-close-modern" onclick="this.closest('.toast-modern').remove()">×</button>
            </div>
            <div class="toast-body-modern">${message}</div>
        `;

        container.appendChild(toast);

        // Animasyon ile göster
        setTimeout(() => toast.classList.add('show'), 100);

        // Otomatik kaldır
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => toast.remove(), 300);
        }, duration);

        return toast;
    }

    // MODAL SİSTEMİ
    initModals() {
        // Modal açma
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-target]')) {
                e.preventDefault();
                const modalId = e.target.dataset.modalTarget;
                this.openModal(modalId);
            }
        });

        // Modal kapatma
        document.addEventListener('click', (e) => {
            if (e.target.matches('.modal-overlay-modern') || 
                e.target.matches('.modal-close-modern')) {
                this.closeModal(e.target.closest('.modal-overlay-modern'));
            }
        });

        // ESC tuşu ile modal kapatma
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal-overlay-modern.show');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    }

    openModal(modalId) {
        const modal = document.querySelector(modalId);
        if (modal) {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // FORM İYİLEŞTİRMELERİ
    initForms() {
        // Floating label forms
        document.querySelectorAll('.form-floating-modern input, .form-floating-modern textarea').forEach(input => {
            // Placeholder ekle
            if (!input.placeholder) {
                input.placeholder = ' ';
            }

            // Focus ve blur olayları
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', () => {
                if (!input.value) {
                    input.parentElement.classList.remove('focused');
                }
            });

            // Başlangıçta değer varsa label'ı yukarı taşı
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });

        // Form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                this.validateForm(form, e);
            });
        });

        // Real-time validation
        document.querySelectorAll('input, textarea, select').forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
        });
    }

    validateForm(form, event) {
        let isValid = true;
        const fields = form.querySelectorAll('input[required], textarea[required], select[required]');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        if (!isValid) {
            event.preventDefault();
            this.showToast('Lütfen tüm gerekli alanları doldurun.', 'error');
        }
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        // Required validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'Bu alan zorunludur.';
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Geçerli bir e-posta adresi girin.';
            }
        }

        // Phone validation
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = 'Geçerli bir telefon numarası girin.';
            }
        }

        // Update field appearance
        field.classList.remove('is-valid', 'is-invalid');
        const feedback = field.parentElement.querySelector('.invalid-feedback-modern, .valid-feedback-modern');
        if (feedback) feedback.remove();

        if (value) { // Sadece değer varsa validation göster
            if (isValid) {
                field.classList.add('is-valid');
            } else {
                field.classList.add('is-invalid');
                const feedbackEl = document.createElement('div');
                feedbackEl.className = 'invalid-feedback-modern';
                feedbackEl.textContent = message;
                field.parentElement.appendChild(feedbackEl);
            }
        }

        return isValid;
    }

    // SCROLL ANİMASYONLARI
    initScrollAnimations() {
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const href = this.getAttribute('href');

                // Boş href="#" veya geçersiz selector'ları kontrol et
                if (!href || href === '#' || href.length <= 1) {
                    return; // Boş veya geçersiz href'ler için işlem yapma
                }

                e.preventDefault();

                try {
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                } catch (error) {
                    console.warn('Geçersiz selector:', href);
                }
            });
        });

        // Scroll to top button
        const scrollTopBtn = document.querySelector('#scroll-top');
        if (scrollTopBtn) {
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    scrollTopBtn.style.display = 'flex';
                    scrollTopBtn.classList.add('animate-fade-in-up');
                } else {
                    scrollTopBtn.style.display = 'none';
                }
            });
        }
    }

    // LOADING STATES
    initLoadingStates() {
        // Form submit loading
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.classList.contains('btn-loading')) {
                    submitBtn.classList.add('btn-loading');
                    submitBtn.disabled = true;
                    
                    // 10 saniye sonra loading'i kaldır (timeout için)
                    setTimeout(() => {
                        submitBtn.classList.remove('btn-loading');
                        submitBtn.disabled = false;
                    }, 10000);
                }
            });
        });

        // AJAX loading için global function
        window.showLoading = () => {
            if (!document.querySelector('.loading-overlay')) {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <h3>Yükleniyor...</h3>
                    </div>
                `;
                document.body.appendChild(overlay);
            }
        };

        window.hideLoading = () => {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        };
    }

    // UTILITY FUNCTIONS
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Progress bar güncelleme
    updateProgress(selector, percentage) {
        const progressBar = document.querySelector(selector);
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
    }
}

// DOM yüklendiğinde ModernUI'yi başlat
document.addEventListener('DOMContentLoaded', () => {
    window.modernUI = new ModernUI();
    
    // Global toast function
    window.showToast = (message, type, duration) => {
        return window.modernUI.showToast(message, type, duration);
    };
});

// Django messages'ları toast olarak göster
document.addEventListener('DOMContentLoaded', () => {
    const messages = document.querySelectorAll('.alert');
    messages.forEach(message => {
        const type = message.classList.contains('alert-success') ? 'success' :
                    message.classList.contains('alert-danger') ? 'error' :
                    message.classList.contains('alert-warning') ? 'warning' : 'info';
        
        showToast(message.textContent.trim(), type);
        message.style.display = 'none';
    });
});
