/*
* CUSTOM CONFIRM DIALOG JS
* <PERSON><PERSON><PERSON> uygun <PERSON>zel onay penceresi
* Son güncelleme: 15.05.2025
*/

class CustomConfirm {
    constructor() {
        this.overlay = null;
        this.isOpen = false;
    }

    show(options = {}) {
        return new Promise((resolve) => {
            if (this.isOpen) {
                resolve(false);
                return;
            }

            const defaults = {
                title: 'Emin misiniz?',
                message: '<PERSON>u işlemi gerçekleştirmek istediğinizden emin misiniz?',
                warning: null,
                icon: 'bi-exclamation-triangle',
                confirmText: 'Evet, Sil',
                cancelText: 'İptal',
                confirmClass: 'custom-confirm-btn-confirm',
                cancelClass: 'custom-confirm-btn-cancel'
            };

            const config = { ...defaults, ...options };
            this.createDialog(config, resolve);
        });
    }

    createDialog(config, resolve) {
        // Overlay oluştur
        this.overlay = document.createElement('div');
        this.overlay.className = 'custom-confirm-overlay';

        // Dialog HTML'i
        this.overlay.innerHTML = `
            <div class="custom-confirm-dialog">
                <div class="custom-confirm-header">
                    <div class="custom-confirm-icon">
                        <i class="bi ${config.icon}"></i>
                    </div>
                    <h3 class="custom-confirm-title">${config.title}</h3>
                </div>
                <div class="custom-confirm-body">
                    <p class="custom-confirm-message">${config.message}</p>
                    ${config.warning ? `<div class="custom-confirm-warning">${config.warning}</div>` : ''}
                </div>
                <div class="custom-confirm-footer">
                    <button type="button" class="custom-confirm-btn ${config.cancelClass}" data-action="cancel">
                        <i class="bi bi-x-circle me-2"></i>${config.cancelText}
                    </button>
                    <button type="button" class="custom-confirm-btn ${config.confirmClass}" data-action="confirm">
                        <i class="bi bi-check-circle me-2"></i>${config.confirmText}
                    </button>
                </div>
            </div>
        `;

        // Event listener'ları ekle
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close(false, resolve);
            }
        });

        const buttons = this.overlay.querySelectorAll('.custom-confirm-btn');
        buttons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.getAttribute('data-action');
                this.close(action === 'confirm', resolve);
            });
        });

        // Keyboard events
        document.addEventListener('keydown', this.handleKeydown.bind(this, resolve));

        // DOM'a ekle ve göster
        document.body.appendChild(this.overlay);
        this.isOpen = true;

        // Animation için timeout
        setTimeout(() => {
            this.overlay.classList.add('show');
        }, 10);
    }

    handleKeydown(resolve, e) {
        if (!this.isOpen) return;

        if (e.key === 'Escape') {
            this.close(false, resolve);
        } else if (e.key === 'Enter') {
            this.close(true, resolve);
        }
    }

    close(result, resolve) {
        if (!this.isOpen) return;

        this.isOpen = false;
        this.overlay.classList.remove('show');

        setTimeout(() => {
            if (this.overlay && this.overlay.parentNode) {
                this.overlay.parentNode.removeChild(this.overlay);
            }
            this.overlay = null;
            document.removeEventListener('keydown', this.handleKeydown);
            resolve(result);
        }, 300);
    }
}

// Global instance
window.customConfirm = new CustomConfirm();

// Silme onayı için özel fonksiyon
window.confirmDelete = async function(event, deleteUrl, type = 'item') {
    event.preventDefault();

    const messages = {
        'calisma': {
            title: 'Çalışmayı Sil',
            message: 'Bu çalışmayı silmek istediğinizden emin misiniz?',
            warning: '<strong>Uyarı:</strong> Çalışma ile ilişkili tüm fotoğraflar da silinecektir.<br>Bu işlem geri alınamaz.'
        },
        'urun': {
            title: 'Ürünü Sil',
            message: 'Bu ürünü silmek istediğinizden emin misiniz?',
            warning: '<strong>Uyarı:</strong> Ürün ile ilişkili tüm fotoğraflar da silinecektir.<br>Bu işlem geri alınamaz.'
        },
        'video': {
            title: 'Videoyu Sil',
            message: 'Bu videoyu silmek istediğinizden emin misiniz?',
            warning: '<strong>Uyarı:</strong> Video kalıcı olarak silinecektir.<br>Bu işlem geri alınamaz.'
        }
    };

    const config = messages[type] || messages['item'];

    const result = await window.customConfirm.show({
        title: config.title,
        message: config.message,
        warning: config.warning,
        icon: 'bi-trash',
        confirmText: 'Evet, Sil',
        cancelText: 'İptal'
    });

    if (result) {
        // Form oluştur ve gönder
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = deleteUrl;

        // CSRF token ekle
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Form'u body'ye ekle ve gönder
        document.body.appendChild(form);
        form.submit();
    }
};
