# Uygulama Özel JavaScript Dosyaları

Bu klasör, her uygulama için özel JavaScript bileşenlerini içerir. Modern ES6+ sınıf yapısı kullanılarak geliştirilmiştir.

## 📁 Klasör Yapısı

```
static/assets/js/apps/
├── atolye/
│   ├── lightbox.js         # Lightbox bileşeni
│   ├── infinite-scroll.js  # Sonsuz kaydırma
│   ├── share.js           # Paylaşım işlevleri
│   ├── download.js        # İndirme işlevleri
│   └── video-player.js    # Video oynatıcı (gelecekte)
├── calismalar/            # Çalışmalar JS'leri (gelecekte)
├── urunler/              # Ürünler JS'leri (gelecekte)
└── README.md             # Bu dosya
```

## 🎯 Amaç

### Önceki Durum (Problemli)
- Atolye JS'leri: `atolye/static/atolye/js/` (ayrı klasör)
- Dağınık JavaScript dosyaları
- Tutars<PERSON>z kod yapısı
- Bağımlılık yönetimi sorunları

### Yeni Durum (Çözüm)
- Merkezi JavaScript klasörü
- Modern ES6+ sınıf yapısı
- Tutarlı API tasarımı
- Modüler ve yeniden kullanılabilir kod

## 🔧 Atolye JavaScript Bileşenleri

### 1. AtolyeLightbox (`lightbox.js`)
Modern lightbox implementasyonu.

**Özellikler:**
- Resim galerisi görüntüleme
- Zoom in/out işlevleri
- Klavye navigasyonu (ESC, Arrow keys, Space)
- Touch/swipe desteği (mobil)
- Responsive tasarım
- Accessibility desteği

**Kullanım:**
```javascript
// Otomatik başlatma
document.addEventListener('DOMContentLoaded', () => {
    window.atolyeLightbox = new AtolyeLightbox();
});

// Manuel açma
AtolyeLightbox.open(imageIndex);
```

### 2. AtolyeInfiniteScroll (`infinite-scroll.js`)
Instagram tarzı sonsuz kaydırma.

**Özellikler:**
- Otomatik içerik yükleme
- Performans optimizasyonu (requestAnimationFrame)
- Hata yönetimi
- Loading states
- URL güncelleme

**Kullanım:**
```javascript
// Otomatik başlatma
AtolyeInfiniteScroll.init();

// Manuel tetikleme
AtolyeInfiniteScroll.triggerLoad();
```

### 3. AtolyeShare (`share.js`)
Modern paylaşım işlevleri.

**Özellikler:**
- Native Web Share API desteği
- Sosyal medya entegrasyonu (Facebook, Twitter, WhatsApp, vb.)
- Link kopyalama
- Toast bildirimleri
- Modal interface

**Desteklenen Platformlar:**
- Facebook
- Twitter
- WhatsApp
- Pinterest
- Telegram
- LinkedIn

**Kullanım:**
```javascript
// Paylaşım modalını aç
AtolyeShare.share({
    title: 'Başlık',
    text: 'Açıklama',
    url: 'https://example.com',
    image: 'https://example.com/image.jpg'
});
```

### 4. AtolyeDownload (`download.js`)
Resim indirme işlevleri.

**Özellikler:**
- Direkt resim indirme
- Watermark ekleme
- Canvas manipülasyonu
- CORS desteği
- Dosya adı temizleme

**Kullanım:**
```javascript
// Resim indir
AtolyeDownload.download(imageUrl, filename, title);
```

## 🏗️ Kod Yapısı

### ES6+ Sınıf Yapısı
```javascript
class AtolyeComponent {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupUI();
    }

    bindEvents() {
        // Event listeners
    }

    // Public static methods
    static methodName() {
        // Static implementation
    }
}
```

### Global Erişim
```javascript
// Global window objesi üzerinden erişim
window.atolyeLightbox = new AtolyeLightbox();
window.atolyeShare = new AtolyeShare();
window.atolyeDownload = new AtolyeDownload();
window.atolyeInfiniteScroll = new AtolyeInfiniteScroll();
```

### Module Export Desteği
```javascript
// CommonJS export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AtolyeComponent;
}
```

## 📱 Responsive & Accessibility

### Mobil Desteği
- Touch events
- Swipe gestures
- Responsive breakpoints
- Mobile-first approach

### Accessibility
- ARIA labels
- Keyboard navigation
- Focus management
- Screen reader support
- High contrast mode
- Reduced motion support

## 🔄 Event System

### Event Delegation
Dinamik içerik için event delegation kullanılır:
```javascript
document.addEventListener('click', (e) => {
    if (e.target.closest('.component-trigger')) {
        // Handle event
    }
});
```

### Custom Events
```javascript
// Event dispatch
document.dispatchEvent(new CustomEvent('atolye:lightbox:opened', {
    detail: { imageIndex: 0 }
}));

// Event listener
document.addEventListener('atolye:lightbox:opened', (e) => {
    console.log('Lightbox opened:', e.detail);
});
```

## 🚀 Performance

### Optimizasyonlar
- Lazy loading
- Event throttling/debouncing
- RequestAnimationFrame kullanımı
- Memory leak prevention
- Efficient DOM manipulation

### Loading Strategy
```javascript
// DOM ready'de başlatma
document.addEventListener('DOMContentLoaded', () => {
    // Initialize components
});
```

## 🧪 Testing

### Test Edilmesi Gerekenler
- [ ] Cross-browser compatibility
- [ ] Mobile device testing
- [ ] Accessibility testing
- [ ] Performance testing
- [ ] Error handling

### Test Senaryoları
- Lightbox açma/kapama
- Infinite scroll tetikleme
- Paylaşım modal işlevleri
- İndirme işlemleri
- Keyboard navigation

## 🔧 Geliştirici Araçları

### Debug Mode
```javascript
// Console logging için
window.ATOLYE_DEBUG = true;
```

### Error Handling
```javascript
try {
    // Risky operation
} catch (error) {
    console.error('Atolye Error:', error);
    this.showToast('Bir hata oluştu.', 'error');
}
```

## 📋 Yapılacaklar

### Kısa Vadeli
- [ ] Video player bileşeni
- [ ] PWA JavaScript
- [ ] Unit testler
- [ ] JSDoc documentation

### Uzun Vadeli
- [ ] TypeScript migration
- [ ] Bundle optimization
- [ ] Service Worker integration
- [ ] Offline support

---

**Son Güncelleme**: 2025-06-16  
**Geliştirici**: Küp Cadısı Development Team  
**Versiyon**: 1.0.0
