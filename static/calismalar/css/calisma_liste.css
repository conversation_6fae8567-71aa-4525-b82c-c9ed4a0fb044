/* <PERSON><PERSON><PERSON><PERSON> */
 
.no-image-placeholder {
    width: 100%;
    height: 250px;
    background-color: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}
 
/* <PERSON><PERSON><PERSON> */
.modern-tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
}
 
.modern-tag {
    display: inline-block;
    padding: 8px 15px;
    background-color: rgba(var(--color-moss-rgb), 0.1);
    color: var(--color-moss);
    border-radius: 20px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--color-moss-rgb), 0.2);
    cursor: pointer;
}
 
.modern-tag:hover, .modern-tag.active {
    background-color: var(--color-moss);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(var(--color-moss-rgb), 0.3);
}
 
.filter-section {
    background-color: rgba(var(--color-sand-rgb), 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
}
 
.filter-title {
    color: var(--color-brown);
    font-size: 1.2rem;
    margin-bottom: 15px;
    border-bottom: 2px solid rgba(var(--color-brown-rgb), 0.2);
    padding-bottom: 10px;
}
 
.filter-title i {
    margin-right: 8px;
    color: var(--color-moss);
}
 
/* Arama Formu Stilleri */
.animated-search-group {
    position: relative;
    overflow: hidden;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.15);
    transition: all 0.3s ease;
}
 
.animated-search-group:hover, .animated-search-group:focus-within {
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.25);
    transform: translateY(-3px);
}
 
.animated-search-input {
    border: 1px solid rgba(var(--color-brown-rgb), 0.2);
    border-radius: 30px;
    padding: 12px 20px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: rgba(var(--color-sand-rgb), 0.05);
}
 
.animated-search-input:focus {
    border-color: var(--color-brown);
    box-shadow: none;
    background-color: white;
}
 
.modern-btn-search {
    position: absolute;
    right: 5px;
    top: 5px;
    bottom: 5px;
    border: none;
    background: var(--color-brown);
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
}
 
.modern-btn-search:hover {
    background: var(--color-moss);
    transform: rotate(15deg);
}
 
/* Arama Sonuçları Vurgusu */
.search-highlight {
    background-color: rgba(var(--color-brown-rgb), 0.2);
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: 500;
}