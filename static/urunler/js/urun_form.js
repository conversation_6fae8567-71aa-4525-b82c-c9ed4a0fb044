/**
 * <PERSON><PERSON>ün Formu JavaScript Dosyası
 * Bu dosya ürün ekleme/düzenleme formundaki işlevselliği yönetir.
 */

// Global değişkenler
let dropzoneInstance = null;
let isDropzoneInitialized = false;

// Dropzone'ın otomatik keşfini devre dışı bırak
if (typeof Dropzone !== 'undefined') {
    Dropzone.autoDiscover = false;
}

// CSRF token'ını al
function getCsrfToken() {
    // Önce meta etiketinden dene
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    // Sonra formdaki csrfmiddlewaretoken alanından dene
    const formToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (formToken) {
        return formToken.value;
    }
    
    // Son çare olarak cookie'den al
    const cookieValue = document.cookie
        .split('; ')
        .find(row => row.startsWith('csrftoken='))
        ?.split('=')[1];
        
    return cookieValue || '';
}

// Sayfa yüklendiğinde çalışacak fonksiyonlar
function initializePage() {
    console.log('Sayfa başlatılıyor...');
    
    // CSRF token'ını ayarla
    window.csrfToken = getCsrfToken();
    if (window.csrfToken) {
        console.log('CSRF token ayarlandı');
    } else {
        console.warn('CSRF token bulunamadı!');
    }
    
    // Dropzone'ı başlat
    initDropzone();
    
    console.log('Sayfa başarıyla başlatıldı');
}

/**
 * Toast bildirimi gösterir
 * @param {string} title - Başlık
 * @param {string} message - Mesaj içeriği
 * @param {string} type - Bildirim türü (success, error, warning, info)
 */
function showToast(title, message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Kapat"></button>
        </div>
    `;
    
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
        container.appendChild(toast);
    } else {
        toastContainer.appendChild(toast);
    }
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Toast otomatik kapanma
    setTimeout(() => {
        bsToast.hide();
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }, 5000);
}

/**
 * Dropzone'ı başlatır
 */
function initDropzone() {
    console.log('Dropzone başlatılıyor...');
    
    // Eğer zaten başlatıldıysa çık
    if (isDropzoneInitialized || dropzoneInstance) {
        console.log('Dropzone zaten başlatılmış');
        return dropzoneInstance;
    }
    
    const dropzoneElement = document.getElementById('image-upload');
    
    // Eğer dropzone elementi yoksa çık
    if (!dropzoneElement) {
        console.error('Dropzone elementi bulunamadı!');
        return null;
    }
    
    // Eğer Dropzone tanımlı değilse çık
    if (typeof Dropzone === 'undefined') {
        console.error('Dropzone kütüphanesi yüklenemedi!');
        // 1 saniye sonra tekrar dene
        setTimeout(initDropzone, 1000);
        return null;
    }
    
    // Dropzone için URL'yi oluştur
    const uploadUrl = '/urunler/api/upload-image/';
    console.log('Dropzone URL:', uploadUrl);
    
    // CSRF token'ını al
    const csrfTokenElement = document.querySelector('[name=csrfmiddlewaretoken]');
    let csrftoken = '';
    
    if (csrfTokenElement) {
        csrftoken = csrfTokenElement.value;
        console.log('CSRF Token: Bulundu');
    } else {
        console.error('CSRF Token: Bulunamadı!');
        return null;
    }
    
    try {
        // Dropzone'ı başlat
        dropzoneInstance = new Dropzone(dropzoneElement, {
            // Zorunlu ayarlar
            url: uploadUrl,
            method: 'POST',
            paramName: 'image',
            autoProcessQueue: true,
            uploadMultiple: false,
            maxFiles: 10,
            parallelUploads: 1,
            maxFilesize: 5, // MB
            acceptedFiles: 'image/jpeg,image/png,image/gif,image/webp',
            addRemoveLinks: true,
            
            // Çeviriler
            dictDefaultMessage: 'Resimleri buraya sürükleyin veya tıklayın',
            dictFallbackMessage: 'Tarayıcınız sürükle-bırak işlemini desteklemiyor.',
            dictFileTooBig: 'Dosya çok büyük ({{filesize}}MB). Maksimum dosya boyutu: {{maxFilesize}}MB.',
            dictInvalidFileType: 'Bu dosya türü desteklenmiyor. Sadece resim dosyaları yükleyebilirsiniz.',
            dictResponseError: 'Sunucu {{statusCode}} kodu ile yanıt verdi.',
            dictCancelUpload: 'Yüklemeyi iptal et',
            dictUploadCanceled: 'Yükleme iptal edildi.',
            dictRemoveFile: 'Dosyayı kaldır',
            dictMaxFilesExceeded: 'Daha fazla dosya yükleyemezsiniz. (Maksimum: {{maxFiles}})',
            
            // İstek başlıkları
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrftoken
            },
            
            // Özel ayarlar
            clickable: true,
            createImageThumbnails: true,
            thumbnailWidth: 120,
            thumbnailHeight: 120,
            maxThumbnailFilesize: 10, // MB
            
            // İşleyiciler
            init: function() {
                const dropzone = this;
                
                // Dosya eklendiğinde
                this.on('addedfile', function(file) {
                    console.log('Dosya eklendi:', file.name);
                });
                
                // Dosya gönderilirken
                this.on('sending', function(file, xhr, formData) {
                    console.log('Dosya gönderiliyor:', file.name);
                    
                    // Form verilerini ekle
                    const form = document.querySelector('form');
                    if (form) {
                        const formDataObj = new FormData(form);
                        for (let [key, value] of formDataObj.entries()) {
                            // product_id'yi özel olarak işle
                            if (key !== 'product_id') {
                                formData.append(key, value);
                            }
                        }
                    }
                    
                    // Eğer düzenleme modundaysak product_id'yi ekle
                    const productId = document.querySelector("input[name='product_id']");
                    if (productId && productId.value) {
                        formData.append("product_id", productId.value);
                    }
                    
                    // Butonu yükleme durumuna getir
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Yükleniyor...';
                    }
                });
                
                // Yükleme başarılı olduğunda
                this.on('success', function(file, response) {
                    console.log('Dosya başarıyla yüklendi:', response);
                    
                    // Eğer yanıt başarılıysa ve dosya URL'si varsa galeriye ekle
                    if (response.success && response.file_url) {
                        addImageToGallery({
                            id: response.image_id || Date.now(),
                            url: response.file_url,
                            is_main: response.is_main || false
                        });
                        
                        // Başarılı bildirimi göster
                        showToast('Başarılı!', 'Resim başarıyla yüklendi.' + (response.is_main ? ' Bu resim ana resim olarak ayarlandı.' : ''), 'success');
                    } else {
                        throw new Error(response.error || 'Resim yüklenirken bir hata oluştu.');
                    }
                    
                    // Butonu eski haline getir
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="fas fa-save me-1"></i> Kaydet';
                    }
                    
                    // Dosyayı dropzone'dan kaldır
                    this.removeFile(file);
                });
                
                // Yükleme hatası olduğunda
                this.on('error', function(file, errorMessage) {
                    console.error('Yükleme hatası:', errorMessage);
                    
                    let errorText = 'Resim yüklenirken bir hata oluştu.';
                    if (typeof errorMessage === 'string') {
                        errorText = errorMessage;
                    } else if (errorMessage && errorMessage.error) {
                        errorText = errorMessage.error;
                    } else if (errorMessage && errorMessage.message) {
                        errorText = errorMessage.message;
                    } else if (file.xhr && file.xhr.response) {
                        try {
                            const response = JSON.parse(file.xhr.response);
                            if (response.error) {
                                errorText = response.error;
                            } else if (response.message) {
                                errorText = response.message;
                            }
                        } catch (e) {
                            console.error('Hata yanıtı ayrıştırılamadı:', e);
                        }
                    }
                    
                    // Hata mesajını göster
                    showToast('Hata!', errorText, 'error');
                    
                    // Butonu eski haline getir
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="fas fa-save me-1"></i> Kaydet';
                    }
                    
                    // Hatalı dosyayı kaldır
                    if (file) {
                        this.removeFile(file);
                    }
                });
                
                // Tüm yüklemeler tamamlandığında
                this.on('queuecomplete', function() {
                    console.log('Tüm yüklemeler tamamlandı');
                    
                    // Butonu eski haline getir
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="fas fa-save me-1"></i> Kaydet';
                    }
                });
            }
        });
        
        // Dropzone başarıyla başlatıldı
        isDropzoneInitialized = true;
        console.log('Dropzone başarıyla başlatıldı');
        
        return dropzoneInstance;
    } catch (error) {
        console.error('Dropzone başlatılırken hata oluştu:', error);
        showToast('Hata!', 'Dropzone başlatılırken bir hata oluştu.', 'error');
        return null;
    }
}

/**
 * Galeriye resim ekler
 * @param {Object} image - Eklenecek resim nesnesi
 */
function addImageToGallery(image) {
    if (!image || !image.url) {
        console.error('Geçersiz resim nesnesi:', image);
        return;
    }
    
    console.log('Resim galeriye eklendi:', image);
    
    // Galeri konteynerını bul veya oluştur
    let galleryContainer = document.getElementById('current-images');
    if (!galleryContainer) {
        galleryContainer = document.createElement('div');
        galleryContainer.id = 'current-images';
        document.querySelector('.image-upload-container')?.after(galleryContainer);
    }
    
    // Resim kartını oluştur
    const imageCard = document.createElement('div');
    imageCard.className = 'image-card' + (image.is_main ? ' main-image' : '');
    imageCard.id = `image-${image.id || Date.now()}`;
    
    imageCard.innerHTML = `
        <img src="${image.url}" class="img-thumbnail" alt="Ürün resmi">
        <div class="image-actions">
            <button type="button" class="btn btn-sm btn-primary set-main-btn" 
                    data-image-id="${image.id}" title="Ana resim yap">
                <i class="fas fa-star"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger delete-image-btn" 
                    data-image-id="${image.id}" title="Resmi sil">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    // Galeriye ekle
    galleryContainer.appendChild(imageCard);
    
    // Ana resim butonuna tıklama olayı ekle
    const setMainBtn = imageCard.querySelector('.set-main-btn');
    if (setMainBtn) {
        setMainBtn.addEventListener('click', function() {
            const imageId = this.getAttribute('data-image-id');
            setAsMainImage(imageId);
        });
    }
    
    // Silme butonuna tıklama olayı ekle
    const deleteBtn = imageCard.querySelector('.delete-image-btn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            const imageId = this.getAttribute('data-image-id');
            deleteImage(imageId, imageCard);
        });
    }
}

/**
 * Resmi ana resim olarak işaretler
 * @param {string} imageId - Resim ID'si
 */
function setAsMainImage(imageId) {
    if (!imageId) return;
    
    // Tüm ana resim işaretlerini kaldır
    document.querySelectorAll('.main-image').forEach(el => {
        el.classList.remove('main-image');
    });
    
    // Yeni ana resmi işaretle
    const newMainImage = document.getElementById(`image-${imageId}`);
    if (newMainImage) {
        newMainImage.classList.add('main-image');
    }
    
    // AJAX isteği ile sunucuya ana resim olarak ayarlama isteği gönder
    const productId = document.querySelector("input[name='product_id']")?.value;
    if (!productId) {
        console.error('Ürün ID\'si bulunamadı!');
        showToast('Hata!', 'Ürün ID\'si bulunamadı!', 'error');
        return;
    }
    
    fetch(`/urunler/api/products/${productId}/set-main-image/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': window.csrfToken || ''
        },
        body: JSON.stringify({
            image_id: imageId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Ana resim başarıyla güncellendi:', data);
            showToast('Başarılı!', 'Ana resim başarıyla güncellendi.', 'success');
        } else {
            throw new Error(data.message || 'Ana resim güncellenirken bir hata oluştu.');
        }
    })
    .catch(error => {
        console.error('Ana resim güncellenirken hata oluştu:', error);
        showToast('Hata!', error.message || 'Ana resim güncellenirken bir hata oluştu.', 'error');
        
        // Hata durumunda arayüzü geri al
        if (newMainImage) {
            newMainImage.classList.remove('main-image');
        }
    });
}

/**
 * Resmi siler
 * @param {string} imageId - Silinecek resim ID'si
 * @param {HTMLElement} imageCard - Silinecek resim kartı elementi
 */
function deleteImage(imageId, imageCard) {
    if (!imageId || !imageCard) return;
    
    // Kullanıcıya onay sor
    if (!confirm('Bu resmi silmek istediğinizden emin misiniz?')) {
        return;
    }
    
    console.log('Resim siliniyor:', imageId);
    
    // AJAX isteği ile sunucuya silme isteği gönder
    fetch(`/urunler/api/images/${imageId}/delete/`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Resim kartını kaldır
            imageCard.remove();
            showToast('Başarılı!', 'Resim başarıyla silindi.', 'success');
        } else {
            throw new Error(data.message || 'Resim silinirken bir hata oluştu.');
        }
    })
    .catch(error => {
        console.error('Resim silinirken hata oluştu:', error);
        showToast('Hata!', error.message || 'Resim silinirken bir hata oluştu.', 'error');
    });
}

// Sayfa yüklendiğinde çalışacak fonksiyon
function initDeleteButtons() {
    console.log('Silme butonları başlatılıyor...');
    
    // Tüm silme butonlarını seç (hem doğrudan hem de içindeki ikonlar için)
    const deleteButtons = [
        ...document.querySelectorAll('.btn-delete-image'),
        ...document.querySelectorAll('.btn-delete-image .fa-trash')
    ];
    
    console.log('Aranan butonlar:', '.btn-delete-image');
    
    console.log('Bulunan silme butonları:', deleteButtons.length);
    
    // Eğer buton bulunamadıysa, sayfada arama yapalım
    if (deleteButtons.length === 0) {
        console.warn('Silme butonları bulunamadı, sayfa taranıyor...');
        console.log('Tüm butonlar:', document.querySelectorAll('button'));
        console.log('Tüm elementler:', document.querySelectorAll('*'));
        return;
    }
    
    // Her butona ayrı ayrı event listener ekle
    deleteButtons.forEach(button => {
        // Eğer bu buton zaten işlenmişse atla
        if (button.dataset.deleteHandler === 'true') return;
        
        console.log('Event listener ekleniyor:', button);
        
        // Önceki tüm click eventlerini kaldır
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Yeni event listener ekle
        newButton.addEventListener('click', function handleDeleteClick(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            
            console.log('Sil butonuna tıklandı:', this);
            
            // Eğer tıklanan öğe ikonsa, butonu bul
            const actualButton = this.classList.contains('btn-delete-image') ? this : this.closest('.btn-delete-image');
            if (!actualButton) {
                console.error('Silme butonu bulunamadı');
                return;
            }
            
            const imageId = actualButton.dataset.imageId;
            const imageCard = actualButton.closest('.image-card');
            
            console.log('Image ID:', imageId, 'Image Card:', imageCard);
            
            if (imageId && imageCard) {
                deleteImage(imageId, imageCard);
            } else {
                console.error('Silme işlemi için gerekli veriler bulunamadı:', {
                    hasImageId: !!imageId,
                    hasImageCard: !!imageCard,
                    button: actualButton
                });
            }
            
            return false;
        }, true); // capture: true ile en üst seviyeden yakala
        
        // Bu butonun işlendiğini işaretle
        newButton.dataset.deleteHandler = 'true';
        
        // Hata ayıklama için global erişim sağla
        if (!window._deleteButtons) window._deleteButtons = [];
        window._deleteButtons.push(newButton);
    });
    
    console.log('Silme butonları başarıyla başlatıldı');
}

// Hata ayıklama için global erişim sağla
window.initDeleteButtons = initDeleteButtons;

// Fiyat alanı işlevselliği
function setupPriceInput() {
    const priceInput = document.getElementById('id_price');
    const priceHidden = document.getElementById('price_hidden');
    
    if (!priceInput || !priceHidden) return;
    
    // Sayfa yüklendiğinde fiyatı formatla
    const formatPrice = (value) => {
        // Tüm noktaları kaldır ve virgülü noktaya çevir
        let num = value.replace(/\./g, '').replace(',', '.');
        // Geçerli bir sayıya çevir
        num = parseFloat(num);
        if (isNaN(num)) return '0,00';
        
        // Gizli alana nokta ile kaydet (örn: 1000.00)
        priceHidden.value = num.toFixed(2);
        
        // Görüntüleme için binlik ayraçlı ve virgüllü format (örn: 1.000,00)
        return num.toFixed(2)
                 .replace(/\./g, '|')  // Noktaları geçici olarak değiştir
                 .replace(/,/g, '.')    // Virgülü noktaya çevir
                 .replace(/\|/g, ',')   // Geçici işaretleri virgül yap
                 .replace(/\B(?=(\d{3})+(?!\d))/g, "."); // Binlik ayraç ekle
    };
    
    // Sayfa yüklendiğinde mevcut değeri formatla
    if (priceInput.value) {
        priceInput.value = formatPrice(priceInput.value);
    }
    
    // Kullanıcı giriş yaptığında
    priceInput.addEventListener('input', function(e) {
        // Sadece rakam, nokta ve virgüle izin ver
        this.value = this.value.replace(/[^0-9,.]/g, '')
                             // Sadece bir ondalık ayracına izin ver
                             .replace(/([,.])[^,.]*$/, '$1')
                             // Noktaları binlik ayracı olarak kabul et
                             .replace(/\.(?=.*\.)/g, '');
    });
    
    // Input'tan çıkıldığında formatı düzelt
    priceInput.addEventListener('blur', function() {
        this.value = formatPrice(this.value);
    });
}

// Sayfa yüklendiğinde çalışacak fonksiyon
function initApp() {
    console.log('Uygulama başlatılıyor...');
    
    // Eğer silme modalı açıksa kapat
    const deleteModal = document.getElementById('deleteModal');
    if (deleteModal && deleteModal.classList.contains('show')) {
        deleteModal.classList.remove('show');
        deleteModal.style.display = 'none';
        document.body.classList.remove('modal-open');
        console.log('Açık olan silme modalı kapatıldı');
    }
    
    // Dropzone'ı başlat
    //initDropzone();
    
    // Silme butonlarını başlat
    initDeleteButtons();
    
    // Fiyat giriş alanını başlat
    setupPriceInput();
    
    // WOW.js başlat
    if (typeof WOW !== 'undefined') {
        new WOW().init();
    }
    
    // 5 saniye sonra da bir kontrol yapalım
    setTimeout(() => {
        if (document.querySelectorAll('.delete-image-btn').length === 0) {
            console.warn('Dropzone yüklenemedi, manuel kontrol yapılıyor...');
            initDeleteButtons();
        }
    }, 5000);
    
    // Hata ayıklama için
    console.log('Uygulama başlatma tamamlandı');
}

// Sayfa yüklendiğinde başlat
if (document.readyState === 'loading') {
    // Hala yükleniyorsa, event listener ekle
    document.addEventListener('DOMContentLoaded', initApp);
} else {
    // DOM zaten yüklendi, hemen başlat
    initApp();
}
