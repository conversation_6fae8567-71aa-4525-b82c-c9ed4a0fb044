// Ürün Listesi Sayfası JavaScript Kodları
document.addEventListener('DOMContentLoaded', function() {
    // Arama formu submit işlemi
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const searchInput = this.querySelector('input[name="q"]');
            if (searchInput && searchInput.value.trim() === '') {
                e.preventDefault();
            }
        });
    }

    // Arama sonuçlarında vurgulama
    const searchQuery = new URLSearchParams(window.location.search).get('q');
    if (searchQuery) {
        highlightSearchResults(searchQuery);
    }

    // Filtreleme işlemleri
    setupFiltering();
});

// Arama sonuçlarını vurgula
function highlightSearchResults(query) {
    const elements = document.querySelectorAll('.product-description, .product-title');
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 2);
    
    if (searchTerms.length === 0) return;

    elements.forEach(element => {
        let html = element.innerHTML;
        searchTerms.forEach(term => {
            const regex = new RegExp(`(${term})`, 'gi');
            html = html.replace(regex, '<span class="search-highlight">$1</span>');
        });
        element.innerHTML = html;
    });
}

// Filtreleme işlemlerini ayarla
function setupFiltering() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterValue = this.getAttribute('data-filter');
            
            // Tüm ürünleri gizle
            document.querySelectorAll('.product-item').forEach(item => {
                item.style.display = 'none';
            });
            
            // Seçili filtreye göre ürünleri göster
            if (filterValue === 'all') {
                document.querySelectorAll('.product-item').forEach(item => {
                    item.style.display = 'block';
                });
            } else {
                document.querySelectorAll(`.${filterValue}`).forEach(item => {
                    item.style.display = 'block';
                });
            }
            
            // Aktif butonu güncelle
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            this.classList.add('active');
        });
    });
}
