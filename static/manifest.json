{"name": "Küp Cadısı Atolye", "short_name": "<PERSON><PERSON><PERSON>", "description": "Küp Cadısı Atolye - <PERSON><PERSON><PERSON>, du<PERSON><PERSON><PERSON> ve videolar", "start_url": "/atolye/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#007bff", "orientation": "portrait-primary", "scope": "/", "lang": "tr", "dir": "ltr", "categories": ["art", "photography", "social"], "screenshots": [], "icons": [{"src": "/static/assets/img/favicon/favicon-16x16.png", "sizes": "16x16", "type": "image/png", "purpose": "any"}, {"src": "/static/assets/img/favicon/favicon-32x32.png", "sizes": "32x32", "type": "image/png", "purpose": "any"}, {"src": "/static/assets/img/favicon/favicon.ico", "sizes": "48x48", "type": "image/x-icon", "purpose": "any"}, {"src": "/static/assets/img/favicon/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any maskable"}], "shortcuts": [{"name": "<PERSON><PERSON><PERSON>", "short_name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>sim galerisi ve du<PERSON>lar", "url": "/atolye/", "icons": [{"src": "/static/assets/img/favicon/favicon-32x32.png", "sizes": "32x32"}]}, {"name": "Çalışmalar", "short_name": "Çalışmalar", "description": "Tam<PERSON>lanmış projeler", "url": "/calismalar/", "icons": [{"src": "/static/assets/img/favicon/favicon-32x32.png", "sizes": "32x32"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "short_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "/urunler/", "icons": [{"src": "/static/assets/img/favicon/favicon-32x32.png", "sizes": "32x32"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}