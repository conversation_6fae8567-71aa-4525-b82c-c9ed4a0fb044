/* Küp Cadısı Admin Panel Özel Stilleri */

/* Üst navbar'ı sadeleştir */
.navbar {
    padding: 0.5rem 1rem !important;
    min-height: 50px !important;
}

/* Site başlığını sadeleştir */
.navbar-brand {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
}

/* Arama kutularını küçült */
.navbar .form-control {
    font-size: 0.9rem !important;
    padding: 0.375rem 0.75rem !important;
    max-width: 200px !important;
}

/* Üst menü linklerini sadeleştir */
.navbar-nav .nav-link {
    font-size: 0.9rem !important;
    padding: 0.5rem 0.75rem !important;
}

/* Dashboard başlığını sadeleştir */
.content-header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    color: #2c3e50 !important;
}

/* Breadcrumb'ı sadeleştir */
.breadcrumb {
    background: transparent !important;
    padding: 0.25rem 0 !important;
    margin-bottom: 1rem !important;
}

.breadcrumb-item {
    font-size: 0.9rem !important;
}

/* Sidebar'ı temizle */
.main-sidebar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Sidebar menü öğelerini sadeleştir */
.nav-sidebar .nav-link {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
}

/* Dashboard kartlarını iyileştir */
.card {
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
}

.card-header {
    background: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef !important;
    padding: 0.75rem 1rem !important;
}

.card-title {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
    color: #2c3e50 !important;
}

/* Liste sayfalarını iyileştir */
.results {
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

.results table {
    margin: 0 !important;
}

.results th {
    background: #f8f9fa !important;
    border-bottom: 2px solid #e9ecef !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    padding: 0.75rem !important;
}

.results td {
    padding: 0.75rem !important;
    border-bottom: 1px solid #f1f3f4 !important;
}

/* Butonları iyileştir */
.btn {
    border-radius: 6px !important;
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
}

.btn-primary {
    background: #007bff !important;
    border-color: #007bff !important;
}

.btn-success {
    background: #28a745 !important;
    border-color: #28a745 !important;
}

.btn-warning {
    background: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

.btn-danger {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
}

/* Form alanlarını iyileştir */
.form-control {
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
}

.form-control:focus {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* Fieldset'leri iyileştir */
fieldset {
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
}

fieldset legend {
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    padding: 0 0.5rem !important;
    margin-bottom: 0 !important;
}

/* Responsive iyileştirmeler */
@media (max-width: 768px) {
    .navbar .form-control {
        max-width: 150px !important;
        font-size: 0.8rem !important;
    }
    
    .content-header h1 {
        font-size: 1.3rem !important;
    }
    
    .results th,
    .results td {
        padding: 0.5rem !important;
        font-size: 0.9rem !important;
    }
}

/* Özel admin etiketleri için stiller */
.admin-tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.admin-tag-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.admin-tag-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.admin-tag-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.admin-tag-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Loading animasyonu */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Hover efektleri */
.results tbody tr:hover {
    background-color: #f8f9fa !important;
}

.nav-sidebar .nav-link:hover {
    background-color: rgba(255,255,255,0.1) !important;
}

/* Scroll bar'ları iyileştir */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
