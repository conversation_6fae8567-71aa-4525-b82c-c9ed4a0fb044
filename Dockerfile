# Python'ın resmi imajını temel al
FROM python:3.11-slim

# Ortam değişkenleri
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Çalışma dizini oluştur
WORKDIR /app

# Bağımlılıkları kopyala ve kur
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Proje dosyalarını kopyala
COPY . /app/

# Uygulamanın çalışacağı portu belirt
EXPOSE 8000

# Uygulamayı başlat
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
