/*
* ÜYELİK PROFİL STYLES CSS
* Üyelik profil sayfası için özel stiller
* Ana tasarım sistemi ile uyumlu hale getirildi
*/

/* Ana tasarım sistemi değişkenlerini içe aktar */
@import url('../../../assets/css/_variables.css');

/* Çalışma kartı stilleri artık modern-cards.css dosyasında yönetilmektedir. */
/* Profil Kartı */
.profil-karti {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.profil-karti:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(var(--color-brown-rgb), 0.15);
}

.profil-karti__baslik {
    background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-navy) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.profil-karti__baslik::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.profil-karti__avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid rgba(255,255,255,0.3);
    margin: 0 auto 1rem;
    display: block;
    object-fit: cover;
    position: relative;
    z-index: 1;
}

.profil-karti__isim {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.profil-karti__eposta {
    opacity: 0.9;
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

.profil-karti__govde {
    padding: 2rem;
}

/* Profil İstatistikleri */
.profil-karti__istatistikler {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.profil-karti__istatistik-oge {
    text-align: center;
    padding: 1rem;
    background: rgba(var(--color-sand-rgb), 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.profil-karti__istatistik-oge:hover {
    background: rgba(var(--color-sand-rgb), 0.2);
    transform: translateY(-3px);
}

.profil-karti__istatistik-sayi {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-brown);
    display: block;
}

.profil-karti__istatistik-etiket {
    font-size: 0.9rem;
    color: var(--text-soft);
    margin-top: 0.5rem;
}



/* Responsive Düzenlemeler */
@media (max-width: 991.98px) {
    .profil-karti__istatistikler {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767.98px) {
    .profil-karti__baslik {
        padding: 1.5rem;
    }
    
    .profil-karti__avatar {
        width: 100px;
        height: 100px;
    }
    
    .profil-karti__isim {
        font-size: 1.3rem;
    }
    
    .profil-karti__govde {
        padding: 1.5rem;
    }
    
    .profil-karti__istatistikler {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Boş durum mesajları */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-soft);
}

.empty-state p {
    font-size: 1rem;
    line-height: 1.6;
}
