from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.utils.html import format_html
from django.contrib import messages
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Profil
from utils.permissions import get_user_permission_summary

@admin.action(description="Seçili kullanıcıları onayla")
def approve_users(modeladmin, request, queryset):
    updated = queryset.update(is_approved=True)
    messages.success(request, f'{updated} kullanıcı başarıyla onaylandı.')

@admin.action(description="<PERSON>çili kullanıcıların onayını kaldır")
def unapprove_users(modeladmin, request, queryset):
    updated = queryset.update(is_approved=False)
    messages.success(request, f'{updated} kullanıcının onayı kaldırıldı.')

class ProfilAdmin(admin.ModelAdmin):
    list_display = ('user', 'get_full_name', 'telefon', 'approval_status', 'is_staff_user')
    list_filter = ('is_approved', 'user__is_staff', 'user__is_superuser')
    search_fields = ('user__username', 'user__email', 'ad', 'soyad', 'telefon')
    actions = [approve_users, unapprove_users]
    list_per_page = 20
    
    fieldsets = (
        ('Kullanıcı Bilgileri', {
            'fields': ('user', 'is_approved')
        }),
        ('Kişisel Bilgiler', {
            'fields': ('ad', 'soyad', 'telefon', 'adres', 'profile_picture')
        }),
        ('Adres Bilgileri', {
            'fields': ('sehir', 'ilce', 'ulke'),
            'classes': ('collapse',)
        }),
        ('Diğer Bilgiler', {
            'fields': ('dogum_tarihi', 'cinsiyet', 'aciklama'),
            'classes': ('collapse',)
        }),
        ('İletişim Tercihleri', {
            'fields': ('email_public', 'phone_public'),
        }),
    )
    
    def get_full_name(self, obj):
        if obj.ad and obj.soyad:
            return f"{obj.ad} {obj.soyad}"
        elif obj.ad:
            return obj.ad
        elif obj.soyad:
            return obj.soyad
        return "İsim belirtilmemiş"
    get_full_name.short_description = "Ad Soyad"
    
    def approval_status(self, obj):
        if obj.is_approved:
            return format_html('<span style="color:green; font-weight:bold;">✓ Onaylı</span>')
        return format_html('<span style="color:red; font-weight:bold;">✗ Onaysız</span>')
    approval_status.short_description = "Onay Durumu"
    
    def is_staff_user(self, obj):
        if obj.user.is_superuser:
            return format_html('<span style="color:purple; font-weight:bold;">Süper Kullanıcı</span>')
        elif obj.user.is_staff:
            return format_html('<span style="color:blue; font-weight:bold;">Yönetici</span>')
        return format_html('<span style="color:gray;">Normal Kullanıcı</span>')
    is_staff_user.short_description = "Kullanıcı Türü"

# Gelişmiş User Admin
class CustomUserAdmin(BaseUserAdmin):
    list_display = ('username', 'email', 'get_full_name', 'user_type_display',
                   'permission_summary', 'is_active', 'date_joined')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'groups')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('-date_joined',)
    list_per_page = 25

    # Fieldsets'i daha kullanıcı dostu hale getir
    fieldsets = (
        ('🔐 Temel Bilgiler', {
            'fields': ('username', 'password'),
            'description': 'Kullanıcının giriş bilgileri'
        }),
        ('👤 Kişisel Bilgiler', {
            'fields': ('first_name', 'last_name', 'email'),
            'description': 'Kullanıcının kişisel bilgileri'
        }),
        ('🔑 Yetki ve İzinler', {
            'fields': ('is_active', 'is_staff', 'is_superuser'),
            'description': 'Kullanıcının sistem yetkileri'
        }),
        ('👥 Grup Üyelikleri', {
            'fields': ('groups',),
            'description': 'Kullanıcının dahil olduğu izin grupları'
        }),
        ('🎯 Özel İzinler', {
            'fields': ('user_permissions',),
            'classes': ('collapse',),
            'description': 'Kullanıcıya özel olarak verilen izinler'
        }),
        ('📅 Tarih Bilgileri', {
            'fields': ('last_login', 'date_joined'),
            'classes': ('collapse',),
            'description': 'Kullanıcının sistem tarihçesi'
        }),
    )

    add_fieldsets = (
        ('🆕 Yeni Kullanıcı Oluştur', {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2'),
            'description': 'Yeni kullanıcı hesabı oluşturmak için gerekli bilgiler'
        }),
    )

    def get_full_name(self, obj):
        if obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        elif obj.first_name:
            return obj.first_name
        elif obj.last_name:
            return obj.last_name
        return "İsim belirtilmemiş"
    get_full_name.short_description = "Ad Soyad"

    def user_type_display(self, obj):
        if obj.is_superuser:
            return format_html(
                '<span style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); '
                'color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; '
                'font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">'
                '👑 Süper Yönetici</span>'
            )
        elif obj.is_staff:
            return format_html(
                '<span style="background: linear-gradient(45deg, #3742fa, #2f3542); '
                'color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; '
                'font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">'
                '🛡️ Yönetici</span>'
            )
        elif obj.groups.exists():
            return format_html(
                '<span style="background: linear-gradient(45deg, #00d2d3, #54a0ff); '
                'color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; '
                'font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">'
                '👥 Grup Üyesi</span>'
            )
        else:
            return format_html(
                '<span style="background: linear-gradient(45deg, #a4b0be, #747d8c); '
                'color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; '
                'font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">'
                '👤 Normal Kullanıcı</span>'
            )
    user_type_display.short_description = "Kullanıcı Türü"

    def permission_summary(self, obj):
        try:
            summary = get_user_permission_summary(obj)
            group_count = len(summary['groups'])
            permission_count = summary['permission_count']

            if obj.is_superuser:
                return format_html(
                    '<div style="text-align: center;">'
                    '<span style="color: #e74c3c; font-weight: bold;">∞ Tüm İzinler</span><br>'
                    '<small style="color: #7f8c8d;">Süper kullanıcı</small>'
                    '</div>'
                )

            group_text = f"{group_count} grup" if group_count > 0 else "Grup yok"
            permission_text = f"{permission_count} izin" if permission_count > 0 else "İzin yok"

            color = "#27ae60" if permission_count > 0 else "#e74c3c"

            return format_html(
                '<div style="text-align: center;">'
                '<span style="color: {}; font-weight: bold;">{}</span><br>'
                '<small style="color: #7f8c8d;">{}</small>'
                '</div>',
                color, permission_text, group_text
            )
        except Exception:
            return format_html('<span style="color: #e74c3c;">Hata</span>')
    permission_summary.short_description = "İzin Özeti"

# User modelini yeniden kaydet
admin.site.unregister(User)
admin.site.register(User, CustomUserAdmin)
admin.site.register(Profil, ProfilAdmin)
