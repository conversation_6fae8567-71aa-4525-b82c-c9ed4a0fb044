{% extends 'base.html' %}
{% load static %}

{% block title %}Profil - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/global-components.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'uyelik/css/profil.css' %}">
<link rel="stylesheet" href="{% static 'uyelik/css/profil-tabs.css' %}">
{% block profile_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Profil Başlık -->
        <div class="modern-header mb-5 text-center wow fadeIn" data-wow-delay="0.2s">
            <h1 class="gradient-heading display-4 fw-bold mb-3">{% block profile_title %}Profil{% endblock %}</h1>
            <p class="lead text-muted">{% block profile_subtitle %}Profil bilgilerinizi görüntüleyin{% endblock %}</p>
        </div>

        <!-- Profil Kartı -->
        <div class="profil-karti wow fadeInUp" data-wow-delay="0.3s">
            <div class="profil-karti__baslik">
                {% if is_own_profile %}
                    <div class="profile-avatar-container">
                        {% if user.profil.profile_picture %}
                            <img src="{{ user.profil.profile_picture.url }}" alt="Profil Resmi" class="profil-karti__avatar">
                        {% else %}
                            <img src="{% static 'assets/img/default-profile.png' %}" alt="Varsayılan Profil Resmi" class="profil-karti__avatar">
                        {% endif %}
                    </div>
                    <div class="profil-karti__bilgi">
                        <h4>{{ user.username }}</h4>
                        <p>{% if user.profil.ad and user.profil.soyad %}{{ user.profil.ad }} {{ user.profil.soyad }}{% else %}Profil bilgilerinizi güncelleyin{% endif %}</p>
                        {% if user.profil.adres %}
                            <div class="mt-1 text-muted small">
                                <i class="bi bi-geo-alt me-1"></i> {{ user.profil.adres }}
                            </div>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="profile-avatar-container">
                        {% if profil.profile_picture %}
                            <img src="{{ profil.profile_picture.url }}" alt="Profil Resmi" class="profil-karti__avatar">
                        {% else %}
                            <img src="{% static 'assets/img/default-profile.png' %}" alt="Varsayılan Profil Resmi" class="profil-karti__avatar">
                        {% endif %}
                    </div>
                    <div class="profil-karti__bilgi">
                        <h4>{{ viewed_user.username }}</h4>
                        <p>{% if profil.ad and profil.soyad %}{{ profil.ad }} {{ profil.soyad }}{% else %}Profil bilgileri belirtilmemiş{% endif %}</p>
                        {% if profil.adres %}
                            <div class="mt-1 text-muted small">
                                <i class="bi bi-geo-alt me-1"></i> {{ profil.adres }}
                            </div>
                        {% endif %}
                    </div>
                {% endif %}

                {% if is_own_profile %}
                <div class="profil-karti__duzenle-buton">
                    <a href="{% url 'uyelik:profil_duzenle' %}" class="button button--primary">
                        <i class="bi bi-pencil-square"></i>
                        Profili Düzenle
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- Profil Sekmeleri -->
            <nav class="tabs">
                <ul class="tabs__list">
                    <li class="tabs__item">
                        <a href="{% if is_own_profile %}{% url 'uyelik:profil' %}{% else %}{% url 'uyelik:kullanici_profil' viewed_user.username %}{% endif %}"
                           class="tabs__link {% block profile_tab_active %}{% endblock %}">
                            <i class="bi bi-person-lines-fill"></i> Profil
                        </a>
                    </li>
                    <li class="tabs__item">
                        <a href="{% if is_own_profile %}{% url 'uyelik:profil_hakkimda' %}{% else %}{% url 'uyelik:kullanici_hakkinda' viewed_user.username %}{% endif %}"
                           class="tabs__link {% block about_tab_active %}{% endblock %}">
                            <i class="bi bi-info-circle"></i> Hakkımda
                        </a>
                    </li>
                    <li class="tabs__item">
                        <a href="{% if is_own_profile %}{% url 'uyelik:profil_calismalarim' %}{% else %}{% url 'uyelik:kullanici_calismalar' viewed_user.username %}{% endif %}"
                           class="tabs__link {% block works_tab_active %}{% endblock %}">
                            <i class="bi bi-journal-text"></i> Çalışmalar
                        </a>
                    </li>
                    <li class="tabs__item">
                        <a href="{% if is_own_profile %}{% url 'uyelik:profil_arkadaslar' %}{% else %}{% url 'uyelik:kullanici_arkadaslar' viewed_user.username %}{% endif %}"
                           class="tabs__link {% block friends_tab_active %}{% endblock %}">
                            <i class="bi bi-people"></i> Arkadaşlar
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Profil İçeriği -->
        <div class="profil-karti__icerik mt-4">
            <div class="row">
                <div class="col-lg-12">
                    {% block profile_content %}
                    <!-- Bu kısım alt şablonlarda doldurulacak -->
                    {% endblock %}
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
