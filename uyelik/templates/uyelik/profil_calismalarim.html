{% extends 'uyelik/profil_base.html' %}

{% block works_tab_active %}active{% endblock %}
{% load static %}

{% block title %}Çalışmalarım - Küp Cadısı{% endblock %}

{% block profile_title %}Çalışmalarım{% endblock %}
{% block profile_subtitle %}Eklediğiniz çalışmaları görüntüleyin ve yönetin{% endblock %}

{% block profile_extra_css %}
{% endblock %}

{% block profile_content %}
<div class="card wow fadeInUp" data-wow-delay="0.4s">
    <div class="card__header">
        <h3 class="card__title"><i class="bi bi-journal-text me-2"></i>Çalışmalarım</h3>
        {% if is_own_profile %}
        <div class="card__actions">
            <a href="{% url 'calismalar:calisma_ekle' %}?return_url={% url 'uyelik:profil' %}%23works" class="button button--primary button--with-icon">
                <i class="bi bi-plus-circle"></i>
                <span>Yeni <PERSON></span>
            </a>
        </div>
        {% endif %}
    </div>
    <div class="card__body">
        <div class="row g-4">
            {% for calisma in calismalar %}
            <div class="col-md-6 col-lg-4">
                <div class="card card--work h-100">
                    <div class="card__header">
                        <h4 class="card__title">
                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}">{{ calisma.baslik }}</a>
                        </h4>
                    </div>
                    <div class="card__body">
                        <p class="card__text">{{ calisma.aciklama|truncatechars:100 }}</p>
                    </div>
                    <div class="card__footer">
                        <span class="card__meta"><i class="bi bi-calendar3 me-1"></i>{{ calisma.olusturma_tarihi|date:"d.m.Y" }}</span>
                        <div class="card__actions">
                            {% if is_own_profile and user.is_superuser or user.is_staff or user.groups.all.0.name == 'Çalışma Yöneticileri' %}
                            <a href="{% url 'calismalar:calisma_duzenle' calisma.slug %}" class="button button--secondary button--sm">Düzenle</a>
                            {% endif %}
                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="button button--primary button--sm">Detaylar</a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="card">
                    <div class="card__body text-center p-5">
                        <i class="bi bi-info-circle display-4 text-muted mb-3"></i>
                        <h4 class="card__title">Henüz Çalışma Eklenmemiş</h4>
                        {% if is_own_profile %}
                        <p class="card__text">"Yeni Çalışma Ekle" butonuna tıklayarak ilk eserinizi paylaşabilirsiniz.</p>
                        {% else %}
                        <p class="card__text">Bu kullanıcının henüz paylaşılmış bir çalışması bulunmuyor.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
