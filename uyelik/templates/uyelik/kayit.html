{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">Kayıt Ol</h1>
            <p class="modern-subtitle">Hesabınızı oluşturun ve Küp Cadısı'na katılın</p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-person-plus me-2"></i>Yeni <PERSON>ştur</h3>
                    </div>
                    <div class="modern-card-body">

                        <form method="post" class="auth-form">
                            {% csrf_token %}

                            <div class="form__group">
                                <label for="username" class="form__label">Kullanıcı Adı</label>
                                <div class="form__input-group">
                                    <span class="form__input-group-text"><i class="bi bi-person"></i></span>
                                    <input type="text" class="form__input" id="username" name="username" required>
                                </div>
                                {% if form.username.errors %}
                                    <div class="invalid-feedback d-block">{{ form.username.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form__group">
                                <label for="email" class="form__label">E-posta</label>
                                <div class="form__input-group">
                                    <span class="form__input-group-text"><i class="bi bi-envelope"></i></span>
                                    <input type="email" class="form__input" id="email" name="email" required>
                                </div>
                                {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">{{ form.email.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form__group">
                                <label for="password1" class="form__label">Şifre</label>
                                <div class="form__input-group">
                                    <span class="form__input-group-text"><i class="bi bi-lock"></i></span>
                                    <input type="password" class="form__input" id="password1" name="password1" required>
                                </div>
                                {% if form.password1.errors %}
                                    <div class="invalid-feedback d-block">{{ form.password1.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form__group">
                                <label for="password2" class="form__label">Şifre Tekrar</label>
                                <div class="form__input-group">
                                    <span class="form__input-group-text"><i class="bi bi-lock"></i></span>
                                    <input type="password" class="form__input" id="password2" name="password2" required>
                                </div>
                                {% if form.password2.errors %}
                                    <div class="invalid-feedback d-block">{{ form.password2.errors.0 }}</div>
                                {% endif %}
                            </div>

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger mb-4">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ form.non_field_errors.0 }}
                                </div>
                            {% endif %}

                            <div class="d-grid mt-4">
                                <button type="submit" class="button button--primary">
                                    <i class="bi bi-person-plus me-2"></i>Kayıt Ol
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">Zaten hesabınız var mı? <a href="{% url 'uyelik:giris' %}" class="modern-link">Giriş Yap</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}