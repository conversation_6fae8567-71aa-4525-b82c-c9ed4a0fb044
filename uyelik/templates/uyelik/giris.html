{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title"><PERSON><PERSON>ş <PERSON></h1>
            <p class="modern-subtitle">Küp Cadısı'na hoş geldiniz</p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-box-arrow-in-right me-2"></i>Kullanıcı Girişi</h3>
                    </div>
                    <div class="modern-card-body">

                        <form method="post" class="auth-form">
                            {% csrf_token %}

                            <div class="form__group">
                                <label for="username" class="form__label">Kullanıcı Adı</label>
                                <div class="form__input-group">
                                    <span class="form__input-group-text"><i class="bi bi-person"></i></span>
                                    <input type="text" class="form__input" id="username" name="username" required>
                                </div>
                                {% if form.username.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="form__group">
                                <label for="password" class="form__label">Şifre</label>
                                <div class="form__input-group">
                                    <span class="form__input-group-text"><i class="bi bi-lock"></i></span>
                                    <input type="password" class="form__input" id="password" name="password" required>
                                </div>
                                {% if form.password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.password.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger mb-4">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ form.non_field_errors.0 }}
                                </div>
                            {% endif %}

                            <div class="d-grid mt-4">
                                <button type="submit" class="button button--primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">Hesabınız yok mu? <a href="{% url 'uyelik:kayit' %}" class="modern-link">Kayıt Ol</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}