# Generated by Django 4.2 on 2025-06-09 19:58

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('uyelik', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='profil',
            name='telefon',
            field=models.CharField(blank=True, max_length=15, validators=[django.core.validators.RegexValidator(message='Telefon numarası geçerli formatta olmalıdır. Örnek: +905551234567', regex='^\\+?1?\\d{9,15}$')]),
        ),
    ]
