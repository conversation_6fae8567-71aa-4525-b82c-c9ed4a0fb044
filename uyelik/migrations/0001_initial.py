# Generated by Django 5.2 on 2025-06-08 01:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Profil',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pics/')),
                ('ad', models.CharField(blank=True, max_length=50)),
                ('soyad', models.CharField(blank=True, max_length=50)),
                ('telefon', models.CharField(blank=True, max_length=15)),
                ('adres', models.TextField(blank=True)),
                ('aciklama', models.TextField(blank=True)),
                ('sehir', models.Char<PERSON><PERSON>(blank=True, max_length=50)),
                ('ilce', models.Char<PERSON>ield(blank=True, max_length=50)),
                ('ulke', models.CharField(blank=True, max_length=50)),
                ('dogum_tarihi', models.DateField(blank=True, null=True)),
                ('cinsiyet', models.CharField(blank=True, choices=[('Erkek', 'Erkek'), ('Kadın', 'Kadın')], max_length=10)),
                ('instagram', models.URLField(blank=True, verbose_name='Instagram')),
                ('facebook', models.URLField(blank=True, verbose_name='Facebook')),
                ('twitter', models.URLField(blank=True, verbose_name='Twitter')),
                ('linkedin', models.URLField(blank=True, verbose_name='LinkedIn')),
                ('youtube', models.URLField(blank=True, verbose_name='YouTube')),
                ('email_public', models.BooleanField(default=False, verbose_name='E-posta adresim görünsün')),
                ('phone_public', models.BooleanField(default=False, verbose_name='Telefon numaram görünsün')),
                ('is_approved', models.BooleanField(default=False, verbose_name='Yönetici Onayı')),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
