from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.models import User
from .models import Profil

class KayitForm(UserCreationForm):
    email = forms.EmailField(required=True)

    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2')
        labels = {
            'username': 'Kullanıcı Adı',
            'email': 'E-posta Adresi',
            'password1': 'Şif<PERSON>',
            'password2': '<PERSON><PERSON><PERSON> (Tekrar)',
        }
        help_texts = {
            'username': '150 karakter veya daha az. <PERSON><PERSON><PERSON> ha<PERSON>, rakamlar ve @/./+/-/_ karakterleri.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'form__input'})
            if 'password' not in field_name and field.label:
                field.widget.attrs.update({'placeholder': field.label})

        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = 'form__input--invalid'
                    if invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        if commit:
            user.save()
        return user

class GirisForm(AuthenticationForm):
    """Kullanıcı giriş formu."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form__input',
            'placeholder': 'Kullanıcı Adı veya E-posta'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'form__input',
            'placeholder': 'Şifre'
        })

        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = 'form__input--invalid'
                    if invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

class ProfilForm(forms.ModelForm):
    class Meta:
        model = Profil
        fields = ('ad', 'soyad', 'telefon', 'adres', 'aciklama', 'sehir', 'ilce', 'ulke',
                  'dogum_tarihi', 'cinsiyet', 'email_public', 'phone_public')
        labels = {
            'ad': 'Ad',
            'soyad': 'Soyad',
            'telefon': 'Telefon',
            'adres': 'Adres',
            'aciklama': 'Hakkımda',
            'sehir': 'Şehir',
            'ilce': 'İlçe',
            'ulke': 'Ülke',
            'dogum_tarihi': 'Doğum Tarihi',
            'cinsiyet': 'Cinsiyet',
            'email_public': 'E-posta adresim profilde görünsün',
            'phone_public': 'Telefon numaram profilde görünsün'
        }
        widgets = {
            'adres': forms.Textarea(attrs={'rows': 3}),
            'aciklama': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Kendiniz hakkında kısa bir bilgi...'}),
            'dogum_tarihi': forms.DateInput(attrs={'type': 'date'}),
            'cinsiyet': forms.Select(),
            'email_public': forms.CheckboxInput(),
            'phone_public': forms.CheckboxInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            # Add placeholders based on labels, but don't override existing ones
            if not isinstance(field.widget, forms.CheckboxInput) and field.label and not field.widget.attrs.get('placeholder'):
                 field.widget.attrs.update({'placeholder': field.label})

            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs.update({'class': 'form__check-input'})
            elif isinstance(field.widget, forms.Textarea):
                field.widget.attrs.update({'class': 'form__textarea'})
            elif isinstance(field.widget, forms.Select):
                field.widget.attrs.update({'class': 'form__select'})
            else:
                field.widget.attrs.update({'class': 'form__input'})

        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = ''
                    if isinstance(widget, forms.Textarea):
                        invalid_class = 'form__textarea--invalid'
                    elif isinstance(widget, forms.Select):
                        invalid_class = 'form__select--invalid'
                    elif isinstance(widget, forms.CheckboxInput):
                        invalid_class = 'form__check-input--invalid'
                    else:
                        invalid_class = 'form__input--invalid'

                    if invalid_class and invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()
