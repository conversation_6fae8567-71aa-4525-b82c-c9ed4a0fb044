from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator

class Profil(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True)  # Geçici olarak geri alındı
    profile_picture = models.ImageField(upload_to='profile_pics/', null=True, blank=True)
    ad = models.CharField(max_length=50, blank=True)
    soyad = models.CharField(max_length=50, blank=True)
    telefon = models.CharField(
        max_length=15,
        blank=True,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Telefon numarası geçerli formatta olmalıdır. Örnek: +905551234567"
        )]
    )
    adres = models.TextField(blank=True)
    aciklama = models.TextField(blank=True)
    sehir = models.CharField(max_length=50, blank=True)
    ilce = models.CharField(max_length=50, blank=True)
    ulke = models.CharField(max_length=50, blank=True)
    dogum_tarihi = models.DateField(null=True, blank=True)
    cinsiyet = models.CharField(max_length=10, choices=[('Erkek', 'Erkek'), ('Kadın', 'Kadın')], blank=True)
    
    # Sosyal Medya Hesapları artık anasayfa.SosyalMedya modeli ile yönetiliyor
    # Bu alanlar kaldırıldı - migration ile temizlenecek
    
    # İletişim Tercihleri
    email_public = models.BooleanField(default=False, verbose_name='E-posta adresim görünsün')
    phone_public = models.BooleanField(default=False, verbose_name='Telefon numaram görünsün')
    
    # Onay Durumu
    is_approved = models.BooleanField(default=False, verbose_name='Yönetici Onayı')

    def clean(self):
        """Model validasyonu"""
        if not self.user:
            raise ValidationError('Profil bir kullanıcıya bağlı olmalıdır')

        # E-posta doğrulaması
        if self.user and self.user.email:
            from django.core.validators import validate_email
            try:
                validate_email(self.user.email)
            except ValidationError:
                raise ValidationError('Geçerli bir e-posta adresi giriniz')

    def __str__(self):
        return f"{self.user.username if self.user else 'Anonim'} Profili"

    def get_sosyal_medya_hesaplari(self):
        """
        Bu kullanıcının sosyal medya hesaplarını döndürür.
        anasayfa.SosyalMedya modelinden alır.
        """
        if not self.user:
            return []

        # Circular import'u önlemek için lazy import
        from anasayfa.models import SosyalMedya
        return SosyalMedya.objects.filter(user=self.user, aktif=True)

    def get_sosyal_medya_url(self, platform):
        """
        Belirli bir platform için sosyal medya URL'sini döndürür.

        Args:
            platform (str): Platform adı (instagram, facebook, vb.)

        Returns:
            str: URL veya None
        """
        if not self.user:
            return None

        # Circular import'u önlemek için lazy import
        from anasayfa.models import SosyalMedya

        try:
            sosyal_medya = SosyalMedya.objects.get(
                user=self.user,
                platform=platform,
                aktif=True
            )
            return sosyal_medya.url
        except SosyalMedya.DoesNotExist:
            return None

    # Geriye uyumluluk için property'ler
    @property
    def instagram(self):
        """Geriye uyumluluk için Instagram URL'si"""
        return self.get_sosyal_medya_url('instagram')

    @property
    def facebook(self):
        """Geriye uyumluluk için Facebook URL'si"""
        return self.get_sosyal_medya_url('facebook')

    @property
    def twitter(self):
        """Geriye uyumluluk için Twitter URL'si"""
        return self.get_sosyal_medya_url('twitter')

    @property
    def linkedin(self):
        """Geriye uyumluluk için LinkedIn URL'si"""
        return self.get_sosyal_medya_url('linkedin')

    @property
    def youtube(self):
        """Geriye uyumluluk için YouTube URL'si"""
        return self.get_sosyal_medya_url('youtube')

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        Profil.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    # Sadece profil yoksa oluştur, gereksiz save işlemlerini önle
    if hasattr(instance, 'profil'):
        # Profil zaten var, gereksiz save yapma
        pass
    else:
        # Profil yoksa oluştur (normalde create_user_profile signal'ı halleder)
        Profil.objects.get_or_create(user=instance)
