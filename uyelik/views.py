from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from .forms import KayitForm, ProfilForm
from .models import Profil
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.models import User
import json
import logging
from datetime import datetime
from utils.validators import comprehensive_file_validation

# Logger'ı tanımla
logger = logging.getLogger(__name__)

# Create your views here.

def kayit(request):
    if request.method == 'POST':
        form = KayitForm(request.POST)
        if form.is_valid():
            try:
                user = form.save()
                login(request, user)
                logger.info(f'Yeni kullanıcı kaydı: {user.username} ({user.email})')
                messages.success(request, 'Kayıt iş<PERSON>i başarılı! Profil bilgilerinizi düzenleyebilirsiniz.')
                return redirect('uyelik:profil')
            except Exception as e:
                logger.error(f'Kullanıcı kayıt hatası: {str(e)}')
                messages.error(request, 'Kayıt sırasında bir hata oluştu. Lütfen tekrar deneyin.')
        else:
            logger.warning(f'Geçersiz kayıt formu: {form.errors}')
            messages.error(request, 'Lütfen formu doğru şekilde doldurunuz.')
    else:
        form = KayitForm()
    return render(request, 'uyelik/kayit.html', {'form': form})

def giris(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                logger.info(f'Kullanıcı girişi: {username}')
                messages.success(request, 'Giriş başarılı!')
                return redirect('anasayfa:ana_sayfa')
            else:
                logger.warning(f'Başarısız giriş denemesi: {username}')
                messages.error(request, 'Kullanıcı adı veya şifre hatalı.')
        else:
            logger.warning(f'Geçersiz giriş formu: {form.errors}')
            messages.error(request, 'Lütfen formu doğru şekilde doldurunuz.')
    else:
        form = AuthenticationForm()
    return render(request, 'uyelik/giris.html', {'form': form})

@login_required
def profil_duzenle(request):
    profil = request.user.profil

    # Yönetici kullanıcıları için onay kontrolünü atla
    if not profil.is_approved and not request.user.is_staff and not request.user.is_superuser:
        messages.warning(request, 'Profilinizi güncelleyebilmek için hesabınızın yönetici tarafından onaylanması gerekmektedir.')
        return redirect('uyelik:profil')

    # URL'den dönüş sekmesini al
    return_tab = request.GET.get('return_tab', 'profile')

    if request.method == 'POST':
        form = ProfilForm(request.POST, instance=profil)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profil bilgileriniz başarıyla güncellendi!')
            return redirect(f'/uyelik/profil/?tab={return_tab}')
    else:
        form = ProfilForm(instance=profil)

    return render(request, 'uyelik/profil_duzenle.html', {
        'form': form,
        'return_tab': return_tab
    })

@login_required
def profil(request):
    """Kullanıcının kendi profilini görüntülemesi için view"""
    profil = request.user.profil

    return render(request, 'uyelik/profil.html', {
        'profil': profil,
        'is_own_profile': True
    })

@login_required
def profil_calismalarim(request):
    """Kullanıcının kendi çalışmalarını görüntülemesi için view"""
    profil = request.user.profil

    # Kullanıcının çalışmalarını çek
    from calismalar.models import Calisma

    # Kullanıcının çalışmalarını çek
    calismalar = Calisma.objects.filter(user=request.user).select_related('user', 'kategori').prefetch_related('tags', 'fotograflar').order_by('-olusturma_tarihi')
    calisma_sayisi = calismalar.count()

    # AJAX isteği olup olmadığını kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # Eğer bu bir doğrudan URL isteği ise ve AJAX değilse, profil sayfasına yönlendir
    if not is_ajax and request.path == '/uyelik/profil/calismalarim/':
        return redirect('/uyelik/profil/#works')

    return render(request, 'uyelik/profil_calismalarim.html', {
        'profil': profil,
        'is_own_profile': True,
        'calismalar': calismalar,
        'calisma_sayisi': calisma_sayisi,
        'request': request,
        'is_ajax': is_ajax
    })

@login_required
def profil_hakkimda(request):
    """Kullanıcının kendi hakkında bilgilerini görüntülemesi için view"""
    profil = request.user.profil

    # AJAX isteği olup olmadığını kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # Eğer bu bir doğrudan URL isteği ise ve AJAX değilse, profil sayfasına yönlendir
    if not is_ajax and request.path == '/uyelik/profil/hakkimda/':
        return redirect('/uyelik/profil/#about')

    return render(request, 'uyelik/profil_hakkimda.html', {
        'profil': profil,
        'is_own_profile': True,
        'request': request,
        'is_ajax': is_ajax
    })

@login_required
def profil_arkadaslar(request):
    """Kullanıcının kendi arkadaşlarını görüntülemesi için view"""
    profil = request.user.profil

    # AJAX isteği olup olmadığını kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # Eğer bu bir doğrudan URL isteği ise ve AJAX değilse, profil sayfasına yönlendir
    if not is_ajax and request.path == '/uyelik/profil/arkadaslar/':
        return redirect('/uyelik/profil/#friends')

    return render(request, 'uyelik/profil_arkadaslar.html', {
        'profil': profil,
        'is_own_profile': True,
        'request': request,
        'is_ajax': is_ajax
    })

def kullanici_profil(request, username):
    """Başka bir kullanıcının profilini görüntülemek için view"""
    # Giriş yapmamış kullanıcıları giriş sayfasına yönlendir
    if not request.user.is_authenticated:
        messages.warning(request, 'Kullanıcı profillerini görüntülemek için giriş yapmalısınız.')
        return redirect('uyelik:giris')

    user = get_object_or_404(User.objects.select_related('profil'), username=username)
    profil = user.profil

    # Kendi profilini görüntülüyorsa, profil sayfasına yönlendir
    if request.user.is_authenticated and request.user.username == username:
        return redirect('uyelik:profil')

    return render(request, 'uyelik/profil.html', {
        'profil': profil,
        'is_own_profile': False,
        'viewed_user': user
    })

def kullanici_calismalar(request, username):
    """Başka bir kullanıcının çalışmalarını görüntülemek için view"""
    # Giriş yapmamış kullanıcıları giriş sayfasına yönlendir
    if not request.user.is_authenticated:
        messages.warning(request, 'Kullanıcı profillerini görüntülemek için giriş yapmalısınız.')
        return redirect('uyelik:giris')

    user = get_object_or_404(User.objects.select_related('profil'), username=username)
    profil = user.profil

    # Kendi profilini görüntülüyorsa, profil sayfasına yönlendir
    if request.user.is_authenticated and request.user.username == username:
        return redirect('/uyelik/profil/#works')

    # Kullanıcının çalışmalarını çek
    from calismalar.models import Calisma

    # Kullanıcının çalışmalarını çek
    calismalar = Calisma.objects.filter(user=user).select_related('user', 'kategori').prefetch_related('tags', 'fotograflar').order_by('-olusturma_tarihi')
    calisma_sayisi = calismalar.count()

    # AJAX isteği olup olmadığını kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # Eğer bu bir doğrudan URL isteği ise ve AJAX değilse, profil sayfasına yönlendir
    if not is_ajax and request.path == f'/uyelik/kullanici/{username}/calismalar/':
        return redirect(f'/uyelik/kullanici/{username}/#works')

    return render(request, 'uyelik/profil_calismalarim.html', {
        'profil': profil,
        'is_own_profile': False,
        'viewed_user': user,
        'calismalar': calismalar,
        'calisma_sayisi': calisma_sayisi,
        'request': request,
        'is_ajax': is_ajax
    })

def kullanici_hakkinda(request, username):
    """Başka bir kullanıcının hakkında bilgilerini görüntülemek için view"""
    # Giriş yapmamış kullanıcıları giriş sayfasına yönlendir
    if not request.user.is_authenticated:
        messages.warning(request, 'Kullanıcı profillerini görüntülemek için giriş yapmalısınız.')
        return redirect('uyelik:giris')

    user = get_object_or_404(User.objects.select_related('profil'), username=username)
    profil = user.profil

    # Kendi profilini görüntülüyorsa, profil sayfasına yönlendir
    if request.user.is_authenticated and request.user.username == username:
        return redirect('/uyelik/profil/#about')

    # AJAX isteği olup olmadığını kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # Eğer bu bir doğrudan URL isteği ise ve AJAX değilse, profil sayfasına yönlendir
    if not is_ajax and request.path == f'/uyelik/kullanici/{username}/hakkinda/':
        return redirect(f'/uyelik/kullanici/{username}/#about')

    return render(request, 'uyelik/profil_hakkimda.html', {
        'profil': profil,
        'is_own_profile': False,
        'viewed_user': user,
        'request': request,
        'is_ajax': is_ajax
    })

def kullanici_arkadaslar(request, username):
    """Başka bir kullanıcının arkadaşlarını görüntülemek için view"""
    # Giriş yapmamış kullanıcıları giriş sayfasına yönlendir
    if not request.user.is_authenticated:
        messages.warning(request, 'Kullanıcı profillerini görüntülemek için giriş yapmalısınız.')
        return redirect('uyelik:giris')

    user = get_object_or_404(User.objects.select_related('profil'), username=username)
    profil = user.profil

    # Kendi profilini görüntülüyorsa, profil sayfasına yönlendir
    if request.user.is_authenticated and request.user.username == username:
        return redirect('/uyelik/profil/#friends')

    # AJAX isteği olup olmadığını kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # Eğer bu bir doğrudan URL isteği ise ve AJAX değilse, profil sayfasına yönlendir
    if not is_ajax and request.path == f'/uyelik/kullanici/{username}/arkadaslar/':
        return redirect(f'/uyelik/kullanici/{username}/#friends')

    return render(request, 'uyelik/profil_arkadaslar.html', {
        'profil': profil,
        'is_own_profile': False,
        'viewed_user': user,
        'request': request,
        'is_ajax': is_ajax
    })

@login_required
def profil_foto_guncelle(request):
    if request.method == 'POST' and request.FILES.get('profile_picture'):
        uploaded_file = request.FILES['profile_picture']

        try:
            # Kapsamlı dosya doğrulaması
            comprehensive_file_validation(uploaded_file)

            # Dosyayı kaydet
            profil = request.user.profil
            profil.profile_picture = uploaded_file
            profil.save()

            logger.info(f'Profil fotoğrafı güncellendi: {request.user.username}')
            return JsonResponse({'success': True, 'message': 'Profil fotoğrafı başarıyla güncellendi'})

        except Exception as e:
            logger.error(f'Profil fotoğrafı yükleme hatası ({request.user.username}): {str(e)}')
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Geçersiz istek'})

@login_required
def profil_alan_guncelle(request):
    if request.method == 'POST':
        profil = request.user.profil
        # Yönetici kullanıcıları için onay kontrolünü atla
        if not profil.is_approved and not request.user.is_staff and not request.user.is_superuser:
            return JsonResponse({'success': False, 'error': 'Profilinizi güncelleyebilmek için hesabınızın yönetici tarafından onaylanması gerekmektedir.'})
        data = json.loads(request.body)
        field_name = data.get('field')
        field_value = data.get('value')

        # Profil alanları için eşleştirme
        field_mapping = {
            'ad': 'ad',
            'soyad': 'soyad',
            'telefon': 'telefon',
            'adres': 'adres',
            'aciklama': 'aciklama',
            'sehir': 'sehir',
            'ilce': 'ilce',
            'ulke': 'ulke',
            'dogum_tarihi': 'dogum_tarihi',
            'cinsiyet': 'cinsiyet',
            # Sosyal medya alanları artık anasayfa.SosyalMedya modeli ile yönetiliyor
            'email_public': 'email_public',
            'phone_public': 'phone_public'
        }

        if field_name and field_name in field_mapping:
            try:
                setattr(profil, field_mapping[field_name], field_value)
                profil.save()
                return JsonResponse({'success': True})
            except json.JSONDecodeError:
                return JsonResponse({'success': False, 'error': 'Geçersiz JSON verisi'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})
        return JsonResponse({'success': False, 'error': 'Geçersiz istek'})

@login_required
def profil_coklu_alan_guncelle(request):
    """Profil bilgilerini toplu olarak güncelleme view fonksiyonu"""
    if request.method == 'POST':
        try:
            profil = request.user.profil
            # Yönetici kullanıcıları için onay kontrolünü atla
            if not profil.is_approved and not request.user.is_staff and not request.user.is_superuser:
                return JsonResponse({'success': False, 'error': 'Profilinizi güncelleyebilmek için hesabınızın yönetici tarafından onaylanması gerekmektedir.'})

            data = json.loads(request.body)

            # Güncellenecek alanlar
            fields_to_update = {
                'ad': data.get('ad', ''),
                'soyad': data.get('soyad', ''),
                'telefon': data.get('telefon', ''),
                'adres': data.get('adres', ''),
                'aciklama': data.get('aciklama', ''),
                'sehir': data.get('sehir', ''),
                'ilce': data.get('ilce', ''),
                'ulke': data.get('ulke', ''),
                'email_public': data.get('email_public', False),
                'phone_public': data.get('phone_public', False),
                'adres_public': data.get('adres_public', False)
            }

            # Doğum tarihi için özel işlem
            dogum_tarihi = data.get('dogum_tarihi')
            if dogum_tarihi:
                try:
                    fields_to_update['dogum_tarihi'] = datetime.strptime(dogum_tarihi, '%Y-%m-%d').date()
                except ValueError:
                    pass  # Geçersiz tarih formatı, güncelleme yapmadan devam et
            else:
                fields_to_update['dogum_tarihi'] = None

            # Cinsiyet için özel işlem
            cinsiyet = data.get('cinsiyet')
            if cinsiyet in ['Erkek', 'Kadın']:
                fields_to_update['cinsiyet'] = cinsiyet
            else:
                fields_to_update['cinsiyet'] = ''

            # Tüm alanları güncelle
            for field, value in fields_to_update.items():
                setattr(profil, field, value)

            # Değişiklikleri kaydet
            profil.save()

            return JsonResponse({'success': True})
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Geçersiz JSON verisi'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Geçersiz istek'})

def kullanici_onay_gerekli(view_func):
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.profil.durum == 'onaylandi':
                return view_func(request, *args, **kwargs)
            else:
                messages.warning(request, 'Bu işlemi yapabilmek için hesabınızın onaylanması gerekiyor.')
                return redirect('anasayfa:index')
        return redirect('login')
    return wrapper

def cikis(request):
    logout(request)
    messages.success(request, 'Başarıyla çıkış yaptınız.')
    return redirect('anasayfa:ana_sayfa')

# Yönlendirme fonksiyonları
def profil_redirect(request, tab=None):
    """Eski profil URL'lerini yeni yapıya yönlendirir"""
    if tab:
        return redirect(f'/uyelik/profil/#{tab}')
    return redirect('uyelik:profil')

def kullanici_redirect(request, username, tab=None):
    """Eski kullanıcı profil URL'lerini yeni yapıya yönlendirir"""
    if tab:
        return redirect(f'/uyelik/kullanici/{username}/#{tab}')
    return redirect('uyelik:kullanici_profil', username=username)





