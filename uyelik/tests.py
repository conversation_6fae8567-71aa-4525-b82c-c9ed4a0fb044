from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from .models import Profil
from PIL import Image
import io

class ProfilModelTest(TestCase):
    """Profil modeli için test sınıfı"""

    def setUp(self):
        """Test verilerini hazırla"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_profil_creation(self):
        """Profil oluşturma testi"""
        profil = Profil.objects.create(user=self.user)
        self.assertEqual(profil.user, self.user)
        self.assertIsNotNone(profil.id)

    def test_profil_str_method(self):
        """Profil __str__ metodu testi"""
        profil = Profil.objects.create(
            user=self.user,
            ad='Test',
            soyad='User'
        )
        expected_str = f"{self.user.username} - Test User"
        # Model'de __str__ metodu varsa test et

    def test_profil_without_user_should_fail(self):
        """Kullanıcısız profil oluşturma başarısız olmalı"""
        with self.assertRaises(Exception):
            Profil.objects.create(user=None)

class ProfilViewTest(TestCase):
    """Profil view'ları için test sınıfı"""

    def setUp(self):
        """Test verilerini hazırla"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.profil = Profil.objects.create(user=self.user)

    def test_profil_view_requires_login(self):
        """Profil sayfası giriş gerektirmeli"""
        response = self.client.get(reverse('uyelik:profil'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_profil_view_with_login(self):
        """Giriş yapılmış kullanıcı profil sayfasını görebilmeli"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('uyelik:profil'))
        self.assertEqual(response.status_code, 200)

    def test_profil_update(self):
        """Profil güncelleme testi"""
        self.client.login(username='testuser', password='testpass123')

        data = {
            'ad': 'Updated Name',
            'soyad': 'Updated Surname',
            'telefon': '1234567890'
        }

        response = self.client.post(reverse('uyelik:profil_guncelle'), data)

        # Profili yeniden yükle
        self.profil.refresh_from_db()
        self.assertEqual(self.profil.ad, 'Updated Name')

class FileUploadTest(TestCase):
    """Dosya yükleme testleri"""

    def setUp(self):
        """Test verilerini hazırla"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.profil = Profil.objects.create(user=self.user)

    def create_test_image(self):
        """Test için geçici resim oluştur"""
        file = io.BytesIO()
        image = Image.new('RGB', (100, 100), color='red')
        image.save(file, 'JPEG')
        file.seek(0)
        return SimpleUploadedFile(
            'test.jpg',
            file.getvalue(),
            content_type='image/jpeg'
        )

    def test_valid_image_upload(self):
        """Geçerli resim yükleme testi"""
        self.client.login(username='testuser', password='testpass123')

        test_image = self.create_test_image()

        response = self.client.post(
            reverse('uyelik:profil_resmi_yukle'),
            {'profile_picture': test_image}
        )

        # Başarılı yükleme kontrolü
        self.assertEqual(response.status_code, 200)

    def test_invalid_file_upload(self):
        """Geçersiz dosya yükleme testi"""
        self.client.login(username='testuser', password='testpass123')

        # Geçersiz dosya (text dosyası)
        invalid_file = SimpleUploadedFile(
            'test.txt',
            b'This is not an image',
            content_type='text/plain'
        )

        response = self.client.post(
            reverse('uyelik:profil_resmi_yukle'),
            {'profile_picture': invalid_file}
        )

        # Hata dönmeli
        self.assertNotEqual(response.status_code, 200)
