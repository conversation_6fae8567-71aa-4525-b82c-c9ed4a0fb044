import os
import logging
from pathlib import Path
import environ
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Environment variables
env = environ.Env(
    DEBUG=(bool, False),
    SECRET_KEY=(str, ''),
    ALLOWED_HOSTS=(list, ['localhost', '127.0.0.1']),
)

# .env dosyasını oku
environ.Env.read_env(BASE_DIR / '.env')

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')
if not SECRET_KEY:
    raise ValueError("SECRET_KEY environment variable is required!")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG')

# ALLOWED_HOSTS should be configured properly for production
ALLOWED_HOSTS = env('ALLOWED_HOSTS')

# Application definition
INSTALLED_APPS = [
    'jazzmin',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'widget_tweaks',  # Form widget'ları için özel filtreler sağlar
    'anasayfa',
    'urunler',
    'nasilyapilir',
    'uyelik',
    'yorumlar',
    'calismalar',
    'utils.apps.UtilsConfig',  # Yardımcı fonksiyonlar ve management commands
    'taggit',  # Etiketleme sistemi için
    'rest_framework', # Django REST Framework
]

# Add static template context processor
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.static',
                'anasayfa.context_processors.sosyal_medya',
                'urunler.context_processors.product_permissions',
                'utils.context_processors.user_permissions',
            ],
        },
    },
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'kupcadisi.urls'

WSGI_APPLICATION = 'kupcadisi.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_NAME'),
        'USER': os.environ.get('POSTGRES_USER'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD'),
        'HOST': os.environ.get('POSTGRES_HOST'),
        'PORT': os.environ.get('POSTGRES_PORT'),
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'tr'
TIME_ZONE = 'Europe/Istanbul'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = 'static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Media files (Product Images)
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Ürün resimleri için özel klasör yapısı
PRODUCT_IMAGES_DIR = 'products'

# Dosya yükleme sınırlamaları ve güvenlik ayarları
# 10MB dosya boyutu sınırı
MAX_UPLOAD_SIZE = 10 * 1024 * 1024  # 10MB in bytes
CONTENT_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']

# Güvenli dosya yükleme ayarları
SECURE_CONTENT_TYPE_NOSNIFF = True  # X-Content-Type-Options: nosniff header'ı ekler
SECURE_UPLOAD_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
SECURE_UPLOAD_CONTENT_TYPES = CONTENT_TYPES

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Jazzmin settings - Kullanıcı Dostu Admin Panel
JAZZMIN_SETTINGS = {
    "site_title": "Küp Cadısı Admin",
    "site_header": "Küp Cadısı",
    "site_brand": "Küp Cadısı",
    "site_logo": "images/logo.png",
    "login_logo": None,
    "login_logo_dark": None,
    "site_logo_classes": "img-circle",
    "site_icon": "fas fa-palette",
    "welcome_sign": "Hoş Geldiniz",
    "copyright": "Küp Cadısı 2025",
    "search_model": [],
    "user_avatar": None,

    # Üst menü linkleri - Sadeleştirildi
    "topmenu_links": [
        {"name": "Siteye Dön", "url": "/", "new_window": True},
    ],

    # Kullanıcı menüsü - Sadeleştirildi
    "usermenu_links": [
        {"name": "Profilim", "url": "admin:auth_user_change"},
        {"name": "Şifre Değiştir", "url": "admin:password_change"},
        {"name": "Siteye Dön", "url": "/", "new_window": True},
    ],

    # Sidebar ayarları
    "show_sidebar": True,
    "navigation_expanded": False,
    "hide_apps": [],
    "hide_models": [],
    "order_with_respect_to": ["auth", "anasayfa", "urunler", "calismalar", "nasilyapilir", "atolye", "yorumlar", "uyelik"],

    # Özel linkler
    "custom_links": {
        "urunler": [{
            "name": "📦 Yeni Ürün Ekle",
            "url": "admin:urunler_product_add",
            "icon": "fas fa-plus",
            "permissions": ["urunler.add_product"]
        }],
        "calismalar": [{
            "name": "🎨 Yeni Çalışma Ekle",
            "url": "admin:calismalar_calisma_add",
            "icon": "fas fa-plus",
            "permissions": ["calismalar.add_calisma"]
        }],
        "nasilyapilir": [{
            "name": "🎬 Yeni Video Ekle",
            "url": "admin:nasilyapilir_video_add",
            "icon": "fas fa-plus",
            "permissions": ["nasilyapilir.add_video"]
        }],
        "atolye": [{
            "name": "🎨 Yeni Atolye İçeriği",
            "url": "admin:atolye_atolyeicerik_add",
            "icon": "fas fa-plus",
            "permissions": ["atolye.add_atolyeicerik"]
        }]
    },

    # İkonlar - Emoji ve FontAwesome karışımı
    "icons": {
        # Ana modüller
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "anasayfa": "fas fa-home",
        "urunler": "fas fa-shopping-bag",
        "calismalar": "fas fa-palette",
        "nasilyapilir": "fas fa-video",
        "atolye": "fas fa-hammer",
        "yorumlar": "fas fa-comments",
        "uyelik": "fas fa-user-circle",
        "taggit": "fas fa-tags",

        # Alt modeller
        "urunler.Product": "fas fa-box",
        "urunler.Category": "fas fa-folder",
        "urunler.ProductImage": "fas fa-image",
        "calismalar.Calisma": "fas fa-paint-brush",
        "calismalar.CalismaImage": "fas fa-images",
        "nasilyapilir.Video": "fas fa-play-circle",
        "atolye.AtolyeIcerik": "fas fa-palette",
        "yorumlar.Yorum": "fas fa-comment",
        "uyelik.Profil": "fas fa-id-card",
        "anasayfa.Hakkimda": "fas fa-info-circle",
        "anasayfa.IletisimBilgisi": "fas fa-address-book",
        "anasayfa.SosyalMedya": "fas fa-share-alt",
        "anasayfa.Egitim": "fas fa-graduation-cap",
        "taggit.Tag": "fas fa-tag",
    },

    # Varsayılan ikonlar
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",

    # UI ayarları
    "related_modal_active": True,
    "custom_css": "admin/css/custom_admin.css",
    "custom_js": None,
    "use_google_fonts_cdn": True,
    "show_ui_builder": False,

    # Form düzeni
    "changeform_format": "horizontal_tabs",
    "changeform_format_overrides": {
        "auth.user": "vertical_tabs",
        "auth.group": "vertical_tabs",
        "urunler.product": "horizontal_tabs",
        "calismalar.calisma": "horizontal_tabs"
    },

    # Dil seçici
    "language_chooser": True,

    # Tema renkleri
    "theme": "default",
    "dark_mode_theme": None,

    # Sidebar ayarları
    "show_sidebar": True,
    "navigation_expanded": True,

    # Liste sayfası ayarları
    "show_full_result_count": True,

    # Arama ayarları
    "search_model": ["auth.User", "auth.Group", "urunler.Product", "calismalar.Calisma", "nasilyapilir.Video", "atolye.AtolyeIcerik"],
}

# Static Files Configuration
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Static files storage with compression for production
if DEBUG:
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
else:
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'

# File compression settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB

# Login/Logout URLs
LOGIN_URL = '/uyelik/giris/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

# Cache Configuration
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 300,  # 5 minutes
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

# Cache middleware settings
CACHE_MIDDLEWARE_ALIAS = 'default'
CACHE_MIDDLEWARE_SECONDS = 60  # 1 minute (daha kısa süre)
CACHE_MIDDLEWARE_KEY_PREFIX = 'kupcadisi'

# Cache'lenmeyen URL'ler (kullanıcı authentication'ı etkileyenler)
CACHE_MIDDLEWARE_ANONYMOUS_ONLY = True  # Sadece anonim kullanıcılar için cache

# CSRF ayarları
CSRF_COOKIE_AGE = 3600  # 1 saat
CSRF_TOKEN_VALID_FOR = 3600  # 1 saat
CSRF_USE_SESSIONS = False  # Cookie kullan, session değil
CSRF_COOKIE_SAMESITE = 'Lax'  # Modern tarayıcılar için

# Session ayarları - veritabanı kullan
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24 saat
SESSION_SAVE_EVERY_REQUEST = True  # Her istekte session'ı kaydet

# Security Settings
if not DEBUG:
    # HTTPS settings for production
    SECURE_SSL_REDIRECT = True
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_HTTPONLY = True
    SESSION_COOKIE_HTTPONLY = True

# Logging Configuration
import os

# Logs dizinini oluştur
LOGS_DIR = BASE_DIR / 'logs'
LOGS_DIR.mkdir(exist_ok=True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {name} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {asctime} {name} {message}',
            'style': '{',
        },
        'json': {
            'format': '{{"level": "{levelname}", "time": "{asctime}", "name": "{name}", "module": "{module}", "message": "{message}", "pathname": "{pathname}", "lineno": {lineno}}}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG' if DEBUG else 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'filters': ['require_debug_true'],
        },
        'file_debug': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(LOGS_DIR / 'debug.log'),
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'filters': ['require_debug_true'],
        },
        'file_info': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(LOGS_DIR / 'info.log'),
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': str(LOGS_DIR / 'error.log'),
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 10,
            'formatter': 'json',
        },
        'mail_admins': {
            'level': 'ERROR',
            'class': 'django.utils.log.AdminEmailHandler',
            'filters': ['require_debug_false'],
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file_info', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['file_error', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['file_error', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
        'kupcadisi': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'anasayfa': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'urunler': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'yorumlar': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'calismalar': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'nasilyapilir': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
        'uyelik': {
            'handlers': ['console', 'file_debug', 'file_info', 'file_error'],
            'level': 'DEBUG' if DEBUG else 'INFO',
            'propagate': False,
        },
    },
}

# Sentry Configuration
SENTRY_DSN = env('SENTRY_DSN', default='')
SENTRY_ENVIRONMENT = env('SENTRY_ENVIRONMENT', default='development')

if SENTRY_DSN and not DEBUG:
    # Sentry logging integration
    sentry_logging = LoggingIntegration(
        level=logging.INFO,        # Capture info and above as breadcrumbs
        event_level=logging.ERROR  # Send errors as events
    )

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(
                transaction_style='url',
                middleware_spans=True,
                signals_spans=True,
                cache_spans=True,
            ),
            sentry_logging,
        ],
        environment=SENTRY_ENVIRONMENT,
        traces_sample_rate=0.1,  # 10% of transactions for performance monitoring
        profiles_sample_rate=0.1,  # 10% for profiling
        send_default_pii=False,  # Don't send personally identifiable information
        attach_stacktrace=True,
        before_send=lambda event, hint: event if not DEBUG else None,
    )