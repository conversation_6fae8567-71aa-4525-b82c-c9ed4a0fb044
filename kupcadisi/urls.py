from django.contrib import admin
from django.urls import path, include, re_path
from django.conf.urls.i18n import i18n_patterns
from django.conf import settings
from django.conf.urls.static import static
from uyelik.views import kullanici_profil
from django.http import JsonResponse
from utils.csrf_utils import get_csrf_token

urlpatterns = [
    # Admin paneli
    path('admin/', admin.site.urls),

    # CSRF token endpoint
    path('utils/csrf-token/', get_csrf_token, name='csrf_token'),

    # Diğer URL'ler
    path('i18n/', include('django.conf.urls.i18n')),
    path('', include('anasayfa.urls', namespace='anasayfa')),
    path('urunler/', include('urunler.urls', namespace='urunler')),
    path('nasil-yapilir/', include('nasilyapilir.urls', namespace='nasilyapilir')),
    path('uyelik/', include('uyelik.urls', namespace='uyelik')),
    path('yorumlar/', include('yorumlar.urls', namespace='yorumlar')),
    path('calismalar/', include('calismalar.urls', namespace='calismalar')),

    # Kullanıcı profili URL'si
    re_path(r'^@(?P<username>[\w.@+-]+)/?$', kullanici_profil, name='kullanici_profil'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
