from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from .models import Calisma, CalismaFotograf
from uyelik.models import Profil
from PIL import Image
import io

class CalismaModelTest(TestCase):
    """Çalışma modeli için test sınıfı"""

    def setUp(self):
        """Test verilerini hazırla"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_calisma_creation(self):
        """Çalışma oluşturma testi"""
        calisma = Calisma.objects.create(
            user=self.user,
            baslik='Test Çalışması',
            aciklama='Test açıklaması',
            begeni_sayisi=0
        )
        self.assertEqual(calisma.user, self.user)
        self.assertEqual(calisma.baslik, 'Test Çalışması')
        self.assertEqual(calisma.begeni_sayisi, 0)

    def test_calisma_str_method(self):
        """Çalışma __str__ metodu testi"""
        calisma = Calisma.objects.create(
            user=self.user,
            baslik='Test Çalışması',
            aciklama='Test açıklaması'
        )
        self.assertEqual(str(calisma), 'Test Çalışması')

class CalismaViewTest(TestCase):
    """Çalışma view'ları için test sınıfı"""

    def setUp(self):
        """Test verilerini hazırla"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.calisma = Calisma.objects.create(
            user=self.user,
            baslik='Test Çalışması',
            aciklama='Test açıklaması',
            slug='test-calismasi'
        )

    def test_calisma_listesi_view(self):
        """Çalışma listesi sayfası testi"""
        response = self.client.get(reverse('calismalar:calisma_listesi'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Çalışması')

    def test_calisma_detay_view(self):
        """Çalışma detay sayfası testi"""
        response = self.client.get(
            reverse('calismalar:calisma_detay', kwargs={'slug': self.calisma.slug})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Çalışması')

    def test_calisma_ekle_requires_login(self):
        """Çalışma ekleme sayfası giriş gerektirmeli"""
        response = self.client.get(reverse('calismalar:calisma_ekle'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_calisma_ekle_with_login(self):
        """Giriş yapılmış kullanıcı çalışma ekleyebilmeli"""
        # Kullanıcıya onaylı profil oluştur
        profil, created = Profil.objects.get_or_create(
            user=self.user,
            defaults={'is_approved': True}
        )
        if not created:
            profil.is_approved = True
            profil.save()

        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('calismalar:calisma_ekle'))
        self.assertEqual(response.status_code, 200)

class CalismaFotografiTest(TestCase):
    """Çalışma fotoğrafı testleri"""

    def setUp(self):
        """Test verilerini hazırla"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.calisma = Calisma.objects.create(
            user=self.user,
            baslik='Test Çalışması',
            aciklama='Test açıklaması'
        )

    def create_test_image(self):
        """Test için geçici resim oluştur"""
        file = io.BytesIO()
        image = Image.new('RGB', (100, 100), color='red')
        image.save(file, 'JPEG')
        file.seek(0)
        return SimpleUploadedFile(
            'test.jpg',
            file.getvalue(),
            content_type='image/jpeg'
        )

    def test_fotograf_upload(self):
        """Fotoğraf yükleme testi"""
        self.client.login(username='testuser', password='testpass123')

        test_image = self.create_test_image()

        # Fotoğraf oluştur
        fotograf = CalismaFotograf.objects.create(
            calisma=self.calisma,
            fotograf=test_image
        )

        self.assertEqual(fotograf.calisma, self.calisma)
        self.assertTrue(fotograf.fotograf.name.endswith('.jpg'))
