from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q

from .models import Calisma, CalismaFotograf

def setup_calisma_permissions():
    """
    Çalışma Yöneticileri grubu için gerekli izinleri oluşturur ve atar.
    """
    # Çalışma Yöneticileri grubunu al veya oluştur
    group, created = Group.objects.get_or_create(name='Çalışma Yöneticileri')
    
    # Çalışma ve CalismaFotograf modelleri için ContentType'ları al
    calisma_content_type = ContentType.objects.get_for_model(Calisma)
    fotograf_content_type = ContentType.objects.get_for_model(CalismaFotograf)
    
    # Çalışma modeli için izinleri al
    calisma_permissions = Permission.objects.filter(
        content_type=calisma_content_type,
        codename__in=['add_calisma', 'change_calisma', 'delete_calisma', 'view_calisma']
    )
    
    # CalismaFotograf modeli için izinleri al
    fotograf_permissions = Permission.objects.filter(
        content_type=fotograf_content_type,
        codename__in=['add_calismafotograf', 'change_calismafotograf', 'delete_calismafotograf', 'view_calismafotograf']
    )
    
    # Tüm izinleri gruba ekle
    for perm in list(calisma_permissions) + list(fotograf_permissions):
        group.permissions.add(perm)
    
    return group

def check_calisma_permission(user, calisma=None):
    """
    Kullanıcının çalışma üzerinde yetkisi olup olmadığını kontrol eder.

    Yeni granüler izin sistemi ile entegre edilmiştir.
    Aşağıdaki durumlarda True döner:
    1. Kullanıcı süper kullanıcı ise
    2. Kullanıcı "İçerik Editörü" grubuna dahilse
    3. Kullanıcı "Çalışma Yöneticisi" grubuna dahilse
    4. Kullanıcı staff (yönetici) ise
    5. Onaylanmış kullanıcı ise (kendi çalışmaları için)

    Args:
        user: Kontrol edilecek kullanıcı
        calisma: İsteğe bağlı, belirli bir çalışma için kontrol yapılacaksa

    Returns:
        bool: Kullanıcının yetkisi varsa True, yoksa False
    """
    # Kullanıcı giriş yapmış mı?
    if not user.is_authenticated:
        return False

    # Süper kullanıcılar her zaman yetkilidir
    if user.is_superuser:
        return True

    # Yöneticiler (staff) her zaman yetkilidir
    if user.is_staff:
        return True

    # Granüler izin sistemi kontrolü
    from utils.permissions import check_permission

    # Çalışma yönetimi izinleri
    has_calisma_permission = (
        check_permission(user, 'calismalar.add_calisma') or
        check_permission(user, 'calismalar.change_calisma') or
        check_permission(user, 'calismalar.delete_calisma')
    )

    # Eğer çalışma belirtilmişse
    if calisma:
        # İzin sistemi yetkisi varsa tam yetki
        if has_calisma_permission:
            return True

        # Onaylanmış kullanıcı kendi çalışmasını düzenleyebilir
        if (hasattr(user, 'profil') and user.profil.is_approved and
            calisma.user == user):
            return True
    else:
        # Yeni çalışma ekleme
        if has_calisma_permission:
            return True

        # Onaylanmış kullanıcı yeni çalışma ekleyebilir
        if hasattr(user, 'profil') and user.profil.is_approved:
            return True

    return False
