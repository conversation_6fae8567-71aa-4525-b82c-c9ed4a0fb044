from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
from django.db.models.signals import pre_save, pre_delete, post_delete, m2m_changed
from django.dispatch import receiver
from django.conf import settings
from taggit.managers import TaggableManager
from urunler.models import Category
import os
import uuid
from utils.logging import get_logger

# Logger'ı başlat
logger = get_logger(__name__)

def calisma_fotograf_yolu(instance, filename):
    """
    Fotoğraf dosyalarını güvenli bir şekilde yüklemek için özel bir yol oluşturur.

    Args:
        instance: CalismaFotograf model instance
        filename: Orijinal dosya adı

    Returns:
        str: Güvenli dosya yolu

    Raises:
        ValueError: Kullanıcı ID'si belirlenemezse
    """
    # Dosya uzantısını al ve normalize et
    ext = os.path.splitext(filename)[1].lower()

    # Uzantı kontrolü ve düzeltme
    allowed_extensions = getattr(settings, 'SECURE_UPLOAD_EXTENSIONS', ['.jpg', '.jpeg', '.png', '.gif', '.webp'])
    if not ext or ext not in allowed_extensions:
        ext = '.jpg'  # Varsayılan güvenli uzantı
        logger.warning(f"Geçersiz dosya uzantısı, varsayılan uzantı kullanılıyor: {filename}", extra={
            'original_filename': filename,
            'original_ext': os.path.splitext(filename)[1],
            'new_ext': ext,
            'operation': 'file_upload_path'
        })

    # Benzersiz dosya adı oluştur
    safe_filename = f"{uuid.uuid4().hex}{ext}"

    try:
        # Kullanıcı ID'sini belirle - artık exception fırlatır
        user_id = _get_user_id_for_upload(instance)

        file_path = os.path.join('calisma_fotograflari', user_id, safe_filename)

        logger.debug(f"Fotoğraf yolu oluşturuldu: {file_path}", extra={
            'user_id': user_id,
            'original_filename': filename,
            'safe_filename': safe_filename,
            'operation': 'file_upload_path'
        })

        return file_path

    except ValueError as e:
        # Kullanıcı ID'si bulunamadı - bu durumda dosya yüklenmemeli
        logger.error(f"Fotoğraf yolu oluşturulamadı: {e}", extra={
            'filename': filename,
            'instance_type': type(instance).__name__,
            'operation': 'file_upload_path'
        })
        # ValueError'ı yeniden fırlat - Django bu durumda upload'ı durdurur
        raise

def _get_user_id_for_upload(instance):
    """
    Upload için kullanıcı ID'sini güvenli bir şekilde belirler.

    Args:
        instance: CalismaFotograf model instance

    Returns:
        str: Kullanıcı ID'si

    Raises:
        ValueError: Kullanıcı ID'si belirlenemezse
    """
    try:
        # Önce instance'ın user alanını kontrol et (signal ile doldurulmuş olabilir)
        if hasattr(instance, 'user') and instance.user:
            logger.debug(f"User ID instance.user'dan alındı: {instance.user.id}")
            return str(instance.user.id)

        # Sonra calisma.user'ı kontrol et
        if (hasattr(instance, 'calisma') and
            instance.calisma and
            hasattr(instance.calisma, 'user') and
            instance.calisma.user):
            logger.debug(f"User ID calisma.user'dan alındı: {instance.calisma.user.id}")
            return str(instance.calisma.user.id)

        # Kullanıcı bulunamadı - bu ciddi bir sorun
        error_msg = "Fotoğraf yükleme için kullanıcı ID'si bulunamadı"
        logger.error(error_msg, extra={
            'instance_type': type(instance).__name__,
            'instance_id': getattr(instance, 'id', 'None'),
            'has_user': hasattr(instance, 'user'),
            'has_calisma': hasattr(instance, 'calisma'),
            'calisma_id': getattr(instance.calisma, 'id', 'None') if hasattr(instance, 'calisma') else 'None',
            'operation': 'get_user_id_for_upload'
        })
        raise ValueError(error_msg)

    except ValueError:
        # ValueError'ı yeniden fırlat
        raise
    except Exception as e:
        error_msg = f"Kullanıcı ID'si belirlenirken beklenmeyen hata: {e}"
        logger.error(error_msg, extra={
            'error': str(e),
            'instance_type': type(instance).__name__,
            'operation': 'get_user_id_for_upload'
        })
        raise ValueError(error_msg)

# Kategori modeli artık urunler.Category kullanıyor



class Calisma(models.Model):
    """Kullanıcıların atölye çalışmalarını temsil eden model."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='calismalar')
    baslik = models.CharField(max_length=200, verbose_name='Başlık')
    aciklama = models.TextField(verbose_name='Açıklama')
    slug = models.SlugField(unique=True, max_length=250)
    kategori = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='calismalar')
    tags = TaggableManager(blank=True, verbose_name="Etiketler", help_text="Çalışmayla ilgili etiketleri virgülle ayırarak girin")
    goruntulenme_sayisi = models.PositiveIntegerField(default=0, verbose_name='Görüntülenme Sayısı')
    begeni_sayisi = models.PositiveIntegerField(default=0, verbose_name='Beğeni Sayısı')
    begenenler = models.ManyToManyField(
        User,
        related_name='begenilen_calismalar',
        blank=True,
        verbose_name='Beğenenler',
        help_text='Bu çalışmayı beğenen kullanıcılar'
    )
    olusturma_tarihi = models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')
    guncelleme_tarihi = models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')

    class Meta:
        verbose_name = 'Çalışma'
        verbose_name_plural = 'Çalışmalar'
        ordering = ['-olusturma_tarihi']
        indexes = [
            models.Index(fields=['-olusturma_tarihi'], name='calisma_olusturma_idx'),
            models.Index(fields=['-begeni_sayisi'], name='calisma_begeni_idx'),
            models.Index(fields=['slug'], name='calisma_slug_idx'),
            models.Index(fields=['user', '-olusturma_tarihi'], name='calisma_user_tarih_idx'),
            models.Index(fields=['kategori', '-olusturma_tarihi'], name='calisma_kategori_tarih_idx'),
        ]

    def __str__(self):
        return self.baslik

    def begeni_sayisi_guncelle(self):
        """
        Beğeni sayısını ManyToManyField'dan günceller.

        Not: Bu metod artık signal tarafından otomatik yapılıyor,
        ancak manuel senkronizasyon gerektiğinde kullanılabilir.
        """
        old_count = self.begeni_sayisi
        new_count = self.begenenler.count()

        if old_count != new_count:
            self.begeni_sayisi = new_count
            self.save(update_fields=['begeni_sayisi'])

            logger.debug(f"Manuel beğeni sayısı güncellendi: {self.baslik}", extra={
                'calisma_id': self.id,
                'old_count': old_count,
                'new_count': new_count,
                'operation': 'manual_begeni_sayisi_guncelle'
            })

        return self.begeni_sayisi

    def kullanici_begendi_mi(self, user):
        """
        Belirtilen kullanıcının bu çalışmayı beğenip beğenmediğini kontrol eder.

        Args:
            user: Kontrol edilecek kullanıcı

        Returns:
            bool: Kullanıcı beğendiyse True, değilse False
        """
        if user.is_authenticated:
            return self.begenenler.filter(id=user.id).exists()
        return False

    def begeni_toggle(self, user):
        """
        Kullanıcının beğeni durumunu değiştirir (toggle).
        Signal otomatik olarak begeni_sayisi alanını güncelleyecek.

        Args:
            user: Beğeni durumu değiştirilecek kullanıcı

        Returns:
            tuple: (begendi_mi: bool, yeni_begeni_sayisi: int)
        """
        if not user.is_authenticated:
            return False, self.begeni_sayisi

        if self.kullanici_begendi_mi(user):
            self.begenenler.remove(user)
            begendi = False
        else:
            self.begenenler.add(user)
            begendi = True

        # Signal otomatik olarak begeni_sayisi'ni güncelleyecek
        # Bu nedenle refresh_from_db() ile güncel değeri alalım
        self.refresh_from_db(fields=['begeni_sayisi'])
        yeni_sayisi = self.begeni_sayisi

        logger.info(f"Beğeni durumu değişti: {self.baslik}", extra={
            'calisma_id': self.id,
            'user_id': user.id,
            'user_username': user.username,
            'begendi': begendi,
            'yeni_begeni_sayisi': yeni_sayisi,
            'operation': 'begeni_toggle'
        })

        return begendi, yeni_sayisi

    def save(self, *args, **kwargs):
        """Slug oluşturma ve kaydetme işlemi."""
        if not self.slug:
            # Slug oluştur
            base_slug = slugify(self.baslik)
            # Benzersiz slug oluştur
            unique_slug = base_slug
            num = 1
            while Calisma.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{num}"
                num += 1
            self.slug = unique_slug
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """
        Çalışma silindiğinde CASCADE ile fotoğraflar da silinir.
        Dosya silme işlemi post_delete signal'da yapılır.
        """
        logger.info(f"Calisma siliniyor: {self.baslik} (ID: {self.id})")
        super().delete(*args, **kwargs)

class CalismaFotograf(models.Model):
    """Çalışmalara ait fotoğrafları temsil eden model."""
    calisma = models.ForeignKey(Calisma, on_delete=models.CASCADE, related_name='fotograflar')
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='Kullanıcı',
        help_text='Fotoğrafı yükleyen kullanıcı (otomatik doldurulur)'
    )
    fotograf = models.ImageField(
        upload_to=calisma_fotograf_yolu,
        verbose_name='Fotoğraf'
    )
    aciklama = models.CharField(max_length=255, blank=True, verbose_name='Açıklama')
    sira = models.PositiveIntegerField(default=0, verbose_name='Sıra')
    olusturma_tarihi = models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')

    class Meta:
        verbose_name = 'Çalışma Fotoğrafı'
        verbose_name_plural = 'Çalışma Fotoğrafları'
        ordering = ['sira', 'olusturma_tarihi']
        indexes = [
            models.Index(fields=['calisma', 'sira']),
            models.Index(fields=['user', '-olusturma_tarihi']),
        ]

    def __str__(self):
        return f"{self.calisma.baslik} - Fotoğraf {self.sira}"

    def clean(self):
        """Model validasyonu."""
        from django.core.exceptions import ValidationError

        # User alanı kontrolü
        if not self.user and not (self.calisma and self.calisma.user):
            raise ValidationError(
                'Fotoğraf için kullanıcı bilgisi gereklidir. '
                'Lütfen önce çalışmayı kaydedin veya kullanıcı bilgisini belirtin.'
            )

        # Fotoğraf dosyası validasyonu
        if self.fotograf:
            # Dosya boyutu kontrolü
            max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 10 * 1024 * 1024)  # 10MB default
            if self.fotograf.size > max_size:
                max_size_mb = max_size / (1024 * 1024)
                raise ValidationError(f'Dosya boyutu {max_size_mb:.1f}MB\'dan büyük olamaz.')

            # Dosya türü kontrolü
            allowed_types = getattr(settings, 'CONTENT_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            if hasattr(self.fotograf, 'content_type') and self.fotograf.content_type not in allowed_types:
                raise ValidationError('Desteklenmeyen dosya türü. Sadece JPEG, PNG, GIF ve WEBP formatları kabul edilir.')

            # Dosya adı güvenlik kontrolü
            if hasattr(self.fotograf, 'name'):
                import re
                # Güvenli dosya adı kontrolü
                if not re.match(r'^[a-zA-Z0-9._-]+$', os.path.basename(self.fotograf.name)):
                    raise ValidationError('Dosya adı güvenli karakterler içermelidir.')

        # Çalışma ile ilişki kontrolü
        if self.calisma and self.user and self.calisma.user != self.user:
            raise ValidationError(
                'Fotoğraf kullanıcısı ile çalışma kullanıcısı aynı olmalıdır.'
            )

    def delete(self, *args, **kwargs):
        """
        Fotoğraf modeli silinir.
        Dosya silme işlemi post_delete signal'da yapılır.
        """
        super().delete(*args, **kwargs)


# Signal handlers
@receiver(pre_save, sender=CalismaFotograf)
def calisma_fotograf_pre_save(sender, instance, **kwargs):
    """
    CalismaFotograf kaydedilmeden önce user alanını otomatik doldur ve validasyon yap.
    """
    try:
        # Eğer user alanı boşsa ve calisma.user varsa, onu kullan
        if not instance.user and instance.calisma and instance.calisma.user:
            instance.user = instance.calisma.user
            logger.debug(f"CalismaFotograf user alanı otomatik dolduruldu: {instance.calisma.user.username}")

        # Kritik durum kontrolü - user hala yoksa
        if not instance.user:
            error_msg = "CalismaFotograf için user alanı boş ve otomatik doldurulamadı"
            logger.error(error_msg, extra={
                'instance_id': getattr(instance, 'id', 'None'),
                'calisma_id': getattr(instance.calisma, 'id', 'None') if instance.calisma else 'None',
                'has_calisma': bool(instance.calisma),
                'calisma_has_user': bool(instance.calisma and instance.calisma.user) if instance.calisma else False,
                'operation': 'pre_save_signal'
            })
            # Bu durumda model validation'da hata verilecek

    except Exception as e:
        logger.error(f"CalismaFotograf pre_save signal hatası: {e}", extra={
            'instance_id': getattr(instance, 'id', 'None'),
            'error': str(e),
            'operation': 'pre_save_signal'
        })

@receiver(post_delete, sender=CalismaFotograf)
def calisma_fotograf_post_delete(sender, instance, **kwargs):
    """
    CalismaFotograf silindikten sonra dosyayı güvenli bir şekilde sil.
    Bu yaklaşım memory-efficient'tir ve CASCADE ile otomatik çalışır.
    """
    try:
        if instance.fotograf:
            from utils.file_management import safe_delete_file

            # Dosya yolunu al
            file_path = instance.fotograf.path

            logger.info(f"CalismaFotograf dosyası siliniyor: {file_path} (User: {instance.user})")

            # Dosyayı güvenli bir şekilde sil
            success, error_msg = safe_delete_file(file_path, create_backup=True)

            if success:
                logger.info(f"Çalışma fotoğrafı güvenli bir şekilde silindi: {file_path}")
            else:
                logger.error(f"Çalışma fotoğrafı silinemedi: {error_msg}", extra={
                    'file_path': file_path,
                    'user_id': instance.user.id if instance.user else None,
                    'calisma_id': instance.calisma.id if instance.calisma else None,
                    'operation': 'post_delete_signal'
                })

    except Exception as e:
        logger.error(f"CalismaFotograf post_delete signal hatası: {e}", extra={
            'instance_id': getattr(instance, 'id', 'None'),
            'error': str(e),
            'operation': 'post_delete_signal'
        })

@receiver(m2m_changed, sender=Calisma.begenenler.through)
def begenenler_changed(sender, instance, action, pk_set, **kwargs):
    """
    Calisma.begenenler ManyToManyField değiştiğinde begeni_sayisi alanını otomatik günceller.

    Args:
        sender: Through model (Calisma_begenenler)
        instance: Calisma instance
        action: 'post_add', 'post_remove', 'post_clear'
        pk_set: Eklenen/çıkarılan kullanıcı ID'leri
        **kwargs: Diğer signal parametreleri
    """
    try:
        # Sadece gerçek değişikliklerden sonra güncelle
        if action in ['post_add', 'post_remove', 'post_clear']:
            old_count = instance.begeni_sayisi
            new_count = instance.begenenler.count()

            # Sadece değişiklik varsa güncelle
            if old_count != new_count:
                instance.begeni_sayisi = new_count
                instance.save(update_fields=['begeni_sayisi'])

                logger.debug(f"Beğeni sayısı otomatik güncellendi: {instance.baslik}", extra={
                    'calisma_id': instance.id,
                    'calisma_baslik': instance.baslik,
                    'action': action,
                    'old_count': old_count,
                    'new_count': new_count,
                    'pk_set': list(pk_set) if pk_set else None,
                    'operation': 'begenenler_m2m_changed'
                })

    except Exception as e:
        logger.error(f"Beğeni sayısı güncelleme hatası: {e}", extra={
            'calisma_id': getattr(instance, 'id', 'None'),
            'action': action,
            'error': str(e),
            'operation': 'begenenler_m2m_changed'
        })

@receiver(pre_delete, sender=Calisma)
def calisma_pre_delete(sender, instance, **kwargs):
    """
    Calisma silinmeden önce ilişkili fotoğrafları logla.
    Memory-efficient: sadece count() kullanır, fotoğrafları yüklemez.
    """
    try:
        fotograf_sayisi = instance.fotograflar.count()
        if fotograf_sayisi > 0:
            logger.info(f"Calisma silinecek: {instance.baslik} ({fotograf_sayisi} fotoğraf ile birlikte)", extra={
                'calisma_id': instance.id,
                'calisma_baslik': instance.baslik,
                'fotograf_sayisi': fotograf_sayisi,
                'user_id': instance.user.id if instance.user else None,
                'operation': 'pre_delete_signal'
            })
    except Exception as e:
        logger.error(f"Calisma pre_delete signal hatası: {e}", extra={
            'calisma_id': getattr(instance, 'id', 'None'),
            'error': str(e),
            'operation': 'pre_delete_signal'
        })
