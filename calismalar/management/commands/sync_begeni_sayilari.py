"""
Mevcut çalışmaların beğeni sayılarını senkronize eden management command.
Signal sistemi aktif olduktan sonra mevcut verileri güncellemek için kullanılır.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from calismalar.models import Calisma
from utils.logging import get_logger

logger = get_logger(__name__)

class Command(BaseCommand):
    help = 'Mevcut çalışmaların beğeni sayılarını ManyToManyField ile senkronize eder'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Sad<PERSON><PERSON> rapor gösterir, değişiklik yapmaz',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Detaylı çıktı gösterir',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS('Beğeni sayıları senkronizasyon işlemi başlatılıyor...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN modu - hiçbir değişiklik yapılmayacak')
            )
        
        # Tüm çalışmaları al
        calismalar = Calisma.objects.all()
        total_count = calismalar.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.WARNING('Senkronize edilecek çalışma bulunamadı.')
            )
            return
        
        self.stdout.write(f'Toplam {total_count} çalışma kontrol edilecek...')
        
        updated_count = 0
        error_count = 0
        
        with transaction.atomic():
            for i, calisma in enumerate(calismalar, 1):
                try:
                    # Mevcut değerleri al
                    current_begeni_sayisi = calisma.begeni_sayisi
                    actual_begeni_count = calisma.begenenler.count()
                    
                    if verbose:
                        self.stdout.write(
                            f'[{i}/{total_count}] {calisma.baslik[:50]}... '
                            f'(Mevcut: {current_begeni_sayisi}, Gerçek: {actual_begeni_count})'
                        )
                    
                    # Eğer farklıysa güncelle
                    if current_begeni_sayisi != actual_begeni_count:
                        if not dry_run:
                            calisma.begeni_sayisi = actual_begeni_count
                            calisma.save(update_fields=['begeni_sayisi'])
                            
                            logger.info(f"Beğeni sayısı senkronize edildi: {calisma.baslik}", extra={
                                'calisma_id': calisma.id,
                                'old_count': current_begeni_sayisi,
                                'new_count': actual_begeni_count,
                                'operation': 'sync_begeni_sayilari'
                            })
                        
                        updated_count += 1
                        
                        if verbose or not dry_run:
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f'  ✓ Güncellendi: {current_begeni_sayisi} → {actual_begeni_count}'
                                )
                            )
                    else:
                        if verbose:
                            self.stdout.write('  - Zaten senkron')
                
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f'  ✗ Hata: {calisma.baslik[:30]}... - {str(e)}'
                        )
                    )
                    
                    logger.error(f"Beğeni sayısı senkronizasyon hatası: {e}", extra={
                        'calisma_id': calisma.id,
                        'calisma_baslik': calisma.baslik,
                        'error': str(e),
                        'operation': 'sync_begeni_sayilari'
                    })
        
        # Özet rapor
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('SENKRONIZASYON RAPORU'))
        self.stdout.write('='*50)
        self.stdout.write(f'Toplam çalışma: {total_count}')
        self.stdout.write(f'Güncellenen: {updated_count}')
        self.stdout.write(f'Hata: {error_count}')
        self.stdout.write(f'Senkron: {total_count - updated_count - error_count}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    '\nDRY RUN tamamlandı. Gerçek güncellemeler yapılmadı.'
                )
            )
            self.stdout.write(
                'Gerçek güncelleme için: python manage.py sync_begeni_sayilari'
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\nSenkronizasyon tamamlandı! {updated_count} çalışma güncellendi.'
                )
            )
        
        if error_count > 0:
            self.stdout.write(
                self.style.ERROR(
                    f'\n⚠️  {error_count} çalışmada hata oluştu. Loglara bakın.'
                )
            )
