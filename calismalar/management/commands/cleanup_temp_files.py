"""
Geçici fotoğraf dosyalarını temizlemek için management command.
"""

import os
import time
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from utils.logging import get_logger

logger = get_logger(__name__)


class Command(BaseCommand):
    help = 'Geçici fotoğraf dosyalarını ve kullanılmayan dosyaları temizler'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Kaç günden eski geçici dosyalar silinsin (varsayılan: 7)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Gerçekte silmeden sadece ne silineceğini göster'
        )
        
        parser.add_argument(
            '--include-orphaned',
            action='store_true',
            help='Veritabanında kaydı olmayan dosyaları da temizle'
        )

    def handle(self, *args, **options):
        days_to_keep = options['days']
        dry_run = options['dry_run']
        include_orphaned = options['include_orphaned']
        
        self.stdout.write(
            self.style.SUCCESS('Geçici dosya temizleme başlatılıyor...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN modu - hiçbir dosya silinmeyecek')
            )
        
        try:
            # Geçici dosyaları temizle
            temp_cleaned = self._cleanup_temp_files(days_to_keep, dry_run)
            
            # Yetim dosyaları temizle (isteğe bağlı)
            orphaned_cleaned = 0
            if include_orphaned:
                orphaned_cleaned = self._cleanup_orphaned_files(dry_run)
            
            # Sonuçları göster
            total_cleaned = temp_cleaned + orphaned_cleaned
            
            if dry_run:
                self.stdout.write(
                    f'Temizlenecek dosya sayısı: {total_cleaned}'
                )
                self.stdout.write(
                    f'  - Geçici dosyalar: {temp_cleaned}'
                )
                if include_orphaned:
                    self.stdout.write(
                        f'  - Yetim dosyalar: {orphaned_cleaned}'
                    )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Başarıyla tamamlandı! {total_cleaned} dosya temizlendi.'
                    )
                )
                
                logger.info("Geçici dosya temizleme komutu çalıştırıldı", extra={
                    'days_to_keep': days_to_keep,
                    'temp_cleaned': temp_cleaned,
                    'orphaned_cleaned': orphaned_cleaned,
                    'total_cleaned': total_cleaned,
                    'include_orphaned': include_orphaned,
                    'operation': 'cleanup_temp_files_command'
                })
                
        except Exception as e:
            error_msg = f'Hata oluştu: {str(e)}'
            self.stdout.write(
                self.style.ERROR(error_msg)
            )
            
            logger.error("Geçici dosya temizleme komutunda hata", extra={
                'error': str(e),
                'days_to_keep': days_to_keep,
                'operation': 'cleanup_temp_files_command'
            })
            
            raise CommandError(error_msg)

    def _cleanup_temp_files(self, days_to_keep, dry_run):
        """Geçici klasördeki eski dosyaları temizle."""
        temp_dir = Path(settings.MEDIA_ROOT) / 'calisma_fotograflari' / 'temp'
        
        if not temp_dir.exists():
            self.stdout.write('Geçici dosya dizini bulunamadı.')
            return 0
        
        cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
        cleaned_count = 0
        total_size = 0
        
        for temp_file in temp_dir.rglob('*'):
            if temp_file.is_file() and temp_file.stat().st_mtime < cutoff_time:
                file_size = temp_file.stat().st_size
                total_size += file_size
                
                if not dry_run:
                    try:
                        temp_file.unlink()
                        logger.debug(f"Geçici dosya silindi: {temp_file}")
                    except Exception as e:
                        logger.error(f"Geçici dosya silinemedi: {temp_file}, Hata: {e}")
                        continue
                
                cleaned_count += 1
        
        if dry_run:
            self.stdout.write(
                f'Geçici dizinde silinecek dosya: {cleaned_count} ({total_size / 1024 / 1024:.2f} MB)'
            )
        else:
            self.stdout.write(
                f'Geçici dizinden {cleaned_count} dosya silindi ({total_size / 1024 / 1024:.2f} MB)'
            )
        
        return cleaned_count

    def _cleanup_orphaned_files(self, dry_run):
        """Veritabanında kaydı olmayan dosyaları temizle."""
        from calismalar.models import CalismaFotograf
        
        media_dir = Path(settings.MEDIA_ROOT) / 'calisma_fotograflari'
        
        if not media_dir.exists():
            return 0
        
        # Veritabanındaki tüm dosya yollarını al
        db_files = set()
        for foto in CalismaFotograf.objects.all():
            if foto.fotograf:
                db_files.add(Path(foto.fotograf.path).name)
        
        cleaned_count = 0
        total_size = 0
        
        # Tüm dosyaları kontrol et
        for file_path in media_dir.rglob('*'):
            if file_path.is_file() and file_path.name not in db_files:
                # temp klasörü zaten ayrı temizleniyor
                if 'temp' in file_path.parts:
                    continue
                    
                file_size = file_path.stat().st_size
                total_size += file_size
                
                if not dry_run:
                    try:
                        file_path.unlink()
                        logger.debug(f"Yetim dosya silindi: {file_path}")
                    except Exception as e:
                        logger.error(f"Yetim dosya silinemedi: {file_path}, Hata: {e}")
                        continue
                
                cleaned_count += 1
        
        if dry_run:
            self.stdout.write(
                f'Yetim dosya sayısı: {cleaned_count} ({total_size / 1024 / 1024:.2f} MB)'
            )
        else:
            self.stdout.write(
                f'{cleaned_count} yetim dosya silindi ({total_size / 1024 / 1024:.2f} MB)'
            )
        
        return cleaned_count
