from django.urls import path
from . import views

app_name = 'calismalar'

urlpatterns = [
    path('', views.tum_calismalar, name='calisma_listesi'),
    path('yonetici-calismalari/', views.yonetici_calismalari, name='yonetici_calismalari'),
    path('kategori/<slug:slug>/', views.tum_calismalar, name='kategori_calismalari'),
    path('etiket/<slug:slug>/', views.tum_calismalar, name='etiket_calismalari'),
    path('arama/', views.tum_calismalar, name='calisma_arama'),
    # Önce sabit URL'leri tanımlıyoruz
    path('calisma/ekle/', views.calisma_ekle, name='calisma_ekle'),
    path('fotograf/sirala/', views.fotograf_sirala, name='fotograf_sirala'),
    # <PERSON>ra dinamik (slug içeren) URL'leri tanımlıyoruz
    path('calisma/<slug:slug>/', views.calisma_detay, name='calisma_detay'),
    path('calisma/<slug:slug>/begen/', views.begeni_ekle, name='calisma_begen'),
    path('calisma/<slug:slug>/duzenle/', views.calisma_duzenle, name='calisma_duzenle'),
    path('calisma/<slug:slug>/sil/', views.calisma_sil, name='calisma_sil'),
    path('calisma/<slug:slug>/fotograf-ekle/', views.fotograf_ekle, name='fotograf_ekle'),
    path('fotograf/<int:pk>/sil/', views.fotograf_sil, name='fotograf_sil'),
]
