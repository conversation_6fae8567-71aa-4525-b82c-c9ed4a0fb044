from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count
from .models import Calisma, CalismaFotograf
from urunler.models import Category



class CalismaFotografInline(admin.TabularInline):
    model = CalismaFotograf
    extra = 1
    fields = ('fotograf', 'aciklama', 'sira')
    ordering = ('sira',)

@admin.register(Calisma)
class CalismaAdmin(admin.ModelAdmin):
    list_display = ('baslik', 'user_link', 'kategori', 'goruntulenme_sayisi', 'begeni_sayisi', 'olusturma_tarihi')
    list_filter = ('kategori', 'olusturma_tarihi', 'user')
    search_fields = ('baslik', 'aciklama', 'user__username')
    prepopulated_fields = {'slug': ('baslik',)}
    inlines = [CalismaFotografInline]
    date_hierarchy = 'olusturma_tarihi'
    readonly_fields = ('goruntulenme_sayisi', 'begeni_sayisi')

    def user_link(self, obj):
        url = reverse('admin:auth_user_change', args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = 'Kullanıcı'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'kategori').prefetch_related('tags')

    def save_model(self, request, obj, form, change):
        if not change:  # Yeni çalışma oluşturuluyorsa
            obj.user = request.user
        super().save_model(request, obj, form, change)

        # Kullanıcıyı Çalışma Yöneticileri grubuna ekle
        from django.contrib.auth.models import Group
        group, created = Group.objects.get_or_create(name='Çalışma Yöneticileri')
        if created:
            from django.contrib.auth.models import Permission
            from django.contrib.contenttypes.models import ContentType
            # Çalışma modeli için izinleri al
            calisma_content_type = ContentType.objects.get_for_model(Calisma)
            fotograf_content_type = ContentType.objects.get_for_model(CalismaFotograf)
            kategori_content_type = ContentType.objects.get_for_model(Category)

            # Tüm izinleri gruba ekle
            permissions = Permission.objects.filter(
                content_type__in=[
                    calisma_content_type,
                    fotograf_content_type,
                    kategori_content_type
                ]
            )
            group.permissions.add(*permissions)

        obj.user.groups.add(group)

@admin.register(CalismaFotograf)
class CalismaFotografAdmin(admin.ModelAdmin):
    list_display = ('calisma_link', 'sira', 'olusturma_tarihi')
    list_filter = ('calisma__user',)
    search_fields = ('calisma__baslik', 'aciklama')
    ordering = ('calisma', 'sira')

    def calisma_link(self, obj):
        url = reverse('admin:calismalar_calisma_change', args=[obj.calisma.id])
        return format_html('<a href="{}">{}</a>', url, obj.calisma.baslik)
    calisma_link.short_description = 'Çalışma'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('calisma', 'calisma__user')
