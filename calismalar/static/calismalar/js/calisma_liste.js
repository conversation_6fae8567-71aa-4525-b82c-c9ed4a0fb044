// Çalışma Listesi Sayfası JavaScript Kodları
document.addEventListener('DOMContentLoaded', function() {
    // Arama formu submit işlemi
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const searchInput = this.querySelector('input[name="q"]');
            if (searchInput && searchInput.value.trim() === '') {
                e.preventDefault();
            }
        });
    }

    // Arama sonuçlarında vurgulama
    const searchQuery = new URLSearchParams(window.location.search).get('q');
    if (searchQuery) {
        highlightSearchResults(searchQuery);
    }
});

// Arama sonuçlarını vurgula
function highlightSearchResults(query) {
    const elements = document.querySelectorAll('.calisma-baslik, .calisma-aciklama');
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 2);
    
    if (searchTerms.length === 0) return;

    elements.forEach(element => {
        let html = element.innerHTML;
        searchTerms.forEach(term => {
            const regex = new RegExp(`(${term})`, 'gi');
            html = html.replace(regex, '<span class="search-highlight">$1</span>');
        });
        element.innerHTML = html;
    });
}
