/* Utility Classes - Inline Style Replacements */
.calisma-card-image-container {
    height: 180px;
}

.user-avatar {
    width: 30px;
    height: 30px;
    object-fit: cover;
}

.user-name {
    color: var(--color-brown);
}

.form-preview-image {
    height: 50px;
}

/* Ürünler ile uyu<PERSON>lu kart stilleri */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    aspect-ratio: 4/3;
    width: 100%;
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.modern-card:hover .image-container img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--color-brown-rgb), 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-card:hover .image-overlay {
    opacity: 1;
}

.image-overlay .modern-btn-circle {
    margin: 0 0.5rem;
    padding: 0.75rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-brown);
    text-decoration: none;
    transition: all 0.3s ease;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-overlay .modern-btn-circle:hover {
    background: white;
    transform: scale(1.1);
}

.no-image-placeholder {
    width: 100%;
    aspect-ratio: 4/3;
    background-color: var(--soft-light);
    border-radius: 8px;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border: 2px dashed var(--border-color);
}

.no-image-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.modern-badge {
    background: rgba(var(--color-sand-rgb), 0.3);
    color: var(--color-brown);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.modern-badge-corner {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--color-moss);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    z-index: 2;
}

/* Modern Tag Cloud */
.modern-tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.modern-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: rgba(var(--color-moss-rgb), 0.1);
    color: var(--color-moss);
    border-radius: 15px;
    font-size: 0.75rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--color-moss-rgb), 0.2);
}

.modern-tag:hover {
    background-color: var(--color-moss);
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
}

/* Link butonları için özel stil */
.modern-btn-link {
    cursor: pointer !important;
}

.modern-btn-link:hover {
    background: var(--color-navy) !important;
    color: white !important;
}

/* Çalışma Listesi Sayfası Stilleri */

/* Filtre Bölümü */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: all 0.3s ease;
}

.filter-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.filter-title {
    color: #495057;
    font-size: 1rem;
    margin-bottom: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-title i {
    margin-right: 8px;
    color: #6c757d;
    font-size: 1.1em;
}

/* Modern Tag Cloud */
.modern-tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.modern-tag {
    display: inline-block;
    padding: 4px 10px;
    background-color: white;
    color: #495057;
    border-radius: 4px;
    font-size: 0.85rem;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    margin: 2px;
    line-height: 1.5;
}

.modern-tag:hover, .modern-tag.active {
    background-color: #f8f9fa;
    color: #212529;
    border-color: #adb5bd;
    transform: none;
    box-shadow: none;
}

/* Arama Formu Stilleri */
.search-form {
    position: relative;
    margin-bottom: 15px;
}

.animated-search-group {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #ced4da;
    display: flex;
}

.animated-search-group:hover, .animated-search-group:focus-within {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.animated-search-input {
    border: none;
    border-radius: 4px 0 0 4px;
    padding: 8px 15px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background-color: white;
    width: 100%;
    height: auto;
    flex: 1;
}

.animated-search-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--color-brown-rgb), 0.15);
}

.modern-btn-search {
    border: none;
    background: #f8f9fa;
    color: #6c757d;
    border-radius: 0 4px 4px 0;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 1px solid #ced4da;
}

.modern-btn-search:hover {
    background: #e9ecef;
    color: #495057;
    transform: none;
}

/* Arama Sonuçları Vurgusu */
.search-highlight {
    background-color: rgba(var(--color-brown-rgb), 0.2);
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: 500;
}

/* Ürün Grid */
.product-grid {
    margin-top: 20px;
}

/* Responsive Düzenlemeler */
@media (max-width: 768px) {
    .filter-section {
        margin-bottom: 20px;
    }
    
    .modern-tag {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
}
