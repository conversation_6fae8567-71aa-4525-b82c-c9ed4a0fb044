# Generated manually for category data transfer

from django.db import migrations

def transfer_categories(apps, schema_editor):
    """calismalar kategorilerini urunler.Category'ye transfer et"""
    # Eski modelleri al
    CalismaKategori = apps.get_model('calismalar', 'Kategori')
    UrunlerCategory = apps.get_model('urunler', 'Category')
    
    # Mevcut calismalar kategorilerini urunler.Category'ye kopyala
    for kategori in CalismaKategori.objects.all():
        # Aynı slug'a sahip kategori var mı kontrol et
        existing_category = UrunlerCategory.objects.filter(slug=kategori.slug).first()
        if not existing_category:
            UrunlerCategory.objects.create(
                ad=kategori.ad,
                slug=kategori.slug,
                aciklama=kategori.aciklama
            )

def reverse_transfer_categories(apps, schema_editor):
    """Geri alma işlemi - gerekirse kategorileri geri oluştur"""
    pass  # Bu durumda geri alma yapmıyoruz

class Migration(migrations.Migration):

    dependencies = [
        ('calismalar', '0001_initial'),
        ('urunler', '0003_alter_category_options_and_more'),
    ]

    operations = [
        migrations.RunPython(transfer_categories, reverse_transfer_categories),
    ]
