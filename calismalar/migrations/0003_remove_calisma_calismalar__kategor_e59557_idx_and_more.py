# Generated by Django 4.2 on 2025-06-11 21:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('calismalar', '0002_remove_calisma_begenenler_calisma_begeni_sayisi'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='calisma',
            name='calismalar__kategor_e59557_idx',
        ),
        migrations.RemoveIndex(
            model_name='calisma',
            name='calismalar__user_id_17b03c_idx',
        ),
        migrations.RenameIndex(
            model_name='calisma',
            new_name='calisma_olusturma_idx',
            old_name='calismalar__olustur_eefdc4_idx',
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['-begeni_sayisi'], name='calisma_begeni_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['slug'], name='calisma_slug_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['user', '-olusturma_tarihi'], name='calisma_user_tarih_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['kategori', '-olusturma_tarihi'], name='calisma_kategori_tarih_idx'),
        ),
    ]
