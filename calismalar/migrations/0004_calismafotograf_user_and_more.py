# Generated by Django 4.2 on 2025-06-12 09:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('calismalar', '0003_remove_calisma_calismalar__kategor_e59557_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='calismafotograf',
            name='user',
            field=models.ForeignKey(blank=True, help_text='Fotoğrafı yükleyen kullanıcı (otomatik doldurulur)', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Kullanıcı'),
        ),
        migrations.AddIndex(
            model_name='calismafotograf',
            index=models.Index(fields=['user', '-olusturma_tarihi'], name='calismalar__user_id_628507_idx'),
        ),
    ]
