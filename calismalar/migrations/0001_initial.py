# Generated by Django 5.2 on 2025-06-08 01:58

import calismalar.models
import django.db.models.deletion
import taggit.managers
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Kategor<PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad', models.CharField(max_length=100, verbose_name='<PERSON>gor<PERSON> Adı')),
                ('slug', models.SlugField(unique=True)),
                ('aciklama', models.TextField(blank=True, verbose_name='Açıklama')),
            ],
            options={
                'verbose_name': '<PERSON><PERSON><PERSON>',
                'verbose_name_plural': 'Kategoriler',
                'ordering': ['ad'],
            },
        ),
        migrations.CreateModel(
            name='Calisma',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('baslik', models.CharField(max_length=200, verbose_name='Başlık')),
                ('aciklama', models.TextField(verbose_name='Açıklama')),
                ('slug', models.SlugField(max_length=250, unique=True)),
                ('goruntulenme_sayisi', models.PositiveIntegerField(default=0, verbose_name='Görüntülenme Sayısı')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('guncelleme_tarihi', models.DateTimeField(auto_now=True, verbose_name='Güncelleme Tarihi')),
                ('begenenler', models.ManyToManyField(blank=True, related_name='begenilen_calismalar', to=settings.AUTH_USER_MODEL)),
                ('tags', taggit.managers.TaggableManager(blank=True, help_text='Çalışmayla ilgili etiketleri virgülle ayırarak girin', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Etiketler')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calismalar', to=settings.AUTH_USER_MODEL)),
                ('kategori', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='calismalar', to='calismalar.kategori')),
            ],
            options={
                'verbose_name': 'Çalışma',
                'verbose_name_plural': 'Çalışmalar',
                'ordering': ['-olusturma_tarihi'],
            },
        ),
        migrations.CreateModel(
            name='CalismaFotograf',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fotograf', models.ImageField(upload_to=calismalar.models.calisma_fotograf_yolu, verbose_name='Fotoğraf')),
                ('aciklama', models.CharField(blank=True, max_length=255, verbose_name='Açıklama')),
                ('sira', models.PositiveIntegerField(default=0, verbose_name='Sıra')),
                ('olusturma_tarihi', models.DateTimeField(auto_now_add=True, verbose_name='Oluşturma Tarihi')),
                ('calisma', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fotograflar', to='calismalar.calisma')),
            ],
            options={
                'verbose_name': 'Çalışma Fotoğrafı',
                'verbose_name_plural': 'Çalışma Fotoğrafları',
                'ordering': ['sira', 'olusturma_tarihi'],
                'indexes': [models.Index(fields=['calisma', 'sira'], name='calismalar__calisma_141140_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['-olusturma_tarihi'], name='calismalar__olustur_eefdc4_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['kategori'], name='calismalar__kategor_e59557_idx'),
        ),
        migrations.AddIndex(
            model_name='calisma',
            index=models.Index(fields=['user'], name='calismalar__user_id_17b03c_idx'),
        ),
    ]
