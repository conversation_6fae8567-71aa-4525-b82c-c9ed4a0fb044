{% extends 'base.html' %}
{% load static %}

{% block title %}Çalışma Sil - Küp Cadısı{% endblock %}

{% block content %}
<section class="calisma-delete-section py-5">
    <div class="container">
        <!-- Ba<PERSON>l<PERSON><PERSON> Bölümü -->
        <div class="section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="display-4 fw-bold mb-3 section-title">Çalışma Sil</h1>
            <p class="lead section-subtitle">Çalışmayı silmek istediğinizden emin misiniz?</p>
            <div class="title-decoration">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="delete-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Silme <PERSON></h5>
                    </div>
                    <div class="card-body">
                        <div class="delete-warning">
                            <div class="warning-icon">
                                <i class="bi bi-exclamation-circle"></i>
                            </div>
                            <div class="warning-content">
                                <h4 class="warning-title">Dikkat!</h4>
                                <p class="warning-text">
                                    "<strong>{{ calisma.baslik }}</strong>" başlıklı çalışmayı silmek üzeresiniz. 
                                    Bu işlem geri alınamaz ve çalışmaya ait tüm fotoğraflar ve bilgiler kalıcı olarak silinecektir.
                                </p>
                            </div>
                        </div>

                        <div class="calisma-preview mt-4">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="preview-image">
                                        {% if calisma.fotograflar.exists %}
                                        <img src="{{ calisma.fotograflar.first.fotograf.url }}" alt="{{ calisma.baslik }}" class="img-fluid rounded">
                                        {% else %}
                                        <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok" class="img-fluid rounded">
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="preview-details">
                                        <h5 class="preview-title">{{ calisma.baslik }}</h5>
                                        <div class="preview-meta">
                                            {% if calisma.kategori %}
                                            <span class="preview-category">
                                                <i class="bi bi-folder me-1"></i>{{ calisma.kategori.ad }}
                                            </span>
                                            {% endif %}
                                            <span class="preview-date">
                                                <i class="bi bi-calendar me-1"></i>{{ calisma.olusturma_tarihi|date:"d.m.Y" }}
                                            </span>
                                            <span class="preview-views">
                                                <i class="bi bi-eye me-1"></i>{{ calisma.goruntulenme_sayisi }} görüntülenme
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <form method="post" class="mt-4">
                            {% csrf_token %}
                            <div class="form-actions">
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-trash me-2"></i>Evet, Çalışmayı Sil
                                </button>
                                <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="btn btn-outline-secondary ms-2">
                                    <i class="bi bi-x-circle me-2"></i>İptal
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    /* Silme Sayfası Stilleri */
    .delete-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        background-color: #fff;
        transition: all 0.3s ease;
    }

    .delete-card .card-header {
        background: linear-gradient(to right, #f8f9fa, #e9ecef);
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .delete-card .card-body {
        padding: 2rem;
    }

    .delete-warning {
        display: flex;
        align-items: flex-start;
        background-color: rgba(220, 53, 69, 0.1);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #dc3545;
    }

    .warning-icon {
        font-size: 2.5rem;
        color: #dc3545;
        margin-right: 1.5rem;
        line-height: 1;
    }

    .warning-title {
        color: #dc3545;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }

    .warning-text {
        color: #343a40;
        margin-bottom: 0;
    }

    .calisma-preview {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .preview-image {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .preview-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
        color: #734429;
    }

    .preview-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .form-actions {
        margin-top: 2rem;
        display: flex;
        align-items: center;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }

    .btn-outline-secondary {
        color: #6c757d;
        border-color: #6c757d;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }
</style>
{% endblock %}