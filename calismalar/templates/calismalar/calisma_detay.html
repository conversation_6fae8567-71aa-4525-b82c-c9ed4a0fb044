{% extends 'base.html' %}
{% load static %}

{% block title %}{{ calisma.baslik }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css">
{% endblock %}

{% block content %}
<main class="modern-section py-5">
    <div class="container modern-container">
        <!-- CSRF Token for AJAX requests -->
        {% csrf_token %}

        <!-- Ba<PERSON><PERSON><PERSON>k Bölümü -->
        <div class="modern-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="gradient-heading display-4 fw-bold mb-3"><PERSON><PERSON><PERSON>şma Detayları</h1>
            <p class="lead text-muted">"{{ calisma.baslik }}" çalışması hakkında detaylı bilgiler</p>
        </div>

        <div class="row g-4 mb-5">
            <!-- Çalışma Görselleri -->
            <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.3s">
                <div class="modern-card mb-4">
                    {% if calisma.fotograflar.exists %}
                    <div class="swiper product-slider">
                        <div class="swiper-wrapper">
                            {% for fotograf in calisma.fotograflar.all %}
                            <div class="swiper-slide">
                                <div class="image-container">
                                    <img src="{{ fotograf.fotograf.url }}" alt="{{ calisma.baslik }}" class="modern-image">
                                    <a href="{{ fotograf.fotograf.url }}" class="modern-btn-circle position-absolute top-50 start-50 translate-middle" title="{{ calisma.baslik }}" data-gallery="calisma-gallery">
                                        <i class="bi bi-zoom-in"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <img src="{% static 'img/no-image.jpg' %}" alt="Görsel Yok" class="img-fluid">
                        <p class="mt-3 text-muted">Bu çalışma için henüz görsel eklenmemiş.</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Çalışma Açıklaması -->
                <div class="card featured-content-card mb-4">
                    <div class="card__header autumn-card-header-alt">
                        <h3 class="card__title"><i class="bi bi-file-text me-2"></i>Açıklama</h3>
                    </div>
                    <div class="card__body">
                        <div class="modern-content">
                            {{ calisma.aciklama|linebreaks }}
                        </div>

                        <!-- Ekleyen Bilgisi -->
                        <div class="creator-info mt-4 p-3 bg-light rounded">
                            <div class="d-flex align-items-center">
                                <div class="creator-avatar me-3">
                                    <i class="bi bi-person-circle text-primary" style="font-size: 2rem;"></i>
                                </div>
                                <div class="creator-details">
                                    <h6 class="mb-1">
                                        <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="text-decoration-none text-primary fw-bold">
                                            <i class="bi bi-person-badge me-1"></i>
                                            {{ calisma.user.get_full_name|default:calisma.user.username }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ calisma.olusturma_tarihi|date:"d F Y, H:i" }} tarihinde paylaştı
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Yan Bilgiler -->
            <div class="col-lg-4">
                <!-- Çalışma Bilgileri - Özellikli Kart -->
                <div class="card mb-4 wow fadeInRight calisma-bilgi-karti" data-wow-delay="0.3s">
                    <div class="card__header autumn-card-header">
                        <h3 class="card__title"><i class="bi bi-info-square me-2"></i>Çalışma Bilgileri</h3>
                    </div>
                    <div class="card__body p-0">
                        <div class="modern-info-list">
                            <div class="modern-info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-person-badge"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Oluşturan</span>
                                    <span class="info-value">
                                        <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="creator-highlight">
                                            <i class="bi bi-person-circle me-1"></i>
                                            {{ calisma.user.get_full_name|default:calisma.user.username }}
                                        </a>
                                    </span>
                                </div>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-calendar-event"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Tarih</span>
                                    <span class="info-value">{{ calisma.olusturma_tarihi|date:"d.m.Y" }}</span>
                                </div>
                            </div>
                            {% if calisma.kategori %}
                            <div class="modern-info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Kategori</span>
                                    <span class="info-value">
                                        <a href="{% url 'calismalar:kategori_calismalari' calisma.kategori.slug %}" class="category-link">
                                            <i class="bi bi-tag me-1"></i>{{ calisma.kategori.ad }}
                                        </a>
                                    </span>
                                </div>
                            </div>
                            {% endif %}
                            <div class="modern-info-item modern-list-item">
                                <div class="info-icon autumn-icon">
                                    <i class="bi bi-eye-fill"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Görüntülenme</span>
                                    <span class="info-value">
                                        <span class="badge bg-light text-dark">
                                            <i class="bi bi-graph-up me-1"></i>{{ calisma.goruntulenme_sayisi }}
                                        </span>
                                    </span>
                                </div>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <div class="info-icon autumn-icon like-icon{% if kullanici_begendi %} active{% endif %}" data-calisma-id="{{ calisma.id }}">
                                    <i class="bi bi-heart{% if kullanici_begendi %}-fill{% endif %}"></i>
                                </div>
                                <div class="info-content">
                                    <span class="info-title">Beğeni</span>
                                    <span class="info-value">
                                        <span class="badge bg-light text-dark">
                                            <i class="bi bi-hand-thumbs-up me-1"></i><span id="like-count">{{ calisma.begeni_sayisi }}</span>
                                        </span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        {% if calisma.tamamlanma_yuzdesi %}
                        <div class="completion-status">
                            <h5><i class="bi bi-graph-up me-2"></i>Proje Tamamlanma Durumu</h5>
                            <div class="progress autumn-progress">
                                <div class="progress-bar progress-bar-custom" role="progressbar"
                                     style="width: {{ calisma.tamamlanma_yuzdesi|default:0 }}%"
                                     data-progress="{{ calisma.tamamlanma_yuzdesi|default:0 }}">
                                    {{ calisma.tamamlanma_yuzdesi|default:0 }}%
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Etiketler -->
                <div class="card mb-4 wow fadeInRight etiketler-karti" data-wow-delay="0.4s">
                    <div class="card__header autumn-card-header">
                        <h3 class="card__title"><i class="bi bi-tags me-2"></i>Etiketler</h3>
                    </div>
                    <div class="card__body">
                        {% if calisma.tags.exists %}
                        <div class="modern-tag-cloud">
                            {% for tag in calisma.tags.all %}
                            <a href="?etiket={{ tag.slug }}" class="modern-tag autumn-tag">{{ tag.name }}</a>
                            {% endfor %}
                        </div>
                        {% else %}
                        <p class="text-muted">Bu çalışma için henüz etiket eklenmemiş.</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Benzer Çalışmalar -->
                <div class="card mb-4 wow fadeInRight benzer-calismalar-karti" data-wow-delay="0.5s">
                    <div class="card__header autumn-card-header">
                        <h3 class="card__title"><i class="bi bi-collection me-2"></i>Benzer Çalışmalar</h3>
                    </div>
                    <div class="card__body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for benzer in benzer_calismalar %}
                            <li>
                                <a href="{% url 'calismalar:calisma_detay' benzer.slug %}" class="modern-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        {% if benzer.fotograflar.exists %}
                                        <img src="{{ benzer.fotograflar.first.fotograf.url }}" alt="{{ benzer.baslik }}">
                                        {% else %}
                                        <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok">
                                        {% endif %}
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ benzer.baslik }}</h6>
                                        <span class="text-muted small">{{ benzer.olusturma_tarihi|date:"d.m.Y" }}</span>
                                    </div>
                                </a>
                            </li>
                            {% empty %}
                            <li class="text-center p-3">
                                <span class="text-muted">Benzer çalışma bulunamadı</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <!-- Yönetim Paneli (Çalışma Yöneticileri ve Üstü) -->
                {% if user_perms.can_add_calisma or user_perms.can_edit_calisma %}
                <div class="card admin-panel-card mb-4 wow fadeInRight" data-wow-delay="0.6s">
                    <div class="card__header autumn-card-header-gradient">
                        <h3 class="card__title"><i class="bi bi-gear-fill me-2"></i>Yönetim Paneli</h3>
                    </div>
                    <div class="card__body p-0">
                        <!-- Yönetim Menüsü -->
                        <div class="admin-menu">
                            <!-- Yeni Çalışma Ekle -->
                            <a href="{% url 'calismalar:calisma_ekle' %}" class="admin-menu-item">
                                <div class="admin-menu-icon">
                                    <i class="bi bi-plus-circle-fill" style="color: white; font-size: 1rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Yeni Çalışma Ekle</h5>
                                    <p class="text-muted admin-menu-description">Portfolyonuza yeni bir çalışma ekleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Bu Çalışmayı Düzenle -->
                            <a href="{% url 'calismalar:calisma_duzenle' calisma.slug %}" class="admin-menu-item">
                                <div class="admin-menu-icon" style="background-color: #2c5aa0;">
                                    <i class="bi bi-pencil-square" style="color: white;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Bu Çalışmayı Düzenle</h5>
                                    <p class="text-muted admin-menu-description">Mevcut çalışmanın içeriğini güncelleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>


                            <!-- Çalışmayı Sil -->
                            <a href="#" class="admin-menu-item delete-item" onclick="confirmDelete(event, '{% url 'calismalar:calisma_sil' calisma.slug %}', 'calisma')">
                                <div class="admin-menu-icon delete-icon">
                                    <i class="bi bi-trash3-fill" style="color: white; font-size: 1rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Çalışmayı Sil</h5>
                                    <p class="text-muted admin-menu-description">Bu çalışmayı kalıcı olarak silin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>


                            <!-- Çalışma Listesine Dön -->
                            <a href="{% url 'calismalar:calisma_listesi' %}" class="admin-menu-item">
                                <div class="admin-menu-icon back-icon">
                                    <i class="bi bi-arrow-left-circle"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Çalışma Listesine Dön</h5>
                                    <p class="text-muted admin-menu-description">Tüm çalışmaları görüntüleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if user_perms.can_edit_calisma or user_perms.can_delete_calisma %}


                {% endif %}
            </div>
        </div>

        <!-- Geri ve İleri Butonları -->
        <div class="navigation-buttons mb-4 wow fadeInUp" data-wow-delay="0.3s">
            <div class="row">
                {% if onceki_calisma %}
                <div class="col-6">
                    <a href="{% url 'calismalar:calisma_detay' onceki_calisma.slug %}" class="modern-btn-nav prev autumn-nav-btn">
                        <i class="bi bi-arrow-left me-2"></i>
                        <span class="d-none d-sm-inline">Önceki:</span> {{ onceki_calisma.baslik|truncatechars:25 }}
                    </a>
                </div>
                {% else %}
                <div class="col-6"></div>
                {% endif %}

                {% if sonraki_calisma %}
                <div class="col-6 text-end">
                    <a href="{% url 'calismalar:calisma_detay' sonraki_calisma.slug %}" class="modern-btn-nav next autumn-nav-btn">
                        {{ sonraki_calisma.baslik|truncatechars:25 }} <span class="d-none d-sm-inline">:Sonraki</span>
                        <i class="bi bi-arrow-right ms-2"></i>
                    </a>
                </div>
                {% else %}
                <div class="col-6"></div>
                {% endif %}
            </div>
        </div>
    </div>
</main>
{% endblock %}



{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Swiper başlatma
        const swiper = new Swiper('.product-slider', {
            loop: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            autoplay: {
                delay: 5000,
                disableOnInteraction: false
            }
        });

        // GLightbox başlatma
        const lightbox = GLightbox({
            selector: '.modern-btn-circle',
            touchNavigation: true,
            loop: true,
            autoplayVideos: true
        });

        // Beğenme işlemi - Tamamen yeniden düzenlendi
        document.addEventListener('click', function(e) {
            // Tıklanan eleman veya üst elemanlarından biri like-icon sınıfına sahip mi kontrol et
            const likeButton = e.target.closest('.like-icon');

            if (likeButton) {
                console.log('Beğeni butonuna tıklandı');
                const calismaId = likeButton.getAttribute('data-calisma-id');
                const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;

                fetch('/calismalar/calisma/{{ calisma.slug }}/begen/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: ''
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Beğeni yanıtı:', data);
                    if (data.success) {
                        // Beğeni sayısını güncelle
                        const likeCount = document.getElementById('like-count');
                        if (likeCount) {
                            likeCount.textContent = data.like_count;
                        }

                        // Beğeni ikonunun görünümünü değiştir
                        const heartIcon = likeButton.querySelector('i');
                        if (data.liked) {
                            likeButton.classList.add('active');
                            if (heartIcon) {
                                heartIcon.classList.remove('bi-heart');
                                heartIcon.classList.add('bi-heart-fill');
                            }
                        } else {
                            likeButton.classList.remove('active');
                            if (heartIcon) {
                                heartIcon.classList.remove('bi-heart-fill');
                                heartIcon.classList.add('bi-heart');
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error("Beğeni işlemi sırasında hata oluştu:", error);
                });
            }
        });

        // WOW.js başlatma
        new WOW().init();

        // Custom confirm dialog artık global olarak yüklü

    });
</script>
{% endblock %}
