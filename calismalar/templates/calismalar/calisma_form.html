{% extends 'base.html' %}
{% load static %}

{% block title %}{% if form.instance.id %}Çalışma Düzenle{% else %}<PERSON><PERSON>{% endif %} - <PERSON><PERSON>p Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
<style>
/* Modern Upload Area Styles */
.modern-upload-area {
    border: 2px dashed var(--border-color, #e0e0e0);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    background: var(--background-light, #fafafa);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.modern-upload-area:hover,
.modern-upload-area.dragover {
    border-color: var(--primary-color, #007bff);
    background: var(--primary-light, #f0f8ff);
    transform: translateY(-2px);
}

.upload-zone {
    padding: 1rem;
}

.upload-icon {
    margin-bottom: 1rem;
}

.upload-text h5 {
    color: var(--text-primary, #333);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.upload-text p {
    margin-bottom: 0.5rem;
}

#browse-files:hover {
    text-decoration: underline;
}

/* Photo Items Grid */
.existing-photos,
.new-photos {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.photo-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.photo-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.photo-preview {
    position: relative;
    width: 100%;
    height: 120px;
    overflow: hidden;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-item:hover .photo-preview img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.btn-remove {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #c82333;
    transform: scale(1.1);
}

.photo-form-hidden {
    display: none;
}

/* Progress Bar */
.upload-progress {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.upload-progress-bar {
    height: 100%;
    background: var(--primary-color, #007bff);
    transition: width 0.3s ease;
    width: 0%;
}

/* Loading State */
.photo-item.loading {
    opacity: 0.7;
}

.photo-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color, #007bff);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .existing-photos,
    .new-photos {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .photo-preview {
        height: 100px;
    }

    .modern-upload-area {
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">{% if form.instance.id %}Çalışma Düzenle{% else %}Yeni Çalışma Ekle{% endif %}</h1>
            <p class="modern-subtitle">Küp Cadısı atölyesine yeni çalışmanızı ekleyin veya mevcut çalışmanızı düzenleyin</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-pencil-square me-2"></i>Çalışma Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <form method="post" enctype="multipart/form-data" class="form form--calisma" novalidate>
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Başlık -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form__label">Başlık <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.errors %}
                                        <div class="form__feedback form__feedback--invalid">
                                            {{ form.baslik.errors|striptags }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form__group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form__label">Kategori</label>
                                        {{ form.kategori }}
                                        {% if form.kategori.errors %}
                                        <div class="form__feedback form__feedback--invalid">
                                            {{ form.kategori.errors|striptags }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form__group">
                                        <label for="{{ form.tags.id_for_label }}" class="form__label">Etiketler</label>
                                        {{ form.tags }}
                                        <small class="form__help">Etiketleri virgülle ayırarak girin (örn: ahşap, el yapımı, vintage)</small>
                                        {% if form.tags.errors %}
                                        <div class="form__feedback form__feedback--invalid">
                                            {{ form.tags.errors|striptags }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form__label">Açıklama <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.errors %}
                                        <div class="form__feedback form__feedback--invalid">
                                            {{ form.aciklama.errors|striptags }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Fotoğraflar -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label class="form__label">Fotoğraflar</label>

                                        {{ formset.management_form }}

                                        <!-- Modern Drag & Drop Upload Area -->
                                        <div class="modern-upload-area" id="upload-area">
                                            <div class="upload-zone" id="upload-zone">
                                                <div class="upload-icon">
                                                    <i class="bi bi-cloud-upload" style="font-size: 3rem; color: var(--primary-color); opacity: 0.7;"></i>
                                                </div>
                                                <div class="upload-text">
                                                    <h5>Fotoğrafları buraya sürükleyin</h5>
                                                    <p class="text-muted">veya <span class="text-primary" style="cursor: pointer;" id="browse-files">dosya seçin</span></p>
                                                    <small class="text-muted">PNG, JPG, JPEG formatları desteklenir (Max: 5MB)</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Mevcut Fotoğraflar -->
                                        <div class="existing-photos mt-3" id="existing-photos">
                                            {% for foto_form in formset %}
                                            {% if foto_form.instance.pk %}
                                            <div class="photo-item" data-form-index="{{ forloop.counter0 }}">
                                                <div class="photo-preview">
                                                    <img src="{{ foto_form.instance.fotograf.url }}" alt="Fotoğraf">
                                                    <div class="photo-overlay">
                                                        <button type="button" class="btn-remove" data-form-index="{{ forloop.counter0 }}">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <!-- Hidden form fields for existing photos -->
                                                <div style="display: none;">
                                                    {{ foto_form.id }}
                                                    {{ foto_form.fotograf }}
                                                    {{ foto_form.DELETE }}
                                                </div>
                                            </div>
                                            {% endif %}
                                            {% endfor %}
                                        </div>

                                        <!-- Yeni Eklenen Fotoğraflar -->
                                        <div class="new-photos mt-3" id="new-photos"></div>

                                        <!-- Hidden Formset - Only for new uploads -->
                                        <div class="photo-formset" style="display: none;">
                                            <!-- New forms will be added here dynamically -->
                                        </div>

                                        <!-- Hidden file input for browse -->
                                        <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form__actions d-flex justify-content-between align-items-center">
                                        <div>
                                            <button type="submit" class="button button--primary">
                                                <i class="bi bi-check-circle me-2"></i>{% if form.instance.id %}Güncelle{% else %}Kaydet{% endif %}
                                            </button>
                                            <a href="{% url 'calismalar:calisma_listesi' %}" class="button button--outline ms-2">
                                                <i class="bi bi-x-circle me-2"></i>İptal
                                            </a>
                                        </div>
                                        {% if form.instance.id %}
                                        <div>
                                            <a href="#" class="button button--danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                                <i class="bi bi-trash me-2"></i>Sil
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if form.instance.id %}
<!-- Silme Onay Modalı -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal__content">
            <div class="modal__header">
                <h5 class="modal__title" id="deleteModalLabel"><i class="bi bi-exclamation-triangle-fill me-2"></i>Çalışmayı Sil</h5>
                <button type="button" class="modal__close" data-bs-dismiss="modal" aria-label="Kapat"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal__body">
                <p>Bu çalışmayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                <p class="fw-bold mt-2"><strong>{{ form.instance.baslik }}</strong></p>
            </div>
            <div class="modal__footer">
                <button type="button" class="button button--outline" data-bs-dismiss="modal">İptal</button>
                <form action="{% url 'calismalar:calisma_sil' form.instance.slug %}" method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="button button--danger">Evet, Sil</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}



<script>
    document.addEventListener('DOMContentLoaded', function() {
        // WOW.js başlatma
        new WOW().init();

        // Form submit işlemi
        const form = document.querySelector('.form--calisma');
        if (form) {
            form.addEventListener('submit', function(e) {
                // Boş file input'ları temizle
                const fileInputs = form.querySelectorAll('input[type="file"][name*="fotograflar-"]');
                fileInputs.forEach(input => {
                    if (!input.files || input.files.length === 0 || input.files[0].size === 0) {
                        input.removeAttribute('name');
                    }
                });

                // TOTAL_FORMS sayısını güncelle - SADECE yeni fotoğraflar için
                const totalForms = document.querySelector('input[name$="-TOTAL_FORMS"]');
                if (totalForms) {
                    // Mevcut fotoğraflar sayısını değiştirme, sadece yeni eklenen fotoğrafları ekle
                    const initialForms = parseInt(document.querySelector('input[name$="-INITIAL_FORMS"]').value);
                    const newPhotos = document.querySelectorAll('.photo-formset .photo-form-item').length;
                    const newTotal = initialForms + newPhotos;

                    totalForms.value = newTotal;
                }
            });
        }

        // Modern Photo Upload System
        const uploadArea = document.getElementById('upload-area');
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const browseFiles = document.getElementById('browse-files');
        const newPhotosContainer = document.getElementById('new-photos');
        const photoFormset = document.querySelector('.photo-formset');
        const totalForms = document.querySelector('input[name$="-TOTAL_FORMS"]');

        let dragCounter = 0;

        // Browse files click
        browseFiles.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        // Drag and drop events
        uploadArea.addEventListener('dragenter', (e) => {
            e.preventDefault();
            dragCounter++;
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dragCounter--;
            if (dragCounter === 0) {
                uploadArea.classList.remove('dragover');
            }
        });

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            dragCounter = 0;
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        // Handle files function
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    if (file.size <= 5 * 1024 * 1024) { // 5MB limit
                        addPhotoPreview(file);
                    } else {
                        alert('Dosya boyutu 5MB\'dan küçük olmalıdır: ' + file.name);
                    }
                } else {
                    alert('Sadece resim dosyaları yüklenebilir: ' + file.name);
                }
            });
        }

        // Add photo preview with AJAX upload
        function addPhotoPreview(file) {
            const formCount = parseInt(totalForms.value);

            // Create photo item
            const photoItem = document.createElement('div');
            photoItem.className = 'photo-item loading';
            photoItem.dataset.formIndex = formCount;

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                photoItem.innerHTML = `
                    <div class="photo-preview">
                        <img src="${e.target.result}" alt="Yeni fotoğraf">
                        <div class="photo-overlay">
                            <button type="button" class="btn-remove" data-form-index="${formCount}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="upload-progress">
                        <div class="upload-progress-bar"></div>
                    </div>
                `;

                // Upload file via AJAX
                uploadFileAjax(file, photoItem, formCount);
            };
            reader.readAsDataURL(file);

            newPhotosContainer.appendChild(photoItem);

            // Update total forms
            totalForms.value = formCount + 1;
        }

        // Upload file via AJAX
        function uploadFileAjax(file, photoItem, formCount) {
            const formData = new FormData();
            formData.append('fotograf', file);

            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            const progressBar = photoItem.querySelector('.upload-progress-bar');

            // Build upload URL
            const pathParts = window.location.pathname.split('/');
            let uploadUrl;

            // Check if we're on edit page or add page
            if (pathParts.includes('duzenle')) {
                // Edit page: /calismalar/calisma/slug/duzenle/
                const workSlug = pathParts[3];
                uploadUrl = `/calismalar/calisma/${workSlug}/fotograf-ekle/`;
            } else {
                // Add page: /calismalar/calisma/ekle/ - can't upload until saved
                alert('Çalışmayı önce kaydetmeniz gerekiyor. Lütfen formu doldurup "Kaydet" butonuna tıklayın.');
                return;
            }

            fetch(uploadUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove loading state
                    photoItem.classList.remove('loading');
                    progressBar.style.width = '100%';

                    // Update photo item with server data
                    photoItem.dataset.imageId = data.image.id;

                    // Create hidden form for deletion
                    createHiddenFormForExisting(formCount, data.image.id);
                } else {
                    // Show error
                    photoItem.innerHTML = `
                        <div class="photo-preview" style="background: #f8d7da; color: #721c24; padding: 1rem; text-align: center;">
                            <i class="bi bi-exclamation-triangle"></i>
                            <div>Hata: ${data.error}</div>
                            <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="this.closest('.photo-item').remove()">Kaldır</button>
                        </div>
                    `;
                }
            })
            .catch(error => {
                photoItem.innerHTML = `
                    <div class="photo-preview" style="background: #f8d7da; color: #721c24; padding: 1rem; text-align: center;">
                        <i class="bi bi-exclamation-triangle"></i>
                        <div>Yükleme hatası</div>
                        <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="this.closest('.photo-item').remove()">Kaldır</button>
                    </div>
                `;
            });
        }

        // Create hidden form for existing image (after AJAX upload)
        function createHiddenFormForExisting(formCount, imageId) {
            const formItem = document.createElement('div');
            formItem.className = 'photo-form-item';
            formItem.dataset.formIndex = formCount;
            formItem.dataset.imageId = imageId;

            formItem.innerHTML = `
                <input type="hidden" name="fotograflar-${formCount}-id" id="id_fotograflar-${formCount}-id" value="${imageId}">
                <input type="hidden" name="fotograflar-${formCount}-fotograf" value="">
                <input type="checkbox" name="fotograflar-${formCount}-DELETE" class="form__check-input" id="id_fotograflar-${formCount}-DELETE">
            `;

            photoFormset.appendChild(formItem);
        }

        // Remove photo
        function removePhoto(formIndex) {
            const photoItem = newPhotosContainer.querySelector(`[data-form-index="${formIndex}"]`);
            const formItem = photoFormset.querySelector(`[data-form-index="${formIndex}"]`);

            if (photoItem && photoItem.dataset.imageId) {
                // If image was uploaded to server, mark for deletion
                if (formItem) {
                    const deleteInput = formItem.querySelector('input[name$="-DELETE"]');
                    if (deleteInput) {
                        deleteInput.checked = true;
                        formItem.style.display = 'none';
                    }
                }

                // Update UI to show deletion
                photoItem.style.opacity = '0.5';
                photoItem.style.pointerEvents = 'none';
                const removeBtn = photoItem.querySelector('.btn-remove');
                if (removeBtn) {
                    removeBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                    removeBtn.onclick = () => {
                        // Undo deletion
                        if (formItem) {
                            const deleteInput = formItem.querySelector('input[name$="-DELETE"]');
                            if (deleteInput) {
                                deleteInput.checked = false;
                                formItem.style.display = 'block';
                            }
                        }
                        photoItem.style.opacity = '1';
                        photoItem.style.pointerEvents = 'auto';
                        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
                        removeBtn.onclick = () => removePhoto(formIndex);
                    };
                }
            } else {
                // If image wasn't uploaded yet, just remove from UI
                if (photoItem) {
                    photoItem.remove();
                }
                if (formItem) {
                    formItem.remove();
                }
            }
        }

        // Handle existing photo removal
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-remove')) {
                const btn = e.target.closest('.btn-remove');
                const formIndex = btn.dataset.formIndex;

                // For existing photos
                const photoItem = btn.closest('.photo-item');

                if (photoItem && photoItem.parentElement.id === 'existing-photos') {
                    const deleteInput = document.querySelector(`input[name="fotograflar-${formIndex}-DELETE"]`);

                    if (deleteInput) {
                        if (!deleteInput.checked) {
                            // Mark for deletion
                            deleteInput.checked = true;
                            photoItem.style.opacity = '0.5';
                            photoItem.style.filter = 'grayscale(100%)';
                            btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                            btn.title = 'Silmeyi geri al';
                            btn.style.background = '#28a745'; // Green for undo
                        } else {
                            // Undo deletion
                            deleteInput.checked = false;
                            photoItem.style.opacity = '1';
                            photoItem.style.filter = 'none';
                            btn.innerHTML = '<i class="bi bi-trash"></i>';
                            btn.title = 'Fotoğrafı sil';
                            btn.style.background = '#dc3545'; // Red for delete
                        }
                    }
                }
                // For new photos (uploaded via AJAX)
                else if (photoItem && photoItem.parentElement.id === 'new-photos') {
                    removePhoto(formIndex);
                }
            }
        });
    });
</script>
{% endblock %}