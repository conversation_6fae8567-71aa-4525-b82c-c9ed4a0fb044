{% extends 'base.html' %}

{% load static %}

{% block title %}Çalışmalar - Küp Cadısı{% endblock %}

{% block content %}
<main class="modern-section py-5">
    <div class="container modern-container">
        <!-- CSRF Token for AJAX requests -->
        {% csrf_token %}

        <!-- Ba<PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-header mb-5 text-center">
            <h1 class="gradient-heading display-4 fw-bold mb-4 wow fadeInUp" data-wow-delay="0.1s">Öğrenci Çalışmaları</h1>
            <p class="lead text-muted mb-3 wow fadeInUp" data-wow-delay="0.2s">Küp Cadısı atölyesinde öğrencilerimiz tarafından yapılan özgün çalışmaları keşfedin</p>
            <div class="d-flex align-items-center justify-content-center flex-wrap wow fadeInUp" data-wow-delay="0.3s">
                <div class="me-4 d-flex align-items-center mb-2">
                    <i class="bi bi-palette-fill me-2" style="color: var(--color-brown); font-size: 1.2rem;"></i>
                    <span>Yaratıcı Eserler</span>
                </div>
                <div class="me-4 d-flex align-items-center mb-2">
                    <i class="bi bi-people-fill me-2" style="color: var(--color-moss); font-size: 1.2rem;"></i>
                    <span>Yetenekli Öğrenciler</span>
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="bi bi-stars me-2" style="color: var(--color-navy); font-size: 1.2rem;"></i>
                    <span>İlham Verici Tasarımlar</span>
                </div>
            </div>
        </div>

        <!-- Öne Çıkan Çalışmalar Slider -->
        {% if one_cikan_calismalar %}
        <div class="featured-works-slider mb-5 wow fadeIn" data-wow-delay="0.3s">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <h2 class="slider-title mb-4"><i class="bi bi-award"></i>Öne Çıkan Çalışmalar</h2>
                        <p class="text-muted mb-5">En çok beğenilen ve görüntülenen çalışmalarımızı keşfedin</p>
                    </div>
                </div>

                <div class="swiper featured-slider">
                    <div class="swiper-wrapper">
                        {% for calisma in one_cikan_calismalar %}
                        <div class="swiper-slide">
                            <div class="featured-slide">
                                <div class="featured-image">
                                    {% if calisma.fotograflar.exists %}
                                    <img src="{{ calisma.fotograflar.all.0.fotograf.url }}" alt="{{ calisma.baslik }}" class="slide-image">
                                    {% else %}
                                    <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok" class="slide-image">
                                    {% endif %}
                                    <div class="featured-overlay">
                                        <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="btn-modern btn-primary-modern btn-icon-modern">
                                            <i class="bi bi-eye"></i>
                                            <span>İncele</span>
                                        </a>
                                    </div>
                                </div>
                                <div class="featured-content">
                                    <h3 class="featured-title">{{ calisma.baslik }}</h3>
                                    <p class="featured-author">
                                        <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="d-flex align-items-center text-decoration-none">
                                            {% if calisma.user.profil.profil_fotografi %}
                                            <img src="{{ calisma.user.profil.profil_fotografi.url }}" alt="{{ calisma.user.username }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                            {% else %}
                                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                            {% endif %}
                                            <span>{{ calisma.user.get_full_name|default:calisma.user.username }}</span>
                                        </a>
                                    </p>
                                    <div class="featured-meta">
                                        <span><i class="bi bi-heart me-1"></i>{{ calisma.begeni_sayisi }}</span>
                                        <span><i class="bi bi-eye me-1"></i>{{ calisma.goruntuleme_sayisi }}</span>
                                        {% if calisma.kategori %}
                                        <span><i class="bi bi-tag me-1"></i>{{ calisma.kategori.ad }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        <!-- Eğer çalışma sayısı az ise, aynı çalışmaları tekrar ekle (slider'ın düzgün çalışması için) -->
                        {% if one_cikan_calismalar|length < 3 %}
                            {% for calisma in one_cikan_calismalar %}
                            <div class="swiper-slide">
                                <div class="featured-slide">
                                    <div class="featured-image">
                                        {% if calisma.fotograflar.exists %}
                                        <img src="{{ calisma.fotograflar.all.0.fotograf.url }}" alt="{{ calisma.baslik }}" class="slide-image">
                                        {% else %}
                                        <img src="{% static 'img/placeholder.jpg' %}" alt="Görsel Yok" class="slide-image">
                                        {% endif %}
                                        <div class="featured-overlay">
                                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="btn-modern btn-primary-modern btn-icon-modern">
                                                <i class="bi bi-eye"></i>
                                                <span>İncele</span>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="featured-content">
                                        <h3 class="featured-title">{{ calisma.baslik }}</h3>
                                        <p class="featured-author">
                                            <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="d-flex align-items-center text-decoration-none">
                                                {% if calisma.user.profil.profil_fotografi %}
                                                <img src="{{ calisma.user.profil.profil_fotografi.url }}" alt="{{ calisma.user.username }}" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                {% else %}
                                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                    <i class="bi bi-person-fill"></i>
                                                </div>
                                                {% endif %}
                                                <span>{{ calisma.user.get_full_name|default:calisma.user.username }}</span>
                                            </a>
                                        </p>
                                        <div class="featured-meta">
                                            <span><i class="bi bi-heart me-1"></i>{{ calisma.begeni_sayisi }}</span>
                                            <span><i class="bi bi-eye me-1"></i>{{ calisma.goruntuleme_sayisi }}</span>
                                            {% if calisma.kategori %}
                                            <span><i class="bi bi-tag me-1"></i>{{ calisma.kategori.ad }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Filtreler ve Arama Bölümleri (Yan Yana) -->
        <div class="row mb-4">
            <!-- Kategori Filtreleri -->
            <div class="col-md-4 mb-3 mb-md-0">
                <div class="filter-section h-100">
                    <h4 class="filter-title"><i class="bi bi-grid"></i>Kategoriler</h4>
                    <div class="modern-tag-cloud">
                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-tag {% if not kategori and not etiket %}active{% endif %}">Tümü</a>
                        {% for kat in kategoriler %}
                        <a href="{% url 'calismalar:kategori_calismalari' kat.slug %}" class="modern-tag {% if kategori.slug == kat.slug %}active{% endif %}">{{ kat.ad }}</a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Etiket Filtreleri -->
            <div class="col-md-4 mb-3 mb-md-0">
                <div class="filter-section h-100">
                    <h4 class="filter-title"><i class="bi bi-tags"></i>Etiketler</h4>
                    <div class="modern-tag-cloud">
                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-tag {% if not current_tag %}active{% endif %}">Tümü</a>
                        {% for et in etiketler %}
                        <a href="{% url 'calismalar:calisma_listesi' %}{% if kategori %}{{ kategori.slug }}/{% endif %}?etiket={{ et.slug }}" class="modern-tag {% if current_tag == et.slug %}active{% endif %}">{{ et.name }}</a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Arama Formu -->
            <div class="col-md-4">
                <div class="filter-section h-100">
                    <h4 class="filter-title"><i class="bi bi-search"></i>Çalışma Ara</h4>
                    <form action="{% url 'calismalar:calisma_arama' %}" method="get" class="form-modern">
                        <div class="input-group-modern">
                            <input type="text" name="q" class="form-control-modern"
                                   placeholder="Çalışma adı, açıklama veya etiket..."
                                   value="{{ request.GET.q|default:'' }}" aria-label="Çalışma ara">
                            <button type="submit" class="btn-modern btn-primary-modern" aria-label="Ara">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        {% if current_tag %}
                        <input type="hidden" name="tag" value="{{ current_tag }}">
                        {% endif %}
                    </form>
                    
                    
                </div>
            </div>
        </div>

        {% if request.GET.q %}
        <!-- Arama Sonuçları Başlığı -->
        <div class="alert mb-4 wow fadeIn" data-wow-delay="0.2s" style="background-color: rgba(var(--color-brown-rgb), 0.1); border-left: 4px solid var(--color-brown); border-radius: 0 8px 8px 0; padding: 15px 20px;">
            <div class="d-flex align-items-center">
                <i class="bi bi-search me-3 fs-4"></i>
                <div>
                    <h5 class="mb-1">"{{ request.GET.q }}" için arama sonuçları</h5>
                    <p class="mb-0">{{ page_obj.paginator.count }} sonuç bulundu</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Çalışma Listesi -->
        <div class="row g-4 product-grid">
            {% for calisma in page_obj %}
            <div class="col-lg-4 col-md-6 product-item filter-{{ calisma.kategori.slug|default:'genel' }} wow fadeInUp" data-wow-delay="0.3s">
                <div class="modern-card h-100">
                    <div class="image-container">
                        <a href="{% url 'calismalar:calisma_detay' calisma.slug %}">
                            {% if calisma.fotograflar.exists %}
                            <img src="{{ calisma.fotograflar.all.0.fotograf.url }}" alt="{{ calisma.baslik }}" class="modern-image">
                            {% else %}
                            <div class="no-image-placeholder d-flex align-items-center justify-content-center">
                                <i class="bi bi-image fs-1 text-muted"></i>
                            </div>
                            {% endif %}
                        </a>
                        {% if calisma.kategori %}
                        <span class="modern-badge-corner">{{ calisma.kategori.ad }}</span>
                        {% endif %}
                        <div class="image-overlay">
                            <div class="d-flex gap-2 justify-content-center">
                                {% if calisma.fotograflar.exists %}
                                <a href="{{ calisma.fotograflar.all.0.fotograf.url }}" class="modern-btn-circle" title="{{ calisma.baslik }}" data-gallery="portfolio-gallery-{{ calisma.kategori.slug|default:'genel' }}">
                                    <i class="bi bi-zoom-in"></i>
                                </a>
                                {% endif %}
                                <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="modern-btn-circle modern-btn-link">
                                    <i class="bi bi-link-45deg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <h3 class="modern-card-title">
                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="text-decoration-none text-inherit">{{ calisma.baslik }}</a>
                        </h3>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <span class="modern-badge">{{ calisma.kategori.ad|default:"Genel" }}</span>
                            <div class="d-flex gap-2">
                                <span class="text-muted small"><i class="bi bi-eye me-1"></i>{{ calisma.goruntuleme_sayisi }}</span>
                                <span class="text-muted small"><i class="bi bi-heart me-1"></i>{{ calisma.begeni_sayisi }}</span>
                            </div>
                        </div>

                        <!-- Öğrenci Bilgisi -->
                        {% if calisma.user %}
                        <div class="mt-3">
                            <div class="d-flex align-items-center">
                                {% if calisma.user.profil.profil_fotografi %}
                                <img src="{{ calisma.user.profil.profil_fotografi.url }}" alt="{{ calisma.user.username }}" class="rounded-circle me-2 user-avatar">
                                {% else %}
                                <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2 user-avatar">
                                    <i class="bi bi-person-fill"></i>
                                </div>
                                {% endif %}
                                <span class="text-muted small user-name">{{ calisma.user.get_full_name|default:calisma.user.username }}</span>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Etiketler -->
                        {% if calisma.tags.all %}
                        <div class="mt-3">
                            <div class="modern-tag-cloud">
                                {% for tag in calisma.tags.all %}
                                <a href="{% url 'calismalar:calisma_listesi' %}?etiket={{ tag.slug }}" class="modern-tag">{{ tag.ad }}</a>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="modern-empty-state wow fadeIn" data-wow-delay="0.3s">
                    {% if request.GET.q or request.GET.kategori or request.GET.etiket %}
                        <!-- Arama/Filtreleme sonucu boş -->
                        <i class="bi bi-search display-1 text-muted mb-4"></i>
                        <h3 class="mt-4">Çalışma Bulunamadı</h3>
                        <p class="text-muted mb-4">Aradığınız kriterlere uygun çalışma bulunamadı.</p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{% url 'calismalar:calisma_listesi' %}" class="btn-modern btn-outline-modern btn-icon-modern">
                                <i class="bi bi-arrow-left"></i>
                                <span>Tüm Çalışmaları Göster</span>
                            </a>
                            {% if user.is_authenticated and user.is_staff %}
                            <a href="{% url 'calismalar:calisma_ekle' %}" class="btn-modern btn-primary-modern btn-icon-modern">
                                <i class="bi bi-plus-circle"></i>
                                <span>Yeni Çalışma Ekle</span>
                            </a>
                            {% endif %}
                        </div>
                    {% else %}
                        <!-- Hiç çalışma yok -->
                        <i class="bi bi-palette display-1 text-muted mb-4"></i>
                        <h3 class="mt-4">Henüz Çalışma Eklenmemiş</h3>
                        <p class="text-muted mb-4">Portfolyonuzda görüntülenecek çalışma bulunmuyor. İlk çalışmanızı ekleyerek başlayın!</p>
                        {% if user.is_authenticated and user.is_staff %}
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{% url 'calismalar:calisma_ekle' %}" class="btn-modern btn-primary-modern btn-icon-modern">
                                <i class="bi bi-plus-circle"></i>
                                <span>İlk Çalışmayı Ekle</span>
                            </a>
                            <a href="{% url 'admin:calismalar_calisma_changelist' %}" class="btn-modern btn-outline-modern btn-icon-modern">
                                <i class="bi bi-gear"></i>
                                <span>Çalışma Yönetimi</span>
                            </a>
                        </div>
                        {% else %}
                        <p class="text-muted small">Çalışma eklemek için admin paneline giriş yapın.</p>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Sayfalama -->
        {% if page_obj.has_other_pages %}
        <div class="modern-pagination wow fadeInUp" data-wow-delay="0.4s">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?sayfa={{ page_obj.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                </li>
                {% endif %}

                {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?sayfa={{ i }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?sayfa={{ page_obj.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                </li>
                {% endif %}
            </ul>
        </div>
        {% endif %}
    </div>
</main>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}">
<link rel="stylesheet" href="{% static 'calismalar/css/calisma_liste.css' %}">
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://unpkg.com/isotope-layout@3/dist/isotope.pkgd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" />
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Isotope başlatma
        var $grid = $('.product-grid').isotope({
            itemSelector: '.product-item',
            layoutMode: 'fitRows'
        });

        // GLightbox başlatma - sadece zoom butonları için
        const lightbox = GLightbox({
            selector: '.modern-btn-circle[data-gallery]',
            touchNavigation: true,
            loop: true,
            autoplayVideos: true
        });

        // Link butonlarının normal davranmasını sağla
        $('.modern-btn-link').on('click', function(e) {
            e.stopPropagation();
            // Normal link davranışı devam etsin
        });

        // Öne çıkan çalışmalar slider'ını başlat
        const featuredSlider = new Swiper('.featured-slider', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            loopAdditionalSlides: 3,
            speed: 800,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                // 768px ve üzeri ekranlarda 2 slide göster
                768: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                // 1024px ve üzeri ekranlarda 3 slide göster
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
            },
            effect: 'slide',
            grabCursor: true,
            parallax: true,
            on: {
                init: function() {
                    console.log('Slider başlatıldı');

                    // Slider'daki tüm slide'ları seç
                    const slides = document.querySelectorAll('.featured-slide');

                    // Her slide'a animasyon sınıfı ekle
                    slides.forEach(function(slide) {
                        slide.classList.add('animated');
                    });
                }
            }
        });

        // Slider'ı manuel olarak başlat
        if (featuredSlider) {
            featuredSlider.autoplay.start();
            console.log('Autoplay başlatıldı');
        }

        // Slider'a hover olduğunda autoplay'i durdur, hover'dan çıkınca devam ettir
        const sliderContainer = document.querySelector('.featured-slider');
        if (sliderContainer) {
            sliderContainer.addEventListener('mouseenter', function() {
                featuredSlider.autoplay.stop();
            });

            sliderContainer.addEventListener('mouseleave', function() {
                featuredSlider.autoplay.start();
            });
        }
    });
</script>
{% endblock %}