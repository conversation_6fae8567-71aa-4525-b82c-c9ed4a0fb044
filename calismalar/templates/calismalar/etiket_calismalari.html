{% extends 'calismalar/calisma_listesi.html' %}
{% load static %}

{% block title %}{{ etiket.ad }} Etiketli Çalışmalar - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'calismalar/css/calisma_liste.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">"{{ etiket.ad }}" Etiketli Çalışmalar</h1>
            <p class="modern-subtitle">Bu etikete sahip çalışmaları keşfedin</p>
        </div>

        <!-- Filtreler -->
        <div class="modern-card mb-4 wow fadeInUp" data-wow-delay="0.3s">
            <div class="modern-card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h3 class="mb-3"><i class="bi bi-filter me-2"></i>Kategoriler</h3>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-badge">
                                <i class="bi bi-grid me-1"></i>Tümü
                            </a>
                            {% for kat in kategoriler %}
                            <a href="{% url 'calismalar:kategori_calismalari' kat.slug %}" class="modern-badge">
                                <i class="bi bi-tag me-1"></i>{{ kat.ad }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h3 class="mb-3"><i class="bi bi-search me-2"></i>Arama</h3>
                        <form action="{% url 'calismalar:calisma_arama' %}" method="get" class="search-form">
                            <div class="input-group">
                                <input type="text" name="q" class="form-control modern-form-control" placeholder="Çalışma ara..." value="{{ request.GET.q|default:'' }}">
                                <button type="submit" class="modern-btn modern-btn-sm">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Etiket Bilgisi -->
        <div class="modern-card mb-4 wow fadeInUp" data-wow-delay="0.3s">
            <div class="modern-card-header">
                <h3><i class="bi bi-tag-fill me-2"></i>Etiket Bilgisi</h3>
            </div>
            <div class="modern-card-body">
                <div class="info-item">
                    <div class="info-icon">
                        <i class="bi bi-tag"></i>
                    </div>
                    <div class="info-content">
                        <span class="info-title">Etiket Adı</span>
                        <span class="info-value">{{ etiket.ad }}</span>
                    </div>
                </div>
                
                {% if etiket.aciklama %}
                <div class="info-item">
                    <div class="info-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <div class="info-content">
                        <span class="info-title">Açıklama</span>
                        <span class="info-value">{{ etiket.aciklama }}</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Çalışma Listesi -->
        <div class="row g-4">
            {% for calisma in calismalar %}
            <div class="col-md-6 col-lg-4 wow fadeInUp" data-wow-delay="{{ forloop.counter|divisibleby:3|yesno:'0.6,0.4,0.2' }}s">
                <div class="modern-card h-100">
                    <div class="modern-card-header">
                        <h3 class="fs-5">{{ calisma.baslik }}</h3>
                    </div>
                    <div class="position-relative overflow-hidden calisma-card-image-container">
                        <a href="{% url 'calismalar:calisma_detay' calisma.slug %}">
                            {% if calisma.fotograflar.exists %}
                            <img src="{{ calisma.fotograflar.first.fotograf.url }}" alt="{{ calisma.baslik }}" class="w-100 h-100 object-fit-cover transition-all">
                            {% else %}
                            <img src="{% static 'assets/img/cover-default.jpg' %}" alt="{{ calisma.baslik }}" class="w-100 h-100 object-fit-cover transition-all">
                            {% endif %}
                        </a>
                    </div>
                    <div class="modern-card-body">
                        <p class="text-muted mb-3">{{ calisma.aciklama|truncatechars:120 }}</p>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="bi bi-calendar3"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-title">Tarih</span>
                                <span class="info-value">{{ calisma.olusturma_tarihi|date:"d.m.Y" }}</span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            <div class="info-content">
                                <span class="info-title">Ekleyen</span>
                                <span class="info-value">
                                    <a href="{% url 'uyelik:kullanici_profil' calisma.user.username %}" class="modern-link">
                                        {{ calisma.user.username }}
                                    </a>
                                </span>
                            </div>
                        </div>
                        
                        <div class="text-end mt-3">
                            <a href="{% url 'calismalar:calisma_detay' calisma.slug %}" class="modern-btn modern-btn-sm">Detayları Gör</a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center py-5">
                <div class="modern-card wow fadeIn" data-wow-delay="0.3s">
                    <div class="modern-card-body text-center py-5">
                        <i class="bi bi-search display-1 text-muted mb-3"></i>
                        <h3 class="modern-title mt-4">Çalışma Bulunamadı</h3>
                        <p class="modern-subtitle">Bu etikette henüz çalışma bulunmuyor.</p>
                        <a href="{% url 'calismalar:calisma_listesi' %}" class="modern-btn mt-3">
                            <i class="bi bi-arrow-left me-2"></i>Tüm Çalışmaları Göster
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Sayfalama -->
        {% if calismalar.has_other_pages %}
        <div class="modern-card mt-5 wow fadeInUp" data-wow-delay="0.4s">
            <div class="modern-card-body">
                <h3 class="text-center mb-3"><i class="bi bi-book me-2"></i>Sayfalar</h3>
                <nav aria-label="Sayfalama">
                    <ul class="pagination justify-content-center">
                        {% if calismalar.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ calismalar.previous_page_number }}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link"><i class="bi bi-chevron-left"></i></span>
                        </li>
                        {% endif %}

                        {% for i in calismalar.paginator.page_range %}
                            {% if calismalar.number == i %}
                            <li class="page-item active">
                                <span class="page-link">{{ i }}</span>
                            </li>
                            {% elif i > calismalar.number|add:'-3' and i < calismalar.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if calismalar.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ calismalar.next_page_number }}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link"><i class="bi bi-chevron-right"></i></span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}