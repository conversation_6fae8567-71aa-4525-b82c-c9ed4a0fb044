from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.http import JsonResponse, HttpResponseForbidden, HttpResponseRedirect
from django.contrib import messages
from django.views.decorators.http import require_POST, require_http_methods
from django.core.paginator import Paginator
from django.db.models import Count, Q
from django.views.decorators.cache import cache_page
from django.contrib.auth.models import Group
from django.conf import settings

from .models import Calisma, CalismaFotograf
from urunler.models import Category
from .forms import CalismaForm, CalismaFotografForm, KategoriForm
from taggit.models import Tag
from .permissions import check_calisma_permission, setup_calisma_permissions
from utils.logging import get_logger

# Logger'ı başlat
logger = get_logger(__name__)

def _save_fotograf_with_validation(fotograf, calisma, sira, user, operation_prefix):
    """
    Fotoğraf dosyasını validasyon ile birlikte kaydeder.

    Args:
        fotograf: CalismaFotograf instance
        calisma: İlişkili Calisma instance
        sira: Fotoğraf sıra numarası
        user: İşlemi yapan kullanıcı
        operation_prefix: Logging için operation prefix'i

    Returns:
        bool: Başarılı ise True, değilse False
    """
    if hasattr(fotograf, 'fotograf') and fotograf.fotograf:
        try:
            # Dosya boyutu kontrolü
            if hasattr(fotograf.fotograf, 'size'):
                max_size = getattr(settings, 'MAX_UPLOAD_SIZE', 10 * 1024 * 1024)  # 10MB
                if fotograf.fotograf.size > max_size:
                    max_size_mb = max_size / (1024 * 1024)
                    logger.warning(f"Fotoğraf dosyası çok büyük: {fotograf.fotograf.size} bytes", extra={
                        'calisma_id': calisma.id,
                        'fotograf_sira': sira,
                        'file_size': fotograf.fotograf.size,
                        'max_size': max_size,
                        'max_size_mb': max_size_mb,
                        'user_id': user.id,
                        'operation': f'{operation_prefix}_file_too_large'
                    })
                    return False

            fotograf.save()
            return True

        except Exception as e:
            logger.error(f"Fotoğraf kaydedilirken hata: {e}", extra={
                'calisma_id': calisma.id,
                'fotograf_id': getattr(fotograf, 'id', 'None'),
                'fotograf_sira': sira,
                'user_id': user.id,
                'error': str(e),
                'operation': f'{operation_prefix}_fotograf_save_error'
            })
            return False
    else:

        return False

def calisma_listesi(request, admin_only=False):
    """
    Çalışmaları listeler.

    Args:
        admin_only (bool): True ise sadece yönetici çalışmalarını gösterir
    """
    # Filtreleme parametreleri
    kategori = request.GET.get('kategori')
    etiket = request.GET.get('etiket')
    arama = request.GET.get('q')

    # Ana sorgu - admin_only parametresine göre filtrele
    if admin_only:
        from django.contrib.auth.models import User
        admin_users = User.objects.filter(is_staff=True)
        calismalar = Calisma.objects.filter(user__in=admin_users)
        title = 'Yönetici Çalışmaları'
        logger.debug("Yönetici çalışmaları listeleniyor", extra={
            'admin_user_count': admin_users.count(),
            'operation': 'calisma_listesi_admin'
        })
    else:
        calismalar = Calisma.objects.all()
        title = 'Çalışmalar'
        logger.debug("Tüm çalışmalar listeleniyor", extra={
            'operation': 'calisma_listesi_all'
        })

    # Ortak optimizasyonlar
    calismalar = calismalar.select_related('user', 'kategori').prefetch_related('tags', 'fotograflar')

    # Filtreleme
    if kategori:
        calismalar = calismalar.filter(kategori__slug=kategori)
        logger.debug(f"Kategori filtresi uygulandı: {kategori}")
    if etiket:
        calismalar = calismalar.filter(tags__slug=etiket)
        logger.debug(f"Etiket filtresi uygulandı: {etiket}")
    if arama:
        calismalar = calismalar.filter(
            Q(baslik__icontains=arama) |
            Q(aciklama__icontains=arama) |
            Q(tags__name__icontains=arama)
        ).distinct()
        logger.debug(f"Arama filtresi uygulandı: {arama}")

    # Öne çıkan çalışmaları al - admin_only parametresine göre
    if admin_only:
        one_cikan_base_query = Calisma.objects.filter(user__in=admin_users)
    else:
        one_cikan_base_query = Calisma.objects.all()

    one_cikan_calismalar = one_cikan_base_query.select_related('user', 'kategori').prefetch_related('fotograflar').order_by('-begeni_sayisi')[:5]

    # Eğer yeterli beğeni alan çalışma yoksa, en son eklenen çalışmaları da ekle
    if one_cikan_calismalar.count() < 5:
        son_calismalar = one_cikan_base_query.select_related('user', 'kategori').prefetch_related('fotograflar').order_by('-olusturma_tarihi').exclude(id__in=one_cikan_calismalar.values_list('id', flat=True))[:5-one_cikan_calismalar.count()]
        one_cikan_calismalar = list(one_cikan_calismalar) + list(son_calismalar)

    # Sayfalama
    paginator = Paginator(calismalar, 12)
    page_number = request.GET.get('sayfa')
    page_obj = paginator.get_page(page_number)

    # Kategoriler ve etiketler
    kategoriler = Category.objects.annotate(calisma_sayisi=Count('calismalar'))
    etiketler = Tag.objects.annotate(calisma_sayisi=Count('taggit_taggeditem_items'))

    # Kullanıcının yönetici veya onaylanmış kullanıcı olup olmadığını kontrol et
    can_add_calisma = False
    if request.user.is_authenticated:
        can_add_calisma = request.user.is_superuser or (hasattr(request.user, 'profil') and request.user.profil.is_approved)

    return render(request, 'calismalar/calisma_listesi.html', {
        'page_obj': page_obj,
        'calismalar': page_obj,
        'kategoriler': kategoriler,
        'etiketler': etiketler,
        'title': title,
        'can_add_calisma': can_add_calisma,
        'one_cikan_calismalar': one_cikan_calismalar,
        'admin_only': admin_only,  # Template'de kullanılabilir
    })

def calisma_detay(request, slug):
    """Belirli bir çalışmanın detaylarını gösterir."""
    calisma = get_object_or_404(
        Calisma.objects.select_related('user', 'kategori')
        .prefetch_related('tags', 'fotograflar'),
        slug=slug
    )

    # Görüntülenme sayısını artır
    calisma.goruntulenme_sayisi += 1
    calisma.save(update_fields=['goruntulenme_sayisi'])

    # Benzer çalışmalar
    benzer_calismalar = Calisma.objects.filter(
        Q(kategori=calisma.kategori) | Q(tags__in=calisma.tags.all())
    ).exclude(id=calisma.id).distinct().order_by('-olusturma_tarihi')[:5]

    # Önceki ve sonraki çalışmalar
    onceki_calisma = Calisma.objects.filter(olusturma_tarihi__lt=calisma.olusturma_tarihi).order_by('-olusturma_tarihi').first()
    sonraki_calisma = Calisma.objects.filter(olusturma_tarihi__gt=calisma.olusturma_tarihi).order_by('olusturma_tarihi').first()

    # Kullanıcı beğenmiş mi kontrolü
    kullanici_begendi = False
    if request.user.is_authenticated:
        kullanici_begendi = calisma.kullanici_begendi_mi(request.user)

    # Sosyal medya hesaplarını al
    sosyal_medya = []

    try:
        # Çalışmayı oluşturan kullanıcının profil bilgilerini al
        profil = calisma.user.profil

        # Sosyal medya hesaplarını kontrol et ve ekle
        if profil.instagram:
            sosyal_medya.append({'platform': 'instagram', 'url': profil.instagram, 'icon': 'bi-instagram'})
        if profil.facebook:
            sosyal_medya.append({'platform': 'facebook', 'url': profil.facebook, 'icon': 'bi-facebook'})
        if profil.twitter:
            sosyal_medya.append({'platform': 'twitter', 'url': profil.twitter, 'icon': 'bi-twitter'})
        if profil.linkedin:
            sosyal_medya.append({'platform': 'linkedin', 'url': profil.linkedin, 'icon': 'bi-linkedin'})
        if profil.youtube:
            sosyal_medya.append({'platform': 'youtube', 'url': profil.youtube, 'icon': 'bi-youtube'})

        # Eğer kullanıcının sosyal medya hesabı yoksa, varsayılan paylaşım butonlarını ekle
        if not sosyal_medya:
            sosyal_medya = [
                {'platform': 'facebook', 'url': f"https://www.facebook.com/sharer/sharer.php?u={request.build_absolute_uri()}", 'icon': 'bi-facebook'},
                {'platform': 'twitter', 'url': f"https://twitter.com/intent/tweet?text={calisma.baslik}&url={request.build_absolute_uri()}", 'icon': 'bi-twitter'},
                {'platform': 'whatsapp', 'url': f'https://wa.me/?text={calisma.baslik} {request.build_absolute_uri()}', 'icon': 'bi-whatsapp'}
            ]

    except Exception as e:
        logger.warning(f"Sosyal medya hesapları alınırken hata: {str(e)}", extra={
            'calisma_id': calisma.id,
            'calisma_baslik': calisma.baslik,
            'user_id': calisma.user.id,
            'error': str(e),
            'operation': 'sosyal_medya_hesaplari'
        })
        # Varsayılan sosyal medya hesapları
        sosyal_medya = [
            {'platform': 'facebook', 'url': f'https://www.facebook.com/sharer/sharer.php?u={request.build_absolute_uri()}', 'icon': 'bi-facebook'},
            {'platform': 'twitter', 'url': f'https://twitter.com/intent/tweet?text={calisma.baslik}&url={request.build_absolute_uri()}', 'icon': 'bi-twitter'},
            {'platform': 'whatsapp', 'url': f'https://wa.me/?text={calisma.baslik} {request.build_absolute_uri()}', 'icon': 'bi-whatsapp'}
        ]

    return render(request, 'calismalar/calisma_detay.html', {
        'calisma': calisma,
        'benzer_calismalar': benzer_calismalar,
        'onceki_calisma': onceki_calisma,
        'sonraki_calisma': sonraki_calisma,
        'kullanici_begendi': kullanici_begendi,
        'sosyal_medya': sosyal_medya,
        'title': calisma.baslik,
    })

@login_required
def calisma_ekle(request):
    """Yeni bir çalışma ekler."""
    # Kullanıcının çalışma ekleme yetkisi olup olmadığını kontrol et
    if not (check_calisma_permission(request.user) or
            (hasattr(request.user, 'profil') and request.user.profil.is_approved)):
        messages.error(request, 'Çalışma eklemek için yönetici onayı veya Çalışma Yöneticisi yetkisi gereklidir.')
        return redirect('anasayfa:ana_sayfa')

    # Fotoğraf formset oluştur
    from django.forms import inlineformset_factory
    FotografFormset = inlineformset_factory(
        Calisma,
        CalismaFotograf,
        form=CalismaFotografForm,
        extra=1,
        can_delete=True
    )

    if request.method == 'POST':
        form = CalismaForm(request.POST)
        formset = FotografFormset(request.POST, request.FILES, prefix='fotograflar')

        if form.is_valid():
            calisma = form.save(commit=False)
            calisma.user = request.user
            calisma.save()
            form.save_m2m()

            # Formset'i kaydet
            if formset.is_valid():
                fotograflar = formset.save(commit=False)
                for i, fotograf in enumerate(fotograflar):
                    fotograf.calisma = calisma
                    fotograf.sira = i
                    # Fotoğraf dosyasını validasyon ile kaydet
                    _save_fotograf_with_validation(fotograf, calisma, i, request.user, 'calisma_ekle')

                # Silinen fotoğrafları işle
                for obj in formset.deleted_objects:
                    obj.delete()

                # Formset'i kaydet
                formset.save_m2m()

            # Çalışma Yöneticileri grubunu kur ve kullanıcıyı ekle
            group = setup_calisma_permissions()
            request.user.groups.add(group)

            messages.success(request, 'Çalışmanız başarıyla oluşturuldu.')
            return redirect('calismalar:calisma_detay', slug=calisma.slug)
    else:
        form = CalismaForm()
        formset = FotografFormset(prefix='fotograflar')

    return render(request, 'calismalar/calisma_form.html', {
        'form': form,
        'formset': formset,
        'title': 'Yeni Çalışma Ekle',
    })

@login_required
def calisma_duzenle(request, slug):
    """Var olan bir çalışmayı düzenler."""
    calisma = get_object_or_404(Calisma, slug=slug)

    if not check_calisma_permission(request.user, calisma):
        return HttpResponseForbidden("Bu çalışmayı düzenleme yetkiniz yok.")

    # Fotoğraf formset oluştur
    from django.forms import inlineformset_factory
    FotografFormset = inlineformset_factory(
        Calisma,
        CalismaFotograf,
        form=CalismaFotografForm,
        extra=1,
        can_delete=True
    )

    if request.method == 'POST':
        form = CalismaForm(request.POST, instance=calisma)
        formset = FotografFormset(request.POST, request.FILES, instance=calisma, prefix='fotograflar')

        if form.is_valid() and formset.is_valid():
            form.save()

            # Formset'i kaydet ve sıra numaralarını ayarla
            fotograflar = formset.save(commit=False)
            for i, fotograf in enumerate(fotograflar):
                fotograf.sira = i
                # Fotoğraf dosyasını validasyon ile kaydet
                _save_fotograf_with_validation(fotograf, calisma, i, request.user, 'calisma_duzenle')

            # Manuel DELETE kontrolü
            for formset_form in formset.forms:
                if formset_form.instance.pk:  # Sadece mevcut objeler için
                    delete_field_name = f"{formset_form.prefix}-DELETE"
                    is_marked_for_deletion = request.POST.get(delete_field_name) == 'on'

                    if is_marked_for_deletion:
                        formset_form.instance.delete()

            # Formset'in otomatik silme işlemi
            for obj in formset.deleted_objects:
                obj.delete()

            # Formset'i kaydet
            formset.save_m2m()
            messages.success(request, 'Çalışma bilgileri güncellendi.')
            return redirect('calismalar:calisma_detay', slug=calisma.slug)
    else:
        form = CalismaForm(instance=calisma)
        formset = FotografFormset(instance=calisma, prefix='fotograflar')

    return render(request, 'calismalar/calisma_form.html', {
        'form': form,
        'formset': formset,
        'calisma': calisma,
        'title': f'{calisma.baslik} - Düzenle',
    })

@login_required
@require_POST
def fotograf_ekle(request, slug):
    """Bir çalışmaya fotoğraf ekler. AJAX ve normal form isteklerini destekler."""
    calisma = get_object_or_404(Calisma, slug=slug)

    if not check_calisma_permission(request.user, calisma):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'Bu çalışmaya fotoğraf ekleme yetkiniz yok.'
            }, status=403)
        return HttpResponseForbidden("Bu çalışmaya fotoğraf ekleme yetkiniz yok.")

    # Dosya kontrolü
    if 'fotograf' not in request.FILES:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'Lütfen bir fotoğraf dosyası seçin.'
            }, status=400)
        messages.error(request, 'Lütfen bir fotoğraf dosyası seçin.')
        return redirect('calismalar:calisma_duzenle', slug=calisma.slug)

    # AJAX isteği için
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        uploaded_file = request.FILES['fotograf']

        # Dosya validasyonu
        if uploaded_file.size > 5 * 1024 * 1024:  # 5MB limit
            return JsonResponse({
                'success': False,
                'error': 'Dosya boyutu 5MB\'dan küçük olmalıdır.'
            }, status=400)

        if not uploaded_file.content_type.startswith('image/'):
            return JsonResponse({
                'success': False,
                'error': 'Sadece resim dosyaları yüklenebilir.'
            }, status=400)

        try:
            # Fotoğrafı kaydet
            fotograf = CalismaFotograf()
            fotograf.calisma = calisma
            fotograf.fotograf = uploaded_file

            from django.db.models import Max
            son_sira = calisma.fotograflar.aggregate(Max('sira'))['sira__max'] or 0
            fotograf.sira = son_sira + 1
            fotograf.save()

            return JsonResponse({
                'success': True,
                'message': 'Fotoğraf başarıyla yüklendi.',
                'image': {
                    'id': fotograf.id,
                    'url': fotograf.fotograf.url,
                    'name': uploaded_file.name
                }
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Fotoğraf yüklenirken bir hata oluştu: {str(e)}'
            }, status=500)

    # Normal form isteği için
    form = CalismaFotografForm(request.POST, request.FILES)
    if form.is_valid():
        fotograf = form.save(commit=False)
        fotograf.calisma = calisma
        from django.db.models import Max
        son_sira = calisma.fotograflar.aggregate(Max('sira'))['sira__max'] or 0
        fotograf.sira = son_sira + 1
        fotograf.save()
        messages.success(request, 'Fotoğraf başarıyla eklendi.')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                messages.error(request, f"{error}")

    # Eğer modal'dan geldiyse, detay sayfasına yönlendir
    if request.POST.get('from_modal'):
        return redirect('calismalar:calisma_detay', slug=calisma.slug)

    # Aksi takdirde düzenleme sayfasına yönlendir
    return redirect('calismalar:calisma_duzenle', slug=calisma.slug)

@login_required
def fotograf_sil(request, pk):
    """Bir çalışmadan fotoğraf siler."""
    fotograf = get_object_or_404(CalismaFotograf, pk=pk)
    calisma = fotograf.calisma

    if not check_calisma_permission(request.user, calisma):
        return HttpResponseForbidden("Bu fotoğrafı silme yetkiniz yok.")

    fotograf.delete()
    messages.success(request, 'Fotoğraf silindi.')

    return redirect('calismalar:calisma_duzenle', slug=calisma.slug)

@login_required
@require_POST
def fotograf_sirala(request):
    """Fotoğrafların sırasını günceller."""
    import json
    data = json.loads(request.body)
    fotograf_ids = data.get('fotograflar', [])

    if not fotograf_ids:
        return JsonResponse({'success': False, 'error': 'Fotoğraf listesi boş.'}, status=400)

    ilk_fotograf = get_object_or_404(CalismaFotograf, id=fotograf_ids[0])
    if not check_calisma_permission(request.user, ilk_fotograf.calisma):
        return JsonResponse({'success': False, 'error': 'Bu fotoğrafları sıralama yetkiniz yok.'}, status=403)

    for index, fotograf_id in enumerate(fotograf_ids):
        try:
            fotograf = CalismaFotograf.objects.get(id=fotograf_id)
            fotograf.sira = index
            fotograf.save(update_fields=['sira'])
        except CalismaFotograf.DoesNotExist:
            pass

    return JsonResponse({'success': True})

@login_required
def calisma_sil(request, slug):
    """Bir çalışmayı siler."""
    calisma = get_object_or_404(Calisma, slug=slug)

    if not check_calisma_permission(request.user, calisma):
        return HttpResponseForbidden("Bu çalışmayı silme yetkiniz yok.")

    # Silme işleminden sonra yönlendirilecek URL'yi belirle
    # Eğer çalışma kullanıcının kendi çalışmasıysa, kullanıcının çalışma listesine yönlendir
    # Aksi takdirde genel çalışma listesine yönlendir
    if request.method == 'POST':
        # Çalışmanın sahibini ve kategorisini silmeden önce kaydet
        user_id = calisma.user.id
        is_admin = calisma.user.is_staff or calisma.user.is_superuser
        kategori_slug = calisma.kategori.slug if calisma.kategori else None

        # Çalışmayı sil (Calisma.delete() metodu override edilmiş ve ilişkili fotoğrafları da siliyor)
        calisma.delete()

        messages.success(request, 'Çalışma başarıyla silindi.')

        # Yönlendirme mantığı
        if request.user.id == user_id:
            # Kendi çalışmasını silen kullanıcıyı kendi çalışmaları listesine yönlendir
            return redirect('uyelik:profil_calismalarim')
        elif is_admin and kategori_slug:
            # Yönetici, kategorili bir çalışmayı siliyorsa, o kategorinin listesine yönlendir
            return redirect('calismalar:calisma_listesi_kategori', kategori_slug=kategori_slug)
        elif is_admin:
            # Yönetici, kategorisiz bir çalışmayı siliyorsa, yönetici çalışmaları listesine yönlendir
            return redirect('calismalar:yonetici_calismalari')
        else:
            # Diğer durumlarda genel çalışma listesine yönlendir
            return redirect('calismalar:calisma_listesi')

    return render(request, 'calismalar/calisma_sil.html', {
        'calisma': calisma,
        'title': f'{calisma.baslik} - Sil',
    })

@login_required
@require_POST
def begeni_ekle(request, slug):
    """
    Bir çalışmaya beğeni ekler veya kaldırır (toggle).
    Artık veritabanı tabanlı sağlam beğeni sistemi kullanıyor.
    """
    calisma = get_object_or_404(Calisma, slug=slug)

    # Beğeni durumunu değiştir (toggle) - model metodunu kullan
    begendi, yeni_begeni_sayisi = calisma.begeni_toggle(request.user)

    # Mesaj belirle
    if begendi:
        action_message = 'Çalışmayı beğendiniz.'
    else:
        action_message = 'Beğeni kaldırıldı.'

    # AJAX isteği kontrolü
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if is_ajax:
        return JsonResponse({
            'success': True,
            'like_count': yeni_begeni_sayisi,
            'liked': begendi
        })
    else:
        messages.success(request, action_message)
        return redirect('calismalar:calisma_detay', slug=calisma.slug)

# Wrapper view'lar - geriye uyumluluk için

def tum_calismalar(request):
    """Tüm çalışmaları listeler - ana view'a yönlendirir."""
    return calisma_listesi(request, admin_only=False)

def yonetici_calismalari(request):
    """Sadece yönetici çalışmalarını listeler - ana view'a yönlendirir."""
    return calisma_listesi(request, admin_only=True)
