from django import forms
from django.core.exceptions import ValidationError
from django.conf import settings
from django.utils.html import strip_tags
import os
import re
from .models import Calisma, CalismaFotograf
from urunler.models import Category

def validate_file_size(value):
    """Dosya boyutunu kontrol eder"""
    max_size = settings.MAX_UPLOAD_SIZE
    if value.size > max_size:
        max_size_mb = max_size / (1024 * 1024)
        raise ValidationError(f'Dosya boyutu {int(max_size_mb)}MB\'dan büyük olamaz.')

def validate_file_type(value):
    """Dosya türünü kontrol eder (dosya uzantısı kontrolü)"""
    ext = os.path.splitext(value.name)[1].lower()
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    if ext not in allowed_extensions:
        raise ValidationError(f'Geçersiz dosya uzantısı. İzin verilen uzantılar: {", ".join(allowed_extensions)}')

class CalismaForm(forms.ModelForm):
    """Çalışma oluşturma ve düzenleme formu."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            tag_names = [tag.name for tag in self.instance.tags.all()]
            self.initial['tags'] = ', '.join(tag_names)

        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = ''
                    if isinstance(widget, forms.Textarea):
                        invalid_class = 'form__textarea--invalid'
                    elif isinstance(widget, forms.Select):
                        invalid_class = 'form__select--invalid'
                    else:
                        invalid_class = 'form__input--invalid'
                    
                    if invalid_class and invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

    class Meta:
        model = Calisma
        fields = ['baslik', 'aciklama', 'kategori', 'tags']
        widgets = {
            'baslik': forms.TextInput(attrs={'class': 'form__input'}),
            'aciklama': forms.Textarea(attrs={'class': 'form__textarea', 'rows': 5}),
            'kategori': forms.Select(attrs={'class': 'form__select'}),
            'tags': forms.TextInput(attrs={'class': 'form__input', 'placeholder': 'Etiketleri virgülle ayırarak girin'}),
        }

    def clean_baslik(self):
        baslik = self.cleaned_data.get('baslik', '').strip()
        if not baslik:
            raise ValidationError('Başlık alanı boş olamaz.')
        baslik = strip_tags(baslik)
        if len(baslik) > 200:
            raise ValidationError(f'Başlık 200 karakterden uzun olamaz.')
        if len(baslik) < 3:
            raise ValidationError(f'Başlık en az 3 karakter olmalıdır.')
        if not baslik.strip():
            raise ValidationError('Başlık sadece boşluk karakterlerinden oluşamaz.')
        dangerous_patterns = [r'javascript:', r'data:', r'vbscript:', r'on\w+\s*=']
        baslik_lower = baslik.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, baslik_lower):
                raise ValidationError('Başlık güvenlik açısından uygun olmayan karakterler içeriyor.')
        return baslik

    def clean_aciklama(self):
        aciklama = self.cleaned_data.get('aciklama', '').strip()
        if not aciklama:
            raise ValidationError('Açıklama alanı boş olamaz.')
        aciklama = strip_tags(aciklama)
        if len(aciklama) > 2000:
            raise ValidationError(f'Açıklama 2000 karakterden uzun olamaz.')
        if len(aciklama) < 10:
            raise ValidationError(f'Açıklama en az 10 karakter olmalıdır.')
        dangerous_patterns = [r'javascript:', r'data:', r'vbscript:', r'on\w+\s*=']
        aciklama_lower = aciklama.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, aciklama_lower):
                raise ValidationError('Açıklama güvenlik açısından uygun olmayan içerik barındırıyor.')
        return aciklama

class CalismaFotografForm(forms.ModelForm):
    """Çalışma fotoğrafı ekleme formu."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    if 'form__input--invalid' not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} form__input--invalid'.strip()

    class Meta:
        model = CalismaFotograf
        fields = ['fotograf', 'aciklama']
        widgets = {
            'fotograf': forms.FileInput(attrs={'class': 'form__input'}),
            'aciklama': forms.TextInput(attrs={'class': 'form__input', 'placeholder': 'Fotoğraf için kısa bir açıklama'}),
        }

    def clean_fotograf(self):
        fotograf = self.cleaned_data.get('fotograf')
        if fotograf and not isinstance(fotograf, str):
            validate_file_size(fotograf)
            try:
                validate_file_type(fotograf)
            except Exception as e:
                raise ValidationError(f"Dosya doğrulama hatası: {str(e)}")
        return fotograf

    def clean_aciklama(self):
        aciklama = self.cleaned_data.get('aciklama', '').strip()
        if aciklama:
            aciklama = strip_tags(aciklama)
            if len(aciklama) > 255:
                raise ValidationError('Fotoğraf açıklaması 255 karakterden uzun olamaz.')
        return aciklama

class KategoriForm(forms.ModelForm):
    """Kategori oluşturma ve düzenleme formu."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = 'form__textarea--invalid' if isinstance(widget, forms.Textarea) else 'form__input--invalid'
                    if invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

    class Meta:
        model = Category
        fields = ['ad', 'aciklama']
        widgets = {
            'ad': forms.TextInput(attrs={'class': 'form__input'}),
            'aciklama': forms.Textarea(attrs={'class': 'form__textarea', 'rows': 3}),
        }

    def clean_ad(self):
        ad = self.cleaned_data.get('ad', '').strip()
        if not ad:
            raise ValidationError('Kategori adı boş olamaz.')
        ad = strip_tags(ad)
        if len(ad) > 100:
            raise ValidationError('Kategori adı 100 karakterden uzun olamaz.')
        if len(ad) < 2:
            raise ValidationError('Kategori adı en az 2 karakter olmalıdır.')
        return ad

    def clean_aciklama(self):
        aciklama = self.cleaned_data.get('aciklama', '').strip()
        if aciklama:
            aciklama = strip_tags(aciklama)
            if len(aciklama) > 500:
                raise ValidationError('Kategori açıklaması 500 karakterden uzun olamaz.')
        return aciklama
