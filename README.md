# 🎲 Küp Cadısı

Django ile geliştirilmiş e-ticaret ve eğitim platformu

## 🎯 Proje Amacı
Rubik küp ve bulmaca tutkunları için özel olarak tasarlanmış, ürün satışı ve eğitim içerikleri sunan bir platform.

## ✨ Özellikler
- 🛒 **<PERSON>rün Yönetimi**
  - <PERSON>rün listeleme ve detay sayfaları
  - Kategorilere göre filtreleme
  - Ürün arama sistemi

- 🎥 **Video İçerikler**
  - Eğitim videoları
  - Çözüm teknikleri
  - İpuçları ve püf noktaları

- 👥 **Kullanıcı Profilleri**
  - Özelleştirilebilir profil sayfaları
  - Kullanıcı yetkilendirme sistemi
  - Favori ürünler listesi

- ✏️ **İçerik Yönetimi**
  - Blog yazıları
  - Ürün incelemeleri
  - Kullanıcı yorumları

## 🛠️ Teknolojiler
- **Back-end:** Django 4.2
- **Front-end:** Bootstrap 5, JavaScript
- **Veritabanı:** SQLite
- **Deployment:** Python 3.13

## 🚀 Kurulum

### Hızlı Kurulum (Makefile ile)
```bash
# Repository'yi klonla
git clone https://github.com/uzunecevit/kupcadisi.git

# Proje dizinine git
cd kupcadisi

# Geliştirme ortamını kur
make setup-dev

# Sunucuyu başlat
make runserver
```

### Manuel Kurulum
```bash
# Repository'yi klonla
git clone https://github.com/uzunecevit/kupcadisi.git

# Proje dizinine git
cd kupcadisi

# Virtual environment kur ve aktif et
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# Gereksinimleri yükle
pip install -r requirements.txt

# Ortam değişkenlerini ayarla
cp .env.example .env
# .env dosyasını düzenle

# Veritabanını oluştur
python manage.py migrate

# Süper kullanıcı oluştur
python manage.py createsuperuser

# Sunucuyu başlat
python manage.py runserver
```

## 🔧 Geliştirme

### Kullanılabilir Komutlar
```bash
make help          # Tüm komutları göster
make test           # Testleri çalıştır
make test-cov       # Test coverage raporu
make lint           # Kod kalitesi kontrolü
make format         # Kod formatını düzelt
make security       # Güvenlik kontrolü
```

### Test Çalıştırma
```bash
# Tüm testler
python manage.py test

# Belirli bir uygulama
python manage.py test uyelik

# Coverage raporu
coverage run --source='.' manage.py test
coverage report
coverage html
```

## 🔒 Güvenlik

### Production Ortamı İçin Önemli Notlar
1. **Ortam Değişkenleri**: `.env` dosyasını oluşturun ve güvenli değerler atayın
2. **SECRET_KEY**: Güçlü ve benzersiz bir secret key kullanın
3. **DEBUG**: Production'da mutlaka `DEBUG=False` yapın
4. **ALLOWED_HOSTS**: Geçerli domain'lerinizi tanımlayın
5. **HTTPS**: SSL sertifikası kullanın
6. **Database**: SQLite yerine PostgreSQL kullanın

### Güvenlik Kontrolleri
```bash
# Django güvenlik kontrolü
python manage.py check --deploy

# Kod güvenlik taraması
make security
```

## 📊 Proje Durumu

### Son Güncelleme: 2025-01-09
- ✅ Kritik güvenlik açıkları düzeltildi
- ✅ Kod kalitesi iyileştirildi
- ✅ Test altyapısı eklendi
- ✅ Logging sistemi kuruldu
- ✅ Geliştirme araçları eklendi

### Bilinen Sorunlar
- Test coverage düşük (%0 -> hedef %80)
- Bazı view'larda error handling eksik
- Performance optimizasyonu gerekli

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 👨‍💻 Geliştirici
- [Ecevit Uzun](https://github.com/uzunecevit)

## 📝 Lisans
Bu proje [MIT lisansı](LICENSE) altında lisanslanmıştır.
