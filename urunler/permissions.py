"""
<PERSON><PERSON><PERSON><PERSON><PERSON> uygulaması için yetkilendirme fonksiyonları ve decorator'ları.
"""

from django.http import HttpResponseForbidden, JsonResponse
from django.contrib import messages
from django.shortcuts import redirect
from functools import wraps
from utils.logging import get_logger

logger = get_logger(__name__)


def check_product_admin_permission(user):
    """
    Kullanıcının ürün yönetimi yetkisi olup olmadığını kontrol eder.

    Yeni granüler izin sistemi ile entegre edilmiştir.
    Şu kullanıcılar ürün yönetimi yapabilir:
    - Süper kullanıcılar
    - İçerik Editörü grubu üyeleri
    - Ürün Yöneticisi grubu üyeleri

    Args:
        user: Kontrol edilecek kullanıcı

    Returns:
        bool: Kullanıcının yetkisi varsa True, yoksa False
    """
    # <PERSON><PERSON><PERSON><PERSON><PERSON> giriş yapmış mı?
    if not user.is_authenticated:
        return False

    # <PERSON><PERSON><PERSON> kullanıcılar her zaman yetkilidir
    if user.is_superuser:
        return True

    # Granüler izin sistemi kontrolü
    from utils.permissions import check_permission

    # Ürün ekleme izni varsa yönetici sayılır
    return (check_permission(user, 'urunler.add_product') or
            check_permission(user, 'urunler.change_product') or
            check_permission(user, 'urunler.delete_product'))


def check_product_view_permission(user, product=None):
    """
    Kullanıcının ürün görüntüleme yetkisi olup olmadığını kontrol eder.
    
    Args:
        user: Kontrol edilecek kullanıcı
        product: İsteğe bağlı, belirli bir ürün için kontrol yapılacaksa
        
    Returns:
        bool: Kullanıcının yetkisi varsa True, yoksa False
    """
    # Ürün görüntüleme herkese açık (is_available=True olanlar için)
    if product:
        return product.is_available
    
    return True


def check_product_image_permission(user, product):
    """
    Kullanıcının ürün resmi ekleme/düzenleme yetkisi olup olmadığını kontrol eder.

    Yeni granüler izin sistemi ile entegre edilmiştir.

    Args:
        user: Kontrol edilecek kullanıcı
        product: Resim eklenecek/düzenlenecek ürün

    Returns:
        bool: Kullanıcının yetkisi varsa True, yoksa False
    """
    # Kullanıcı giriş yapmış mı?
    if not user.is_authenticated:
        return False

    # Süper kullanıcılar her zaman yetkili
    if user.is_superuser:
        return True

    # Granüler izin sistemi kontrolü
    from utils.permissions import check_permission

    # Ürün resmi yönetimi izni
    return (check_permission(user, 'urunler.add_productimage') or
            check_permission(user, 'urunler.change_productimage') or
            check_permission(user, 'urunler.delete_productimage'))


def require_product_admin(view_func):
    """
    Ürün yönetimi yetkisi gerektiren view'lar için decorator.
    
    Kullanım:
        @require_product_admin
        def urun_ekle(request):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not check_product_admin_permission(request.user):
            # Kullanıcı bilgilerini logla
            logger.warning("Yetkisiz ürün yönetimi erişimi", extra={
                'user_id': request.user.id if request.user.is_authenticated else None,
                'username': request.user.username if request.user.is_authenticated else 'Anonymous',
                'view_name': view_func.__name__,
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
                'operation': 'unauthorized_product_access'
            })
            
            # AJAX isteği mi?
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': 'Bu işlemi yapmaya yetkiniz yok. Ürün yönetimi sadece yöneticiler tarafından yapılabilir.'
                }, status=403)
            
            # Normal istek için
            if request.user.is_authenticated:
                messages.error(request, 'Bu işlemi yapmaya yetkiniz yok. Ürün yönetimi sadece yöneticiler tarafından yapılabilir.')
                return redirect('urunler:urun_listesi')
            else:
                messages.error(request, 'Bu işlemi yapmak için giriş yapmanız ve yönetici yetkisine sahip olmanız gerekir.')
                return redirect('uyelik:giris')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def require_product_image_permission(view_func):
    """
    Ürün resmi yönetimi yetkisi gerektiren view'lar için decorator.
    
    Kullanım:
        @require_product_image_permission
        def fotograf_ekle(request, id, slug):
            ...
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Ürün bilgisini al
        product_id = kwargs.get('id') or kwargs.get('product_id')
        if not product_id:
            return JsonResponse({'error': 'Geçersiz ürün bilgisi.'}, status=400)
        
        try:
            from .models import Product
            product = Product.objects.get(id=product_id)
        except Product.DoesNotExist:
            return JsonResponse({'error': 'Ürün bulunamadı.'}, status=404)
        
        if not check_product_image_permission(request.user, product):
            # Kullanıcı bilgilerini logla
            logger.warning("Yetkisiz ürün resmi erişimi", extra={
                'user_id': request.user.id if request.user.is_authenticated else None,
                'username': request.user.username if request.user.is_authenticated else 'Anonymous',
                'product_id': product.id,
                'product_name': product.name,
                'view_name': view_func.__name__,
                'operation': 'unauthorized_product_image_access'
            })
            
            return JsonResponse({
                'error': 'Bu işlemi yapmaya yetkiniz yok. Ürün resmi yönetimi sadece yöneticiler tarafından yapılabilir.'
            }, status=403)
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def get_user_product_permissions(user):
    """
    Kullanıcının ürün ile ilgili tüm yetkilerini döndürür.
    Template'lerde kullanım için.
    
    Args:
        user: Kontrol edilecek kullanıcı
        
    Returns:
        dict: Yetki bilgilerini içeren sözlük
    """
    return {
        'can_add_product': check_product_admin_permission(user),
        'can_edit_product': check_product_admin_permission(user),
        'can_delete_product': check_product_admin_permission(user),
        'can_manage_images': check_product_admin_permission(user),
        'is_admin': user.is_superuser if user.is_authenticated else False,
        'is_staff': user.is_staff if user.is_authenticated else False,
    }
