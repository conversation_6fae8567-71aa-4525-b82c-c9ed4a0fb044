/*
* ÜRÜNLER FORM STYLES CSS
* Ürün form sayfaları için <PERSON>zel stiller
* Ana tasarım sistemi ile uyumlu hale getirildi
*/

/* Ana tasarım sistemi değişkenlerini içe aktar */
@import url('../../../assets/css/_variables.css');

/* Ana form stilleri */
.urun-form-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0.5rem 1rem rgba(var(--color-brown-rgb), 0.15);
    overflow: hidden;
    background: var(--white);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.urun-form-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(var(--color-brown-rgb), 0.15);
}

.urun-form-header {
    background: linear-gradient(135deg, var(--color-brown) 0%, var(--color-navy) 100%);
    color: white;
    padding: 1.5rem;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    position: relative;
    overflow: hidden;
}

.urun-form-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.urun-form-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
}

.urun-form-header h2::after {
    display: none; /* Modern-theme.css'deki underline'ı kaldır */
}

.urun-form-header p {
    margin: 0.5rem 0 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

/* Resim galerisi */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.image-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(var(--color-brown-rgb), 0.075);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(var(--color-brown-rgb), 0.15);
}

.image-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    gap: 0.5rem;
}





/* Form grupları arası boşluk */
.row.g-4 > [class*='col-'] {
    margin-bottom: 1rem;
}

/* Zorunlu alan işareti */
.text-danger {
    color: var(--error-color) !important;
}



/* Bölüm başlıkları */
.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-brown);
    margin: 2rem 0 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
}

.section-title::after {
    display: none; /* Modern-theme.css'deki underline'ı kaldır */
}

.section-title i {
    margin-right: 0.75rem;
    color: var(--color-brown);
}



/* Form işlem butonları */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}



/* Mevcut resimler galerisi */
.current-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.current-image-card {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(var(--color-brown-rgb), 0.1);
    transition: all 0.3s ease;
}

.current-image-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.15);
}

.current-image-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.current-image-card:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--color-brown-rgb), 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.current-image-card:hover .image-overlay {
    opacity: 1;
}

.image-overlay .btn {
    margin: 0 0.25rem;
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

/* Responsive düzenlemeler */
@media (max-width: 991.98px) {
    .section-title {
        font-size: 1.1rem;
        margin: 1.5rem 0 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }
}

@media (max-width: 767.98px) {
    .current-images {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 1rem;
    }

    .image-card img,
    .current-image-card img {
        height: 140px;
    }

    .urun-form-header {
        padding: 1rem;
        margin: -1rem -1rem 1.5rem -1rem;
    }

    .btn {
        padding: 0.65rem 1.25rem;
        font-size: 0.95rem;
    }
}

/* Özel scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--soft-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--color-sand);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-brown);
}
