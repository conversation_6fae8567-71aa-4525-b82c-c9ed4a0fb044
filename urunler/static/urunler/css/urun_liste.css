/*
* ÜRÜNLER LİSTE STYLES CSS
* <PERSON><PERSON><PERSON>n listesi sayfası için <PERSON>zel stiller
* Ana tasarım sistemi ile uyumlu hale getirildi
*/

/* CSS Değişkenleri - Ana tasarım sistemi ile uyumlu */
:root {
  /* Sonbahar Renk Paleti */
  --color-navy: #42708C;
  --color-navy-rgb: 66, 112, 140;
  --color-sky: #80A7BF;
  --color-sky-rgb: 128, 167, 191;
  --color-moss: #657351;
  --color-moss-rgb: 101, 115, 81;
  --color-brown: #A66F3F;
  --color-brown-rgb: 166, 111, 63;
  --color-sand: #D9A273;
  --color-sand-rgb: 217, 162, 115;

  /* <PERSON>az Tonları */
  --soft-white: #f8f5f0;
  --soft-white-rgb: 248, 245, 240;
  --soft-light: #f2ede2;
  --soft-light-rgb: 242, 237, 226;
  --off-white: #f5f0e8;
  --off-white-rgb: 245, 240, 232;
  --white: #ffffff;
  --white-rgb: 255, 255, 255;

  /* <PERSON><PERSON> */
  --text-color: #5a3d2b;
  --text-color-rgb: 90, 61, 43;
  --text-soft: #6e4c36;
  --text-muted: #8c6952;
  --light-text: #a38b7d;

  /* Genel Kullanım */
  --primary-color: var(--color-brown);
  --primary-color-rgb: var(--color-brown-rgb);
  --secondary-color: var(--color-navy);
  --secondary-color-rgb: var(--color-navy-rgb);
  --accent-color: var(--color-moss);
  --accent-color-rgb: var(--color-moss-rgb);
  --light-accent: var(--color-sand);
  --light-accent-rgb: var(--color-sand-rgb);
  --highlight-color: var(--color-sky);
  --highlight-color-rgb: var(--color-sky-rgb);

  /* Fonksiyonel Renkler */
  --background-color: var(--soft-white);
  --default-color: var(--text-soft);
  --heading-color: var(--color-brown);
  --border-color: rgba(var(--color-brown-rgb), 0.15);
  --shadow-color: rgba(var(--color-brown-rgb), 0.08);
  --contrast-color: var(--soft-white);
  --surface-color: var(--soft-light);

  /* Font Aileleri */
  --heading-font: 'Poppins', sans-serif;
  --body-font: 'Roboto', sans-serif;

  /* Font Ağırlıkları */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Boşluk Sistemi */
  --space-unit: 0.25rem;
  --space-xxs: calc(var(--space-unit) * 1);
  --space-xs: calc(var(--space-unit) * 2);
  --space-sm: calc(var(--space-unit) * 3);
  --space-md: calc(var(--space-unit) * 4);
  --space-lg: calc(var(--space-unit) * 6);
  --space-xl: calc(var(--space-unit) * 8);
  --space-xxl: calc(var(--space-unit) * 12);
  --space-xxxl: calc(var(--space-unit) * 16);

  /* Kenar Yuvarlaklıkları */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  --border-radius-circle: 50%;
}

/* Search Result Alert */
.arama-uyarisi {
    background-color: rgba(var(--color-brown-rgb), 0.1);
    border-left: 4px solid var(--color-brown);
    border-radius: 0 8px 8px 0;
    padding: 15px 20px;
}

/* Resim placeholder */
.no-image-placeholder {
    width: 100%;
    aspect-ratio: 4/3;
    background-color: var(--soft-light);
    border-radius: 8px;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border: 2px dashed var(--border-color);
}

.no-image-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Etiket Stilleri */
.modern-tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
}

.modern-tag {
    display: inline-block;
    padding: 8px 15px;
    background-color: rgba(var(--color-moss-rgb), 0.1);
    color: var(--color-moss);
    border-radius: 20px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(var(--color-moss-rgb), 0.2);
    cursor: pointer;
}

.modern-tag:hover, .modern-tag.active {
    background-color: var(--color-moss);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(var(--color-moss-rgb), 0.3);
    text-decoration: none;
}

.filtre-paneli {
    background-color: rgba(var(--color-sand-rgb), 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(var(--color-brown-rgb), 0.1);
    border: 1px solid rgba(var(--color-brown-rgb), 0.1);
    transition: all 0.3s ease;
}

.filtre-paneli:hover {
    box-shadow: 0 8px 25px rgba(var(--color-brown-rgb), 0.15);
    transform: translateY(-2px);
}

.filtre-paneli__baslik {
    color: var(--color-brown);
    font-size: 1.2rem;
    margin-bottom: 15px;
    border-bottom: 2px solid rgba(var(--color-brown-rgb), 0.2);
    padding-bottom: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.filtre-paneli__baslik i {
    margin-right: 8px;
    color: var(--color-moss);
}

/* Arama Formu Stilleri */
.search-form {
    position: relative;
    margin-bottom: 15px;
}

.animated-search-group {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    display: flex;
}

.animated-search-group:hover, .animated-search-group:focus-within {
    border-color: var(--color-brown);
    box-shadow: 0 0 0 0.2rem rgba(var(--color-brown-rgb), 0.25);
}

.animated-search-input {
    border: none;
    border-radius: 4px 0 0 4px;
    padding: 8px 15px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background-color: var(--white);
    width: 100%;
    height: auto;
    flex: 1;
    color: var(--text-soft);
}

.animated-search-input:focus {
    outline: none;
    box-shadow: none;
}

.animated-search-input::placeholder {
    color: var(--text-muted);
}

.modern-btn-search {
    border: none;
    background: var(--color-sand);
    color: var(--color-brown);
    border-radius: 0 4px 4px 0;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 1px solid var(--border-color);
}

.modern-btn-search:hover {
    background: var(--color-brown);
    color: white;
    transform: none;
}

/* Arama Sonuçları Vurgusu */
.search-highlight {
    background-color: rgba(var(--color-brown-rgb), 0.2);
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: 500;
}

/* Ürün Grid */
.product-grid {
    margin-top: 20px;
}

/* DİKEY DİKDÖRTGEN - 3:4 ORANI */
html body .urunler .container .row .col-lg-4 .modern-card .image-container,
html body .urunler .container .row .col-md-6 .modern-card .image-container,
html body .urunler .modern-card .image-container,
html body .urunler .image-container,
html body .card--product .card__image,
.urunler .modern-card .image-container,
.urunler .image-container,
.card--product .card__image,
.modern-card .image-container,
.image-container {
    position: relative !important;
    overflow: hidden !important;
    border-radius: 8px 8px 0 0 !important;
    width: 100% !important;
    aspect-ratio: 3/4 !important;
}

/* Bu kuralı kaldırdık - çakışma yapıyordu */

/* Bu kuralı da kaldırdık - çakışma yapıyordu */

/* Çalışmalar CSS'ini AYNEN kopyala */
.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.modern-card:hover .image-container img {
    transform: scale(1.1);
}

/* Overlay stilleri - Çalışmalar ile uyumlu */
.image-container .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

/* Hover efektleri */
.modern-card:hover .image-overlay {
    opacity: 1;
}

/* Overlay butonları */
.image-overlay .modern-btn-circle {
    margin: 0 0.5rem;
    padding: 0.75rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--color-brown);
    text-decoration: none;
    transition: all 0.3s ease;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

/* Buton hover efektleri */
.image-overlay .modern-btn-circle:hover {
    background: var(--color-brown);
    color: white;
    transform: scale(1.1);
}

/* Link butonları için özel stil */
.modern-btn-link {
    cursor: pointer !important;
}

.modern-btn-link:hover {
    background: var(--color-navy) !important;
    color: white !important;
}

/* Ürün kartı stilleri - Çalışmalar ile uyumlu hale getirildi */
.modern-card {
    background: var(--surface-color) !important;
    border-radius: var(--border-radius-lg) !important;
    border: 1px solid rgba(var(--color-brown-rgb), 0.15) !important;
    box-shadow: 0 4px 20px rgba(var(--color-brown-rgb), 0.15) !important;
    transition: all 0.3s ease !important;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-card:hover {
    box-shadow: 0 12px 40px rgba(var(--color-brown-rgb), 0.25) !important;
    transform: translateY(-6px) !important;
    border-color: rgba(var(--color-brown-rgb), 0.25) !important;
}

.modern-card-body {
    padding: var(--space-md);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.modern-card-footer {
    padding: var(--space-sm) var(--space-md);
    border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
    background: rgba(var(--color-brown-rgb), 0.02);
    margin-top: auto;
}

.modern-card-title {
    font-family: var(--heading-font);
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-brown);
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.modern-card-title a {
    color: inherit;
    text-decoration: none;
}

.modern-card-title a:hover {
    color: var(--color-navy);
}

.modern-card-price {
    font-family: var(--heading-font);
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--color-moss);
    margin-bottom: 0.5rem;
}

.modern-card-actions {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
}

.product-info {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-brown);
    margin-bottom: 0.75rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-description {
    color: var(--text-soft);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid rgba(var(--color-brown-rgb), 0.1);
}

.product-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-moss);
}

.product-category, .modern-badge {
    background: rgba(var(--color-sand-rgb), 0.3);
    color: var(--color-brown);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.modern-badge-corner {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--color-moss);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    z-index: 2;
}

/* Responsive Düzenlemeler */
@media (max-width: 768px) {
    .filtre-paneli {
        margin-bottom: 20px;
        padding: 15px;
    }
    
    .modern-tag {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
    
    .product-image, .image-container {
        aspect-ratio: 3/4;
    }
    
    .product-info {
        padding: 1rem;
    }
    
    .product-title {
        font-size: 1.1rem;
    }
    
    .filtre-paneli__baslik {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .modern-tag-cloud {
        justify-content: flex-start;
    }

    .product-image, .image-container {
        aspect-ratio: 3/4;
    }

    .product-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Bu kuralı da kaldırdık - çakışma yapıyordu */

/* Bu kuralı da kaldırdık - çakışma yapıyordu */
