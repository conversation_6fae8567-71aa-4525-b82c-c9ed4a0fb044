from django.contrib import admin
from django.db.models import Count
from django.utils.html import format_html
from .models import Category, Product, ProductImage

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('ad', 'product_count', 'created_at_display', 'category_actions')
    list_filter = ('created_at',)
    search_fields = ('ad', 'aciklama')
    prepopulated_fields = {'slug': ('ad',)}
    ordering = ['ad']
    list_per_page = 20

    fieldsets = (
        ('📂 Kategori Bilgileri', {
            'fields': ('ad', 'aciklama'),
            'description': 'Kategori adı ve açıklaması'
        }),
        ('🔗 SEO Ayarları', {
            'fields': ('slug',),
            'description': 'URL\'de görünecek kategori adı (otomatik oluşturulur)'
        }),
        ('📅 <PERSON>rih Bilgileri', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
            'description': '<PERSON><PERSON><PERSON>tur<PERSON> ve güncelleme ta<PERSON>hleri'
        })
    )
    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(product_count=Count('product'))
        return queryset

    def product_count(self, obj):
        count = obj.product_count
        if count > 0:
            return format_html(
                '<span style="background: linear-gradient(45deg, #00d2d3, #54a0ff); '
                'color: white; padding: 3px 8px; border-radius: 10px; font-size: 11px; '
                'font-weight: bold;">{} ürün</span>',
                count
            )
        return format_html(
            '<span style="background: #e74c3c; color: white; padding: 3px 8px; '
            'border-radius: 10px; font-size: 11px;">Ürün yok</span>'
        )
    product_count.short_description = "Ürün Sayısı"
    product_count.admin_order_field = 'product_count'

    def created_at_display(self, obj):
        return obj.created_at.strftime('%d.%m.%Y')
    created_at_display.short_description = "Oluşturma Tarihi"
    created_at_display.admin_order_field = 'created_at'

    def category_actions(self, obj):
        products_url = f"/admin/urunler/product/?category__id__exact={obj.id}"
        return format_html(
            '<a class="button" href="{}" style="background-color: #79aec8; color: white; '
            'padding: 5px 10px; border-radius: 5px; text-decoration: none; font-size: 11px;">'
            '📦 Ürünleri Gör</a>',
            products_url
        )
    category_actions.short_description = "İşlemler"

    # Kategori filtrelemesi için izin ver
    def lookup_allowed(self, lookup, value):
        if lookup in ('category__id__exact', 'category__ad__exact'):
            return True
        return super().lookup_allowed(lookup, value)

class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1
    fields = ('image', 'image_preview', 'is_main', 'created_at')
    readonly_fields = ('created_at', 'image_preview')
    verbose_name = "Ürün Resmi"
    verbose_name_plural = "Ürün Resimleri"

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 60px; height: 60px; object-fit: cover; '
                'border-radius: 8px; border: 2px solid #ddd;" />',
                obj.image.url
            )
        return "Resim yok"
    image_preview.short_description = "Önizleme"

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    inlines = [ProductImageInline]
    list_display = ('name', 'category_display', 'price_display', 'stock_status',
                   'availability_status', 'display_tags', 'image_count', 'created_at_display')
    search_fields = ('name', 'description', 'tags__name')
    prepopulated_fields = {'slug': ('name',)}
    list_editable = ()
    list_per_page = 25
    ordering = ['-created_at']
    actions = ['make_available', 'make_unavailable', 'duplicate_products']

    # İzin verilen filtreleme alanları
    list_filter = ('category', 'is_available', 'created_at', 'tags__name')

    # Admin panelinde güvenli filtreleme için
    def get_list_filter(self, request):
        return self.list_filter

    # Özel filtreleme izinleri
    def lookup_allowed(self, lookup, value):
        # Tag filtrelemesine izin ver
        if lookup in ('tags__name__exact', 'tags__name__icontains', 'tags__name'):
            return True
        # Kategori filtrelemesine izin ver
        if lookup in ('category__id__exact', 'category__ad__exact'):
            return True
        # Varsayılan kontrolleri uygula
        return super().lookup_allowed(lookup, value)

    fieldsets = (
        ('🛍️ Temel Bilgiler', {
            'fields': ('category', 'name', 'description'),
            'description': 'Ürünün temel bilgileri'
        }),
        ('💰 Fiyat ve Stok', {
            'fields': ('price', 'stock_quantity', 'is_available'),
            'description': 'Ürünün fiyat ve stok bilgileri'
        }),
        ('🏷️ Etiketler', {
            'fields': ('tags',),
            'description': 'Ürünü kategorize etmek için etiketler ekleyin (virgülle ayırın)'
        }),
        ('🔗 SEO Ayarları', {
            'fields': ('slug',),
            'description': 'URL\'de görünecek ürün adı (otomatik oluşturulur)',
            'classes': ('collapse',)
        }),
        ('📅 Tarih Bilgileri', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
            'description': 'Ürün oluşturma ve güncelleme tarihleri'
        })
    )
    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('category').prefetch_related('tags', 'images')
        return queryset

    def category_display(self, obj):
        if obj.category:
            return format_html(
                '<span style="background: linear-gradient(45deg, #667eea, #764ba2); '
                'color: white; padding: 3px 8px; border-radius: 8px; font-size: 11px; '
                'font-weight: bold;">{}</span>',
                obj.category.ad
            )
        return format_html('<span style="color: #e74c3c;">Kategori yok</span>')
    category_display.short_description = "Kategori"
    category_display.admin_order_field = 'category__ad'

    def price_display(self, obj):
        if obj.price:
            return format_html(
                '<span style="color: #27ae60; font-weight: bold; font-size: 13px;">'
                '₺{}</span>',
                f"{obj.price:,.2f}"
            )
        return format_html('<span style="color: #e74c3c;">Fiyat yok</span>')
    price_display.short_description = "Fiyat"
    price_display.admin_order_field = 'price'

    def stock_status(self, obj):
        if obj.stock_quantity > 10:
            color = "#27ae60"
            icon = "✅"
            text = f"{obj.stock_quantity} adet"
        elif obj.stock_quantity > 0:
            color = "#f39c12"
            icon = "⚠️"
            text = f"{obj.stock_quantity} adet"
        else:
            color = "#e74c3c"
            icon = "❌"
            text = "Stok yok"

        return format_html(
            '<span style="color: {}; font-weight: bold;">{} {}</span>',
            color, icon, text
        )
    stock_status.short_description = "Stok Durumu"
    stock_status.admin_order_field = 'stock_quantity'

    def availability_status(self, obj):
        if obj.is_available:
            return format_html(
                '<span style="background: #27ae60; color: white; padding: 3px 8px; '
                'border-radius: 8px; font-size: 11px; font-weight: bold;">🟢 Aktif</span>'
            )
        return format_html(
            '<span style="background: #e74c3c; color: white; padding: 3px 8px; '
            'border-radius: 8px; font-size: 11px; font-weight: bold;">🔴 Pasif</span>'
        )
    availability_status.short_description = "Durum"
    availability_status.admin_order_field = 'is_available'

    def image_count(self, obj):
        count = obj.images.count()
        if count > 0:
            main_count = obj.images.filter(is_main=True).count()
            if main_count == 0:
                return format_html(
                    '<span style="color: #f39c12; font-weight: bold;">📷 {} (Ana resim yok!)</span>',
                    count
                )
            return format_html(
                '<span style="color: #27ae60; font-weight: bold;">📷 {}</span>',
                count
            )
        return format_html('<span style="color: #e74c3c;">📷 Resim yok</span>')
    image_count.short_description = "Resimler"

    def created_at_display(self, obj):
        return obj.created_at.strftime('%d.%m.%Y')
    created_at_display.short_description = "Oluşturma"
    created_at_display.admin_order_field = 'created_at'

    # Admin Actions
    def make_available(self, request, queryset):
        updated = queryset.update(is_available=True)
        self.message_user(request, f'{updated} ürün aktif hale getirildi.')
    make_available.short_description = "Seçili ürünleri aktif yap"

    def make_unavailable(self, request, queryset):
        updated = queryset.update(is_available=False)
        self.message_user(request, f'{updated} ürün pasif hale getirildi.')
    make_unavailable.short_description = "Seçili ürünleri pasif yap"

    def duplicate_products(self, request, queryset):
        for product in queryset:
            # Ürünü kopyala
            product.pk = None
            product.name = f"{product.name} (Kopya)"
            product.slug = f"{product.slug}-kopya"
            product.save()

        count = queryset.count()
        self.message_user(request, f'{count} ürün başarıyla kopyalandı.')
    duplicate_products.short_description = "Seçili ürünleri kopyala"

    def display_tags(self, obj):
        """Etiketleri renkli rozetler olarak göster"""
        tags = obj.tags.all()
        if not tags:
            return "-"

        html = ""
        for tag in tags:
            html += format_html(
                '<span style="background-color: #657351; color: white; padding: 3px 7px; '
                'border-radius: 10px; margin-right: 5px; display: inline-block; '
                'font-size: 0.8rem;">{}</span>',
                tag.name
            )
        return format_html(html)
    display_tags.short_description = 'Etiketler'
    display_tags.allow_tags = True

@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ('product', 'is_main', 'created_at')
    list_filter = ('is_main', 'created_at')
    search_fields = ('product__name',)
    ordering = ['-created_at']
    fieldsets = (
        (None, {
            'fields': ('product', 'image', 'is_main')
        }),
        ('Tarihler', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    readonly_fields = ('created_at',)

# Taggit Tag modelini özelleştirme
class TagAdmin(admin.ModelAdmin):
    verbose_name = 'Etiket'
    verbose_name_plural = 'Etiketler'
    
    # Form ve alan etiketlerini özelleştir
    formfield_overrides = {
        'name': {'label': 'Etiket Adı'},
        'slug': {'label': 'URL Uzantısı'}
    }
    
    list_display = ('name', 'slug', 'product_count', 'tag_actions')
    search_fields = ('name', 'slug')
    list_filter = ('name',)
    ordering = ('name',)
    prepopulated_fields = {'slug': ('name',)}
    change_list_template = 'admin/taggit/tag/change_list.html'
    
    def merge_tags(self, request, queryset):
        """Seçili etiketleri birleştir"""
        if queryset.count() < 2:
            self.message_user(request, "Birleştirmek için en az 2 etiket seçmelisiniz.")
            return

        # İlk etiketi hedef olarak kullan
        target_tag = queryset.first()

        # Diğer etiketleri birleştir
        from django.db import transaction
        with transaction.atomic():
            # TaggedItem modelini al
            from taggit.models import TaggedItem

            # Diğer etiketleri döngüye al
            for tag in queryset.exclude(pk=target_tag.pk):
                # Bu etiketle ilişkili tüm öğeleri bul
                tagged_items = TaggedItem.objects.filter(tag=tag)

                # Her öğeyi hedef etikete taşı
                for item in tagged_items:
                    # Hedef etikette bu öğe zaten var mı kontrol et
                    if not TaggedItem.objects.filter(
                        tag=target_tag,
                        content_type=item.content_type,
                        object_id=item.object_id
                    ).exists():
                        # Yoksa, yeni bir TaggedItem oluştur
                        TaggedItem.objects.create(
                            tag=target_tag,
                            content_type=item.content_type,
                            object_id=item.object_id
                        )

                # Eski etiketi sil
                tag.delete()


        self.message_user(request, f"Seçili etiketler '{target_tag.name}' etiketine başarıyla birleştirildi.")
    merge_tags.short_description = 'Seçili etiketleri birleştir'
    actions = [merge_tags]

    def get_queryset(self, request):
        """Etiketlerin kullanım sayısını hesapla"""
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(product_count=Count('taggit_taggeditem_items'))
        return queryset

    def product_count(self, obj):
        """Etiketin kaç üründe kullanıldığını göster"""
        return obj.product_count
    product_count.short_description = 'Ürün Sayısı'
    product_count.admin_order_field = 'product_count'

    def tag_actions(self, obj):
        """Etiket için hızlı eylemler"""
        view_url = f"/admin/taggit/tag/{obj.id}/change/"
        products_url = f"/admin/urunler/product/?tags__name__exact={obj.name}"

        return format_html(
            '<a class="button" href="{}" style="margin-right: 5px;">Düzenle</a>'
            '<a class="button" href="{}" style="background-color: #79aec8; color: white;">Ürünleri Gör</a>',
            view_url, products_url
        )
    tag_actions.short_description = 'İşlemler'
    tag_actions.allow_tags = True

    # Tag filtrelemesi için izin ver
    def lookup_allowed(self, lookup, value):
        if lookup in ('tags__name__exact', 'tags__name__icontains', 'tags__name'):
            return True
        return super().lookup_allowed(lookup, value)

    def merge_tags(self, request, queryset):
        """Seçili etiketleri birleştir"""
        if queryset.count() < 2:
            self.message_user(request, "Birleştirmek için en az 2 etiket seçmelisiniz.")
            return

        # İlk etiketi hedef olarak kullan
        target_tag = queryset.first()

        # Diğer etiketleri birleştir
        from django.db import transaction
        with transaction.atomic():
            # TaggedItem modelini al
            from taggit.models import TaggedItem

            # Diğer etiketleri döngüye al
            for tag in queryset.exclude(pk=target_tag.pk):
                # Bu etiketle ilişkili tüm öğeleri bul
                tagged_items = TaggedItem.objects.filter(tag=tag)

                # Her öğeyi hedef etikete taşı
                for item in tagged_items:
                    # Hedef etikette bu öğe zaten var mı kontrol et
                    if not TaggedItem.objects.filter(
                        tag=target_tag,
                        content_type=item.content_type,
                        object_id=item.object_id
                    ).exists():
                        # Yoksa, yeni bir TaggedItem oluştur
                        TaggedItem.objects.create(
                            tag=target_tag,
                            content_type=item.content_type,
                            object_id=item.object_id
                        )

                # Eski etiketi sil
                tag.delete()

        self.message_user(request, f"Seçili etiketler '{target_tag.name}' etiketine başarıyla birleştirildi.")
    merge_tags.short_description = "Seçili etiketleri birleştir"

    def changelist_view(self, request, extra_context=None):
        """Etiket istatistiklerini hesapla ve şablona gönder"""
        # Temel istatistikler
        from taggit.models import Tag, TaggedItem
        from .models import Product

        # Etiket sayısı
        tag_count = Tag.objects.count()

        # Ürün sayısı
        product_count = Product.objects.count()

        # Etiketli ürün sayısı
        tagged_product_count = Product.objects.filter(tags__isnull=False).distinct().count()

        # Ürün başına ortalama etiket sayısı
        if tagged_product_count > 0:
            total_tags = TaggedItem.objects.filter(
                content_type__model='product',
                content_type__app_label='urunler'
            ).count()
            avg_tags_per_product = total_tags / tagged_product_count
        else:
            avg_tags_per_product = 0

        # Popüler etiketler
        popular_tags = Tag.objects.annotate(
            count=Count('taggit_taggeditem_items')
        ).filter(count__gt=0).order_by('-count')[:20]

        # Ekstra bağlam
        context = {
            'tag_count': tag_count,
            'product_count': product_count,
            'tagged_product_count': tagged_product_count,
            'avg_tags_per_product': avg_tags_per_product,
            'popular_tags': popular_tags,
        }

        if extra_context:
            context.update(extra_context)

        return super().changelist_view(request, extra_context=context)

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """Etiket detay sayfasını özelleştir"""
        from taggit.models import Tag, TaggedItem
        from django.contrib.contenttypes.models import ContentType
        from .models import Product

        # Etiket nesnesini al
        tag = self.get_object(request, object_id)

        if tag:
            # Bu etiketi kullanan ürünleri bul
            product_content_type = ContentType.objects.get_for_model(Product)
            product_ids = TaggedItem.objects.filter(
                tag=tag,
                content_type=product_content_type
            ).values_list('object_id', flat=True)

            products = Product.objects.filter(id__in=product_ids)
            product_count = products.count()

            # Benzer etiketleri bul (isim benzerliği)
            from django.db.models import Q
            similar_tags = []

            # Etiket adının parçalarını al
            tag_parts = tag.name.split()

            # Her parça için benzer etiketleri ara
            if tag_parts:
                q_objects = Q()
                for part in tag_parts:
                    if len(part) > 2:  # Çok kısa parçaları atla
                        q_objects |= Q(name__icontains=part)

                # Kendisi hariç benzer etiketleri bul
                similar_tags = Tag.objects.filter(q_objects).exclude(id=tag.id).annotate(
                    count=Count('taggit_taggeditem_items')
                ).order_by('-count')[:10]

            # Ekstra bağlam
            context = {
                'products': products,
                'product_count': product_count,
                'similar_tags': similar_tags,
            }

            if extra_context:
                context.update(extra_context)

            return super().change_view(request, object_id, form_url, extra_context=context)

        return super().change_view(request, object_id, form_url, extra_context=extra_context)

# Not: Tag modelinin admin kaydı apps.py dosyasında yapılıyor
# Bu şekilde taggit'in kendi admin kaydını geçersiz kılabiliyoruz