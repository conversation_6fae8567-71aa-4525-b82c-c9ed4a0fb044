"""
<PERSON><PERSON><PERSON><PERSON>ler uygulaması için template tag'leri.
"""

from django import template
from ..permissions import get_user_product_permissions, check_product_admin_permission

register = template.Library()


@register.simple_tag
def user_can_manage_products(user):
    """
    <PERSON><PERSON><PERSON><PERSON><PERSON>nın ürün yönetimi yetkisi olup olmadığını kontrol eder.
    
    Kullanım:
        {% user_can_manage_products user as can_manage %}
        {% if can_manage %}
            <a href="{% url 'urunler:urun_ekle' %}"><PERSON><PERSON><PERSON><PERSON></a>
        {% endif %}
    """
    return check_product_admin_permission(user)


@register.simple_tag
def get_product_permissions(user):
    """
    Kullanıcının tüm ürün yetkilendirme bilgilerini döndürür.
    
    Kullanım:
        {% get_product_permissions user as perms %}
        {% if perms.can_add_product %}
            <a href="{% url 'urunler:urun_ekle' %}"><PERSON><PERSON><PERSON><PERSON></a>
        {% endif %}
    """
    return get_user_product_permissions(user)


@register.inclusion_tag('urunler/partials/admin_buttons.html', takes_context=True)
def show_product_admin_buttons(context, product=None):
    """
    Ürün yönetimi butonlarını gösterir.
    
    Kullanım:
        {% show_product_admin_buttons product %}
    """
    user = context['request'].user
    permissions = get_user_product_permissions(user)
    
    return {
        'user': user,
        'product': product,
        'permissions': permissions,
        'request': context['request']
    }


@register.filter
def can_edit_product(user, product=None):
    """
    Kullanıcının belirli bir ürünü düzenleyip düzenleyemeyeceğini kontrol eder.
    
    Kullanım:
        {% if user|can_edit_product:product %}
            <a href="{% url 'urunler:urun_duzenle' product.id product.slug %}">Düzenle</a>
        {% endif %}
    """
    return check_product_admin_permission(user)


@register.filter
def can_delete_product(user, product=None):
    """
    Kullanıcının belirli bir ürünü silip silemeyeceğini kontrol eder.
    
    Kullanım:
        {% if user|can_delete_product:product %}
            <button class="btn btn-danger">Sil</button>
        {% endif %}
    """
    return check_product_admin_permission(user)
