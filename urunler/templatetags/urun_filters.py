from django import template
from django.template.defaultfilters import stringfilter
from django.utils.safestring import mark_safe
import re
import os

register = template.Library()

@register.filter(name='highlight')
def highlight(text, search):
    """
    Metindeki arama terimlerini vurgular.
    
    Kullanım:
    {{ product.name|highlight:search_query }}
    """
    if not search or not text:
        return text
    
    # HTML etiketlerini korumak için
    search = re.escape(search)
    pattern = re.compile(f'({search})', re.IGNORECASE)
    highlighted = pattern.sub(r'<span class="search-highlight">\1</span>', str(text))
    
    return mark_safe(highlighted)

@register.filter(name='basename')
def basename(value):
    """
    Do<PERSON>a yolundan dosya adını çıkarır.
    
    Kullanım:
    {{ file_path|basename }}
    """
    return os.path.basename(str(value))

@register.filter(name='replace_dot')
def replace_dot(value):
    """
    Metindeki noktaları virgü<PERSON>tirir.
    
    Kullanım:
    {{ value|replace_dot }}
    """
    if not value:
        return ''
    return str(value).replace('.', ',')
