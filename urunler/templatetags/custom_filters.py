from django import template
from django.template.defaultfilters import stringfilter

register = template.Library()

@register.filter(name='replace')
@stringfilter
def replace(value, arg):
    """
    Replaces characters in the string using format 'old:new'.
    Example: {{ value|replace:"a:b" }} replaces all 'a's with 'b's
    """
    if not value:
        return ''
    
    try:
        old, new = arg.split(':')
        return value.replace(old, new)
    except (ValueError, AttributeError):
        return value
