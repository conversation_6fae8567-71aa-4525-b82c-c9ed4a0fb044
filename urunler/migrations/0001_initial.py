# Generated by Django 5.2 on 2025-06-08 01:58

import django.db.models.deletion
import taggit.managers
import urunler.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='<PERSON><PERSON><PERSON> Adı')),
                ('slug', models.SlugField(unique=True, verbose_name='URL Yolu')),
                ('description', models.TextField(blank=True, verbose_name='A<PERSON>ıklama')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Kate<PERSON><PERSON>',
                'verbose_name_plural': '<PERSON>goriler',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Ürün Adı')),
                ('slug', models.SlugField(unique=True, verbose_name='URL Yolu')),
                ('description', models.TextField(verbose_name='Açıklama')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Fiyat')),
                ('stock_quantity', models.PositiveIntegerField(default=0, verbose_name='Stok Miktarı')),
                ('is_available', models.BooleanField(default=True, verbose_name='Stokta Var mı?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='urunler.category', verbose_name='Kategori')),
                ('ekleyen', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='urunler', to=settings.AUTH_USER_MODEL, verbose_name='Ekleyen Kullanıcı')),
                ('tags', taggit.managers.TaggableManager(blank=True, help_text='Ürünle ilgili etiketleri virgülle ayırarak girin', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Etiketler')),
            ],
            options={
                'verbose_name': 'Ürün',
                'verbose_name_plural': 'Ürünler',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to=urunler.models.product_image_upload_path, verbose_name='Ürün Görseli')),
                ('is_main', models.BooleanField(default=False, verbose_name='Ana Görsel')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='urunler.product')),
            ],
            options={
                'verbose_name': 'Ürün Görseli',
                'verbose_name_plural': 'Ürün Görselleri',
                'ordering': ['-created_at'],
            },
        ),
    ]
