# Generated by Django 4.2 on 2025-06-09 20:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('urunler', '0001_initial'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['-created_at'], name='product_created_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['slug'], name='product_slug_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_available', '-created_at'], name='product_available_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', '-created_at'], name='product_category_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['ekleyen', '-created_at'], name='product_user_idx'),
        ),
    ]
