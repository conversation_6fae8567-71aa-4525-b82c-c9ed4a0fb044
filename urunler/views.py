from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseForbidden, JsonResponse
from django.contrib import messages
from django.views.decorators.http import require_POST, require_http_methods
from django.utils.text import slugify
from django.db.models import Q, Case, When, IntegerField, F, ExpressionWrapper, FloatField
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils import timezone
from decimal import Decimal
import os
import json
from datetime import datetime

from .models import Category, Product, ProductImage
from .forms import ProductForm, ProductImageForm, ProductDeleteForm, ProductImageFormSet
from .permissions import require_product_admin, require_product_image_permission, get_user_product_permissions
from utils.logging import get_logger

# Logger'ı başlat
logger = get_logger(__name__)

def _validate_image_file(uploaded_file):
    """
    Resim dosyas<PERSON> doğrulama yardımcı fonksiyonu.
    Returns: (is_valid, error_message)
    """
    # Dosya boyutu kontrolü - settings'den al
    max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 5 * 1024 * 1024)  # Default 5MB
    if uploaded_file.size > max_size:
        max_size_mb = max_size / (1024 * 1024)
        return False, f'Dosya boyutu {max_size_mb:.0f}MB\'dan büyük olamaz.'

    # Dosya uzantısı kontrolü - settings'den al
    allowed_extensions = getattr(settings, 'SECURE_UPLOAD_EXTENSIONS', ['.jpg', '.jpeg', '.png', '.gif', '.webp'])
    file_ext = os.path.splitext(uploaded_file.name)[1].lower()
    if file_ext not in allowed_extensions:
        return False, 'Geçersiz dosya uzantısı. Sadece JPG, JPEG, PNG, GIF ve WEBP formatlarında dosya yükleyebilirsiniz.'

    # Content type kontrolü - settings'den al
    allowed_content_types = getattr(settings, 'SECURE_UPLOAD_CONTENT_TYPES', ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
    if uploaded_file.content_type not in allowed_content_types:
        return False, 'Geçersiz dosya türü. Sadece resim dosyaları yükleyebilirsiniz.'

    return True, None


def _save_product_image(uploaded_file, product=None):
    """
    Resim dosyasını kaydetme yardımcı fonksiyonu.
    Returns: (saved_path, file_url, image_id, is_main)
    """
    # Dosya adını ve uzantısını al
    file_name = os.path.splitext(uploaded_file.name)[0]
    file_ext = os.path.splitext(uploaded_file.name)[1].lower()

    # Benzersiz bir dosya adı oluştur
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    new_filename = f"{timestamp}_{file_name[:50]}{file_ext}"

    # Dosya yolunu oluştur
    if hasattr(settings, 'PRODUCT_IMAGES_DIR'):
        relative_path = os.path.join(
            settings.PRODUCT_IMAGES_DIR,
            datetime.now().strftime('%Y/%m/%d')
        )
        file_path = os.path.join(relative_path, new_filename)

        # Klasör yoksa oluştur
        full_dir_path = os.path.join(settings.MEDIA_ROOT, relative_path)
        os.makedirs(full_dir_path, exist_ok=True)
    else:
        # Fallback path
        file_path = os.path.join('urun_resimleri', new_filename)

    # Dosyayı kaydet
    saved_path = default_storage.save(file_path, ContentFile(uploaded_file.read()))
    file_url = default_storage.url(saved_path)

    # Veritabanına kaydet
    image_id = None
    is_main = False

    if product:
        # İlk resim ana resim olarak işaretlenir
        is_main = not product.images.exists()

        # Aynı isimde resim var mı kontrol et
        if not ProductImage.objects.filter(product=product, image__endswith=uploaded_file.name).exists():
            product_image = ProductImage.objects.create(
                product=product,
                image=saved_path,
                is_main=is_main
            )
            image_id = product_image.id

    return saved_path, file_url, image_id, is_main


# upload_image fonksiyonu kaldırıldı - fotograf_ekle ile birleştirildi

@require_http_methods(['POST'])
def set_main_image(request, product_id):
    """
    Bir ürünün ana resmini ayarlar.
    """
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'message': 'Yetkisiz erişim.'}, status=403)
    
    try:
        data = json.loads(request.body)
        image_id = data.get('image_id')
        
        if not image_id:
            return JsonResponse({'success': False, 'message': 'Resim ID\'si gerekli.'}, status=400)
        
        product = get_object_or_404(Product, id=product_id)
        
        # Tüm resimlerin ana resim özelliğini kaldır
        ProductImage.objects.filter(product=product, is_main=True).update(is_main=False)
        
        # Yeni ana resmi ayarla
        image = get_object_or_404(ProductImage, id=image_id, product=product)
        image.is_main = True
        image.save()
        
        return JsonResponse({
            'success': True,
            'message': 'Ana resim başarıyla güncellendi.',
            'image_id': image.id,
            'image_url': image.image.url
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Geçersiz JSON verisi.'}, status=400)
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)

@require_http_methods(['DELETE'])
def delete_image(request, image_id):
    """
    Bir resmi siler.
    """
    if not request.user.is_authenticated:
        return JsonResponse({'success': False, 'message': 'Yetkisiz erişim.'}, status=403)
    
    try:
        image = get_object_or_404(ProductImage, id=image_id)
        product = image.product
        
        # Eğer silinecek resim ana resimse, başka bir resmi ana resim yap
        if image.is_main:
            other_images = ProductImage.objects.filter(product=product).exclude(id=image_id)
            if other_images.exists():
                new_main_image = other_images.first()
                new_main_image.is_main = True
                new_main_image.save()
        
        # Resmi sil
        image_path = image.image.path
        if os.path.exists(image_path):
            os.remove(image_path)
        
        # Veritabanından sil
        image.delete()
        
        return JsonResponse({
            'success': True,
            'message': 'Resim başarıyla silindi.'
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)

from django.views.decorators.cache import cache_page
from django.conf import settings

@cache_page(60 * 10)  # 10 dakika cache
def urun_listesi(request, category_slug=None):


    # Tüm kategorileri al
    categories = Category.objects.all()

    # Temel sorgu - sadece mevcut ürünler
    products = Product.objects.filter(is_available=True).select_related('category', 'ekleyen').prefetch_related('tags', 'images').order_by('-created_at')

    # Kategori filtreleme
    category = None
    if category_slug:
        category = get_object_or_404(Category, slug=category_slug)
        products = products.filter(category=category)


    # Arama sorgusu
    search_query = request.GET.get('q', '')
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(tags__name__icontains=search_query)
        ).distinct()


    # Etiket filtreleme
    tag_slug = request.GET.get('tag')
    if tag_slug:
        products = products.filter(tags__slug=tag_slug)


    # Tüm etiketleri al
    from taggit.models import Tag
    tags = Tag.objects.all()

    product_count = products.count()
    
    context = {
        'category': category,
        'categories': categories,
        'products': products,
        'tags': tags,
        'current_tag': tag_slug,
        'search_query': search_query,
        'user_permissions': get_user_product_permissions(request.user)
    }
    return render(request, 'urunler/urun_listesi.html', context)

def urun_detay(request, id, slug):
    try:
        # Ürünü getir veya 404 hatası döndür
        product = get_object_or_404(Product.objects.select_related('category', 'ekleyen').prefetch_related('tags', 'images'), id=id, slug=slug, is_available=True)
        

        
        # Benzer ürünleri al (gelişmiş algoritma)
        similar_products = get_similar_products(product)
        
        # ProductDeleteForm'u içe aktar
        from .forms import ProductDeleteForm
        
        context = {
            'product': product,
            'similar_products': similar_products,
            'delete_form': ProductDeleteForm(),
            'user_permissions': get_user_product_permissions(request.user)
        }
        return render(request, 'urunler/urun_detay.html', context)
    except Exception as e:
        logger.error(f"Ürün detay sayfasında hata: {str(e)}", exc_info=True, extra={
            'product_id': id,
            'product_slug': slug,
            'operation': 'urun_detay_view_error'
        })
        raise

def get_similar_products(product, max_products=4):
    """
    Gelişmiş benzer ürünler algoritması.

    Benzerlik faktörleri:
    1. Etiket eşleşmesi (en yüksek ağırlık)
    2. Kategori eşleşmesi
    3. Fiyat aralığı benzerliği
    4. Aynı ekleyen kullanıcı

    Her faktör için bir puan hesaplanır ve toplam benzerlik puanına göre sıralama yapılır.
    """
    from django.db.models import Count, F, Q, Case, When, IntegerField, ExpressionWrapper, FloatField, Value, DecimalField
    from django.db.models.functions import Abs
    from decimal import Decimal
    import math

    # Mevcut ürünün bilgileri
    product_tags = list(product.tags.values_list('id', flat=True))
    product_category = product.category
    product_price = product.price or Decimal('0')
    product_user = product.ekleyen

    # Tüm ürünleri al (kendisi hariç ve stokta olanlar)
    all_products = Product.objects.filter(is_available=True).exclude(id=product.id).select_related('category', 'ekleyen').prefetch_related('tags', 'images')

    # Etiket eşleşmesi yoksa sadece kategori bazlı filtreleme yap
    if not product_tags:
        # Kategori eşleşmesi (2 puan)
        similar_products = all_products.filter(category=product_category)\
            .annotate(similarity_score=Case(
                When(category=product_category, then=2),
                default=0,
                output_field=IntegerField()
            ))

        # Fiyat aralığı benzerliği (0-1 puan)
        if product_price > 0:
            # Fiyat farkının yüzdesini hesapla (ne kadar düşükse o kadar benzer)
            similar_products = similar_products.annotate(
                price_diff=ExpressionWrapper(
                    Abs(F('price') - product_price) / (product_price + Decimal('0.01')),
                    output_field=FloatField()
                )
            ).annotate(
                price_similarity=Case(
                    # Fiyat farkı %50'den azsa, fark yüzdesine göre 0-1 arası puan ver
                    When(price_diff__lte=0.5,
                         then=ExpressionWrapper(1 - F('price_diff'), output_field=FloatField())),
                    default=0,
                    output_field=FloatField()
                )
            )

            # Toplam benzerlik puanını güncelle
            similar_products = similar_products.annotate(
                similarity_score=F('similarity_score') + F('price_similarity')
            )

        # Aynı kullanıcının ürünlerine bonus puan (1 puan)
        if product_user:
            similar_products = similar_products.annotate(
                user_match=Case(
                    When(ekleyen=product_user, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            ).annotate(
                similarity_score=F('similarity_score') + F('user_match')
            )
    else:
        # Etiket eşleşmesi (her etiket için 3 puan)
        similar_products = all_products.filter(
            Q(tags__id__in=product_tags) | Q(category=product_category)
        ).annotate(
            # Eşleşen etiket sayısı
            matching_tags_count=Count('tags', filter=Q(tags__id__in=product_tags)),
            # Kategori eşleşmesi (2 puan)
            category_match=Case(
                When(category=product_category, then=2),
                default=0,
                output_field=IntegerField()
            )
        ).annotate(
            # Etiket benzerlik puanı (her etiket 3 puan)
            tag_similarity=ExpressionWrapper(
                F('matching_tags_count') * 3,
                output_field=IntegerField()
            ),
            # Başlangıç benzerlik puanı
            similarity_score=F('tag_similarity') + F('category_match')
        )

        # Fiyat aralığı benzerliği (0-1 puan)
        if product_price > 0:
            # Fiyat farkının yüzdesini hesapla (ne kadar düşükse o kadar benzer)
            similar_products = similar_products.annotate(
                price_diff=ExpressionWrapper(
                    Abs(F('price') - product_price) / (product_price + Decimal('0.01')),
                    output_field=FloatField()
                )
            ).annotate(
                price_similarity=Case(
                    # Fiyat farkı %50'den azsa, fark yüzdesine göre 0-1 arası puan ver
                    When(price_diff__lte=0.5,
                         then=ExpressionWrapper(1 - F('price_diff'), output_field=FloatField())),
                    default=0,
                    output_field=FloatField()
                )
            )

            # Toplam benzerlik puanını güncelle
            similar_products = similar_products.annotate(
                similarity_score=F('similarity_score') + F('price_similarity')
            )

        # Aynı kullanıcının ürünlerine bonus puan (1 puan)
        if product_user:
            similar_products = similar_products.annotate(
                user_match=Case(
                    When(ekleyen=product_user, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            ).annotate(
                similarity_score=F('similarity_score') + F('user_match')
            )

    # Benzerlik puanına göre sırala ve en fazla max_products kadar ürün döndür
    similar_products = similar_products.order_by('-similarity_score', '-created_at')[:max_products]

    return similar_products

@login_required
@require_product_admin
def urun_ekle(request):
    """Yeni bir ürün ekler."""

    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES)
        formset = ProductImageFormSet(request.POST, request.FILES)

        if form.is_valid() and formset.is_valid():
            try:
                urun = form.save(commit=False)
                urun.slug = slugify(urun.name)
                urun.ekleyen = request.user
                urun.is_available = True  # Ürünü aktif olarak işaretle
                urun.save()
                form.save_m2m()  # many-to-many ilişkilerini kaydet

                # Formset'i kaydet - sadece dolu formları kaydet
                formset.instance = urun
                instances = formset.save(commit=False)
                for instance in instances:
                    if instance.image:  # Sadece resim varsa kaydet
                        instance.save()
                formset.save_m2m()

                # İlk resmi ana resim yap
                images = urun.images.all()
                if images.exists():
                    first_image = images.first()
                    first_image.is_main = True
                    first_image.save()

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': True,
                        'message': 'Ürün başarıyla eklendi.',
                        'redirect_url': f'/urunler/{urun.id}/{urun.slug}/'
                    })

                messages.success(request, 'Ürün başarıyla eklendi.')
                return redirect('urunler:urun_detay', id=urun.id, slug=urun.slug)

            except Exception as e:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': f'Ürün eklenirken bir hata oluştu: {str(e)}'
                    }, status=500)
                messages.error(request, f'Ürün eklenirken bir hata oluştu: {str(e)}')

        # Form geçerli değilse
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Form geçersiz. Lütfen tüm alanları doğru şekilde doldurduğunuzdan emin olun.',
                'errors': form.errors
            }, status=400)
    else:
        form = ProductForm()
        formset = ProductImageFormSet()

    # Yeni resim yükleme formu
    image_form = ProductImageForm()

    context = {
        'form': form,
        'formset': formset,
        'image_form': image_form,
        'title': 'Yeni Ürün Ekle',
        'urun': None  # Yeni ürün eklerken urun None olacak
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': False,
            'message': 'Geçersiz istek.'
        }, status=400)

    return render(request, 'urunler/urun_form.html', context)

@login_required
@require_product_admin
def urun_duzenle(request, id, slug):
    """Var olan bir ürünü düzenler."""

    urun = get_object_or_404(Product.objects.select_related('category', 'ekleyen').prefetch_related('tags', 'images'), id=id, slug=slug)



    if request.method == 'POST':
        form = ProductForm(request.POST, request.FILES, instance=urun)
        formset = ProductImageFormSet(request.POST, request.FILES, instance=urun)



        if form.is_valid() and formset.is_valid():
            try:
                # Önce Product'ı kaydet
                product = form.save(commit=False)
                product.slug = slugify(product.name)
                product.is_available = True  # Ürünü aktif olarak işaretle
                product.save()
                form.save_m2m()  # many-to-many ilişkilerini kaydet

                # Formset'i kaydet - sadece dolu formları kaydet
                instances = formset.save(commit=False)
                for instance in instances:
                    if instance.image:  # Sadece resim varsa kaydet
                        instance.save()
                formset.save_m2m()

                # Manuel DELETE kontrolü
                for formset_form in formset.forms:
                    if formset_form.instance.pk:  # Sadece mevcut objeler için
                        delete_field_name = f"{formset_form.prefix}-DELETE"
                        is_marked_for_deletion = request.POST.get(delete_field_name) == 'on'

                        if is_marked_for_deletion:
                            formset_form.instance.delete()

                # Formset'in otomatik silme işlemi
                for obj in formset.deleted_objects:
                    obj.delete()

                # Ana resim kontrolü - eğer ana resim yoksa ilk resmi ana resim yap
                if not product.images.filter(is_main=True).exists():
                    first_image = product.images.first()
                    if first_image:
                        first_image.is_main = True
                        first_image.save()

                # Ana resim değişikliği
                main_image_id = request.POST.get('main_image')
                if main_image_id:
                    # Önce tüm resimlerin is_main değerini False yap
                    product.images.all().update(is_main=False)
                    # Seçili resmi ana resim yap
                    product.images.filter(id=main_image_id).update(is_main=True)

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': True,
                        'message': 'Ürün başarıyla güncellendi.',
                        'redirect_url': f'/urunler/{product.id}/{product.slug}/'
                    })

                messages.success(request, 'Ürün başarıyla güncellendi.')
                return redirect('urunler:urun_detay', id=product.id, slug=product.slug)

            except Exception as e:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': f'Ürün güncellenirken bir hata oluştu: {str(e)}'
                    }, status=500)
                messages.error(request, f'Ürün güncellenirken bir hata oluştu: {str(e)}')

        # Form geçerli değilse
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Form geçersiz. Lütfen tüm alanları doğru şekilde doldurduğunuzdan emin olun.',
                'errors': form.errors
            }, status=400)
    else:
        form = ProductForm(instance=urun)
        formset = ProductImageFormSet(instance=urun)

    # Yeni resim yükleme formu
    image_form = ProductImageForm()

    context = {
        'form': form,
        'formset': formset,
        'urun': urun,  # urun.images.all() ile şablondan erişilecek
        'image_form': image_form,
        'title': 'Ürün Düzenle',
    }



    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': False,
            'message': 'Geçersiz istek.'
        }, status=400)

    return render(request, 'urunler/urun_form.html', context)

# Eski ikinci urun_detay fonksiyonu kaldırıldı

@login_required
@require_product_admin
def urun_sil(request, id, slug):
    """Bir ürünü siler."""

    product = get_object_or_404(Product, id=id, slug=slug)

    if request.method == 'POST':
        # Ürünü sil (Product.delete() metodu override edilmiş ve ilişkili fotoğrafları da siliyor)
        product.delete()

        messages.success(request, 'Ürün başarıyla silindi.')
        return redirect('urunler:urun_listesi')

    return render(request, 'urunler/urun_detay.html', {
        'product': product,
        'show_delete_modal': True,
        'title': f'{product.name}'
    })

from django.http import JsonResponse

@login_required
@require_POST
@require_product_image_permission
def product_image_upload(request, id=None, slug=None):
    """
    Ürüne resim yükleme API endpoint'i.

    Hem AJAX hem normal form isteklerini destekler:
    - AJAX: Tek dosya yükleme, anında yanıt
    - Form: Çoklu dosya yükleme, toplu işlem

    URL Parametreleri:
        id (int): Ürün ID'si
        slug (str): Ürün slug'ı

    POST Parametreleri:
        image: Yüklenecek resim dosyası(ları)

    Returns:
        JsonResponse: İşlem sonucu ve resim bilgileri
    """
    product = get_object_or_404(Product, id=id, slug=slug)



    # Dosya kontrolü
    if 'image' not in request.FILES:
        return JsonResponse({
            'success': False,
            'error': 'Lütfen bir resim dosyası seçin.'
        }, status=400)

    # AJAX isteği için tek dosya yükleme
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        uploaded_file = request.FILES['image']

        # Dosya doğrulama
        is_valid, error_message = _validate_image_file(uploaded_file)
        if not is_valid:
            return JsonResponse({
                'success': False,
                'error': error_message
            }, status=400)

        try:
            # Resmi kaydet
            saved_path, file_url, image_id, is_main = _save_product_image(uploaded_file, product)

            logger.info("AJAX resim yükleme başarılı", extra={
                'product_id': product.id,
                'image_id': image_id,
                'file_name': uploaded_file.name,
                'is_main': is_main,
                'operation': 'ajax_image_upload_success'
            })

            return JsonResponse({
                'success': True,
                'message': 'Resim başarıyla yüklendi.',
                'image': {
                    'id': image_id,
                    'url': file_url,
                    'is_main': is_main,
                    'name': os.path.basename(saved_path)
                }
            })

        except Exception as e:
            logger.error("AJAX resim yükleme hatası", extra={
                'product_id': product.id,
                'error': str(e),
                'file_name': uploaded_file.name,
                'operation': 'ajax_image_upload_error'
            })
            return JsonResponse({
                'success': False,
                'error': f'Resim yüklenirken bir hata oluştu: {str(e)}'
            }, status=500)

    # Normal form gönderimi için çoklu dosya yükleme
    images = request.FILES.getlist('image')

    if not images:
        return JsonResponse({
            'success': False,
            'error': 'Lütfen en az bir resim yükleyin.'
        }, status=400)

    response_data = []
    errors = []
    skipped = []



    for img in images:
        try:
            # Dosya doğrulama
            is_valid, error_message = _validate_image_file(img)
            if not is_valid:
                errors.append(f'{img.name}: {error_message}')
                continue

            # Aynı isimde resim var mı kontrol et
            if ProductImage.objects.filter(product=product, image__endswith=img.name).exists():
                skipped.append(f'{img.name}: Bu isimde resim zaten mevcut')
                continue

            # Resmi kaydet
            saved_path, file_url, image_id, is_main = _save_product_image(img, product)

            response_data.append({
                'id': image_id,
                'url': file_url,
                'is_main': is_main,
                'name': os.path.basename(saved_path)
            })

        except Exception as e:
            errors.append(f'{img.name}: {str(e)}')

    # Sonuç döndür
    if response_data:
        result = {
            'success': True,
            'message': f'{len(response_data)} resim başarıyla yüklendi.',
            'images': response_data,
            'uploaded_count': len(response_data),
            'total_count': len(images)
        }

        if errors:
            result['errors'] = errors
        if skipped:
            result['skipped'] = skipped

        logger.info("Çoklu resim yükleme tamamlandı", extra={
            'product_id': product.id,
            'uploaded_count': len(response_data),
            'error_count': len(errors),
            'skipped_count': len(skipped),
            'operation': 'bulk_image_upload_success'
        })

        return JsonResponse(result)
    else:
        logger.warning("Hiçbir resim yüklenemedi", extra={
            'product_id': product.id,
            'error_count': len(errors),
            'skipped_count': len(skipped),
            'operation': 'bulk_image_upload_failed'
        })

        return JsonResponse({
            'success': False,
            'error': 'Hiçbir resim yüklenemedi.',
            'errors': errors,
            'skipped': skipped
        }, status=400)


@require_http_methods(['POST'])
def product_image_upload_legacy(request):
    """
    Eski upload_image API'si için geriye uyumluluk wrapper'ı.

    DEPRECATED: Bu endpoint kullanımdan kaldırılmıştır.
    Lütfen /api/products/<id>/<slug>/upload-image/ endpoint'ini kullanın.

    Bu fonksiyon sadece mevcut entegrasyonların bozulmaması için korunmuştur.
    """
    logger.warning("Deprecated upload_image endpoint kullanıldı", extra={
        'user_id': request.user.id if request.user.is_authenticated else None,
        'ip_address': request.META.get('REMOTE_ADDR'),
        'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
        'operation': 'deprecated_upload_image_used'
    })

    # Sadece AJAX isteklerine izin ver
    if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'success': False,
            'error': 'Bu işlem sadece AJAX istekleri için geçerlidir.'
        }, status=400)

    # Ürün ID'si kontrolü
    product_id = request.POST.get('product_id')
    if not product_id or not product_id.isdigit():
        return JsonResponse({
            'success': False,
            'error': 'Geçerli bir product_id parametresi gereklidir.'
        }, status=400)

    try:
        product = Product.objects.get(id=product_id)
    except Product.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Belirtilen ürün bulunamadı.'
        }, status=404)

    # Yetkilendirme kontrolü
    from .permissions import check_product_image_permission
    if not check_product_image_permission(request.user, product):
        return JsonResponse({
            'success': False,
            'error': 'Bu işlemi yapmaya yetkiniz yok.'
        }, status=403)

    # Dosya kontrolü
    if 'image' not in request.FILES:
        return JsonResponse({
            'success': False,
            'error': 'Lütfen bir resim dosyası seçin.'
        }, status=400)

    uploaded_file = request.FILES['image']

    # Dosya doğrulama
    is_valid, error_message = _validate_image_file(uploaded_file)
    if not is_valid:
        return JsonResponse({
            'success': False,
            'error': error_message
        }, status=400)

    try:
        # Resmi kaydet
        saved_path, file_url, image_id, is_main = _save_product_image(uploaded_file, product)

        return JsonResponse({
            'success': True,
            'file_url': file_url,  # Eski API format uyumluluğu
            'image_id': image_id,
            'is_main': is_main,
            'message': 'Resim başarıyla yüklendi. Not: Bu API deprecated, lütfen yeni endpoint kullanın.'
        })

    except Exception as e:
        logger.error("Legacy resim yükleme hatası", extra={
            'product_id': product.id,
            'error': str(e),
            'file_name': uploaded_file.name,
            'operation': 'legacy_image_upload_error'
        })
        return JsonResponse({
            'success': False,
            'error': f'Resim yüklenirken bir hata oluştu: {str(e)}'
        }, status=500)
