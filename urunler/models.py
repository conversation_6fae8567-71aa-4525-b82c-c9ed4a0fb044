from django.db import models
from django.utils.text import slugify
from django.contrib.auth.models import User
from taggit.managers import TaggableManager
from django.conf import settings
from django.db.models.signals import post_delete
from django.dispatch import receiver
import os
from datetime import datetime
from utils.logging import get_logger

# Logger'ı başlat
logger = get_logger(__name__)

def product_image_upload_path(instance, filename):
    """
    Ürün resimleri için dosya yolu oluşturur.
    Format: PRODUCT_IMAGES_DIR/YEAR/MONTH/DAY/filename
    """
    from django.conf import settings

    # Dosya uzantısını al
    ext = os.path.splitext(filename)[1]
    # Benzersiz bir dosya adı oluştur
    filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}{ext}"

    # Ürün resimleri klasörünü settings'den al
    product_images_dir = getattr(settings, 'PRODUCT_IMAGES_DIR', 'products')

    # Tam dosya yolunu oluştur
    return os.path.join(product_images_dir, datetime.now().strftime('%Y/%m/%d'), filename)

# Create your models here.

class Category(models.Model):
    """Merkezi kategori modeli - tüm uygulamalar tarafından kullanılır"""
    ad = models.CharField(max_length=100, verbose_name="Kategori Adı")
    slug = models.SlugField(unique=True, verbose_name="URL Yolu")
    aciklama = models.TextField(blank=True, verbose_name="Açıklama")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Oluşturulma Tarihi")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Güncellenme Tarihi")

    class Meta:
        verbose_name = "Kategori"
        verbose_name_plural = "Kategoriler"
        ordering = ['ad']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.ad)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.ad

    # Geriye uyumluluk için property'ler
    @property
    def name(self):
        """Geriye uyumluluk için name property'si"""
        return self.ad

    @property
    def description(self):
        """Geriye uyumluluk için description property'si"""
        return self.aciklama

class Product(models.Model):
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="Kategori")
    name = models.CharField(max_length=100, verbose_name="Ürün Adı")
    slug = models.SlugField(unique=True, verbose_name="URL Yolu")
    description = models.TextField(verbose_name="Açıklama")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Fiyat")
    stock_quantity = models.PositiveIntegerField(default=0, verbose_name="Stok Miktarı")
    is_available = models.BooleanField(default=True, verbose_name="Stokta Var mı?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ekleyen = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='urunler', verbose_name="Ekleyen Kullanıcı")
    tags = TaggableManager(blank=True, verbose_name="Etiketler", help_text="Ürünle ilgili etiketleri virgülle ayırarak girin")

    class Meta:
        verbose_name = "Ürün"
        verbose_name_plural = "Ürünler"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['-created_at'], name='product_created_idx'),
            models.Index(fields=['slug'], name='product_slug_idx'),
            models.Index(fields=['is_available', '-created_at'], name='product_available_idx'),
            models.Index(fields=['category', '-created_at'], name='product_category_idx'),
            models.Index(fields=['ekleyen', '-created_at'], name='product_user_idx'),
        ]

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    # delete metodu kaldırıldı - dosya silme işlemi post_delete signal'da yapılıyor

    def __str__(self):
        return self.name

class ProductImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to=product_image_upload_path, verbose_name="Ürün Görseli")
    is_main = models.BooleanField(default=False, verbose_name="Ana Görsel")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Ürün Görseli"
        verbose_name_plural = "Ürün Görselleri"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.id}"

    # delete metodu kaldırıldı - dosya silme işlemi post_delete signal'da yapılıyor


# Signal handlers
@receiver(post_delete, sender=ProductImage)
def product_image_post_delete(sender, instance, **kwargs):
    """
    ProductImage silindikten sonra dosyayı güvenli bir şekilde sil.
    Bu yaklaşım memory-efficient'tir ve CASCADE ile otomatik çalışır.
    """
    try:
        if instance.image:
            try:
                # Dosya yolunu güvenli şekilde al
                file_path = instance.image.path

                # Dosyayı güvenli bir şekilde sil
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"Ürün görseli silindi: {file_path}", extra={
                        'instance_id': getattr(instance, 'id', 'None'),
                        'product_id': getattr(instance.product, 'id', 'None') if instance.product else 'None',
                        'file_path': file_path,
                        'operation': 'post_delete_signal'
                    })

            except (ValueError, OSError, AttributeError) as file_error:
                # Dosya yolu veya dosya erişim hatası
                logger.warning(f"Dosya yolu erişim hatası (normal olabilir): {file_error}", extra={
                    'instance_id': getattr(instance, 'id', 'None'),
                    'product_id': getattr(instance.product, 'id', 'None') if instance.product else 'None',
                    'image_name': getattr(instance.image, 'name', 'None') if instance.image else 'None',
                    'error': str(file_error),
                    'operation': 'post_delete_signal'
                })

    except Exception as e:
        # Genel hata durumunda log'la ama işlemi durdurma
        logger.error(f"ProductImage post_delete signal genel hatası: {e}", extra={
            'instance_id': getattr(instance, 'id', 'None'),
            'product_id': getattr(instance.product, 'id', 'None') if instance.product else 'None',
            'error': str(e),
            'operation': 'post_delete_signal'
        })
