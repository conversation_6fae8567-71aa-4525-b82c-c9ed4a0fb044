{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - <PERSON><PERSON><PERSON> Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
<link rel="stylesheet" href="{% static 'urunler/css/urun_form.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON>ölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">{{ title }}</h1>
            <p class="modern-subtitle">
                {% if urun %}
                {{ urun.name }} ürünün<PERSON> düzenleyin
                {% else %}
                Mağazaya yeni bir ürün e<PERSON>in
                {% endif %}
            </p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-box-seam me-2"></i>Ürün Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <form method="post" enctype="multipart/form-data" class="calisma-form">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Ürün Adı -->
                                <div class="col-md-8">
                                    <div class="form__group">
                                        <label for="{{ form.name.id_for_label }}" class="form__label">{{ form.name.label }} <span class="text-danger">*</span></label>
                                        {{ form.name }}
                                        {% if form.name.help_text %}
                                        <small class="form__help">{{ form.name.help_text }}</small>
                                        {% endif %}
                                        {% if form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.name.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-4">
                                    <div class="form__group">
                                        <label for="{{ form.category.id_for_label }}" class="form__label">{{ form.category.label }}</label>
                                        {{ form.category }}
                                        {% if form.category.help_text %}
                                        <small class="form__help">{{ form.category.help_text }}</small>
                                        {% endif %}
                                        {% if form.category.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.category.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.description.id_for_label }}" class="form__label">{{ form.description.label }} <span class="text-danger">*</span></label>
                                        {{ form.description }}
                                        {% if form.description.help_text %}
                                        <small class="form__help">{{ form.description.help_text }}</small>
                                        {% endif %}
                                        {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.description.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Fiyat -->
                                <div class="col-md-4">
                                    <div class="form__group">
                                        <label for="{{ form.price.id_for_label }}" class="form__label">{{ form.price.label }} <span class="text-danger">*</span></label>
                                        {{ form.price }}
                                        {% if form.price.help_text %}
                                        <small class="form__help">{{ form.price.help_text }}</small>
                                        {% endif %}
                                        {% if form.price.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.price.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Stok Miktarı -->
                                <div class="col-md-4">
                                    <div class="form__group">
                                        <label for="{{ form.stock_quantity.id_for_label }}" class="form__label">{{ form.stock_quantity.label }}</label>
                                        {{ form.stock_quantity }}
                                        {% if form.stock_quantity.help_text %}
                                        <small class="form__help">{{ form.stock_quantity.help_text }}</small>
                                        {% endif %}
                                        {% if form.stock_quantity.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.stock_quantity.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Durum -->
                                <div class="col-md-4">
                                    <div class="form__group">
                                        <div class="form__check mt-4">
                                            {{ form.is_available }}
                                            <label for="{{ form.is_available.id_for_label }}" class="form__check-label">{{ form.is_available.label }}</label>
                                        </div>
                                        <small class="form__help">Ürün satışa açık mı?</small>
                                        {% if form.is_available.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.is_available.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.tags.id_for_label }}" class="form__label">{{ form.tags.label }}</label>
                                        {{ form.tags }}
                                        <small class="form__help">Etiketleri virgülle ayırarak girin (örn: ahşap, el yapımı, vintage)</small>
                                        {% if form.tags.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tags.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="modern-btn">
                                            <i class="bi bi-check-circle me-2"></i>{% if urun %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% if urun %}{% url 'urunler:urun_detay' urun.id urun.slug %}{% else %}{% url 'urunler:urun_listesi' %}{% endif %}" class="modern-btn modern-btn-outline ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if urun %}
                                        <a href="#" class="modern-btn modern-btn-danger ms-auto" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="bi bi-trash me-2"></i>Ürünü Sil
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Ürün Fotoğrafları -->
                <div class="modern-card mt-4 wow fadeInUp" data-wow-delay="0.4s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-images me-2"></i>Ürün Fotoğrafları</h3>
                    </div>
                    <div class="modern-card-body">
                        {{ formset.management_form }}

                        <div class="photo-formset">
                            {% for foto_form in formset %}
                            <div class="photo-form-item mb-3">
                                {% if foto_form.instance.pk %}
                                <div class="photo-preview mb-3">
                                    <img src="{{ foto_form.instance.image.url }}" alt="Ürün Fotoğrafı" class="img-fluid">
                                    {% if foto_form.instance.is_main %}
                                    <span class="main-photo-badge">Ana Görsel</span>
                                    {% endif %}
                                </div>
                                {% endif %}

                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        {{ foto_form.id }}
                                        {{ foto_form.image }}
                                        {% if foto_form.image.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ foto_form.image.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            {{ foto_form.is_main }}
                                            <label for="{{ foto_form.is_main.id_for_label }}" class="form__check-label">Ana Görsel</label>
                                        </div>
                                        {% if foto_form.instance.pk %}
                                        <div class="form-check mt-2">
                                            {{ foto_form.DELETE }}
                                            <label for="{{ foto_form.DELETE.id_for_label }}" class="form-check-label text-danger">Sil</label>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                {% for hidden in foto_form.hidden_fields %}
                                {{ hidden }}
                                {% endfor %}
                            </div>
                            {% endfor %}
                        </div>

                        <button type="button" id="add-photo" class="modern-btn modern-btn-sm mt-2">
                            <i class="bi bi-plus-circle me-2"></i>Fotoğraf Ekle
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if urun %}
<!-- Silme Onay Modalı -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal__content">
            <div class="modal__header">
                <h5 class="modal__title" id="deleteModalLabel"><i class="bi bi-exclamation-triangle-fill me-2"></i>Ürünü Sil</h5>
                <button type="button" class="modal__close" data-bs-dismiss="modal" aria-label="Kapat"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal__body">
                <p>Bu ürünü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                <p class="fw-bold mt-2"><strong>{{ urun.name }}</strong></p>
            </div>
            <div class="modal__footer">
                <button type="button" class="button button--outline" data-bs-dismiss="modal">İptal</button>
                <form action="{% url 'urunler:urun_sil' urun.id %}" method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="button button--danger">Evet, Sil</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // WOW.js başlatma
        new WOW().init();

        // Form elemanlarını düzenleme
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(control => {
            control.classList.add('form-control');

            if (control.tagName === 'SELECT') {
                control.classList.add('form-select');
            }
        });

        // Fotoğraf ekleme fonksiyonu
        const addPhotoBtn = document.getElementById('add-photo');
        const photoFormset = document.querySelector('.photo-formset');
        const totalForms = document.getElementById('id_fotograflar-TOTAL_FORMS');

        if (addPhotoBtn && photoFormset && totalForms) {
            addPhotoBtn.addEventListener('click', function() {
                const formCount = parseInt(totalForms.value);
                const newForm = photoFormset.querySelector('.photo-form-item').cloneNode(true);

                // Form elemanlarını temizle ve ID'leri güncelle
                const inputs = newForm.querySelectorAll('input');
                inputs.forEach(input => {
                    if (input.getAttribute('name')) {
                        const name = input.getAttribute('name').replace(/\d+/, formCount);
                        input.setAttribute('name', name);
                    }

                    if (input.getAttribute('id')) {
                        const id = input.getAttribute('id').replace(/\d+/, formCount);
                        input.setAttribute('id', id);
                    }

                    if (input.type === 'file') {
                        input.value = '';
                    } else if (input.type === 'checkbox') {
                        input.checked = false;
                    }
                });

                // Önizleme resmini kaldır
                const imgPreview = newForm.querySelector('.photo-preview');
                if (imgPreview) {
                    imgPreview.remove();
                }

                // Hata mesajlarını temizle
                const errorMessages = newForm.querySelectorAll('.invalid-feedback');
                errorMessages.forEach(error => {
                    error.remove();
                });

                photoFormset.appendChild(newForm);
                totalForms.value = formCount + 1;

                // Yeni eklenen form elemanına odaklan
                const newFileInput = newForm.querySelector('input[type="file"]');
                if (newFileInput) {
                    newFileInput.focus();
                }
            });
        }
    });
</script>
{% endblock %}
