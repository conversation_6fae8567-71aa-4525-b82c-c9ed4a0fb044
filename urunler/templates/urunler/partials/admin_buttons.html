{% comment %}
<PERSON><PERSON><PERSON><PERSON>ö<PERSON> but<PERSON> partial template'i
Kullanım: {% include 'urunler/partials/admin_buttons.html' with product=product %}
{% endcomment %}

{% load product_tags %}

{% if permissions.can_add_product or permissions.can_edit_product or permissions.can_delete_product %}
<div class="admin-buttons-container mt-3">
    <div class="btn-group" role="group" aria-label="Ürün Yönetimi">
        
        {% if permissions.can_add_product %}
        <a href="{% url 'urunler:urun_ekle' %}" class="btn btn-success btn-sm">
            <i class="bi bi-plus-circle me-1"></i>
            <PERSON><PERSON><PERSON>
        </a>
        {% endif %}
        
        {% if product and product.id and product.slug and permissions.can_edit_product %}
        <a href="{% url 'urunler:urun_duzenle' product.id product.slug %}" class="btn btn-primary btn-sm">
            <i class="bi bi-pencil me-1"></i>
            <PERSON><PERSON><PERSON><PERSON>
        </a>
        {% endif %}
        
        {% if product and product.id and product.slug and permissions.can_delete_product %}
        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteProductModal">
            <i class="bi bi-trash me-1"></i>
            Sil
        </button>
        {% endif %}
        
        {% if product and permissions.can_manage_images %}
        <button type="button" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#manageImagesModal">
            <i class="bi bi-images me-1"></i>
            Resimleri Yönet
        </button>
        {% endif %}
        
    </div>
    
    {% if permissions.is_admin %}
    <small class="text-muted d-block mt-1">
        <i class="bi bi-shield-check me-1"></i>
        Yönetici yetkileriniz ile bu işlemleri yapabilirsiniz.
    </small>
    {% endif %}
</div>
{% endif %}
