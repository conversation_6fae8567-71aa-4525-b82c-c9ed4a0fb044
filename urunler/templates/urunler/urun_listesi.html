{% extends 'base.html' %}
{% load static %}
{% load urun_filters %}

{% block title %}Ürünlerimiz - Küp Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'urunler/css/urun_liste.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5 urunler">
    <div class="container modern-container">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Bölümü -->
        <div class="modern-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="gradient-heading display-4 fw-bold mb-3">Ürünlerimiz</h1>
            <p class="lead text-muted">"Topraktan Hayale, Hayalden Hayat ve Sanata..."</p>
        </div>

        <!-- Filtreler -->
        <!-- Filtreler -->
        <!-- Filtrel<PERSON> ve <PERSON> (<PERSON>) -->
        <div class="row mb-5">
            <!-- <PERSON><PERSON><PERSON>lt<PERSON> -->
            <div class="col-md-4 wow fadeInUp" data-wow-delay="0.3s">
                <div class="filtre-paneli h-100">
                    <h4 class="filtre-paneli__baslik"><i class="bi bi-grid"></i>Kategoriler</h4>
                    <div class="modern-tag-cloud">
                        <a href="{% url 'urunler:urun_listesi' %}" class="modern-tag {% if not category %}active{% endif %}">Tümü</a>
                        {% for cat in categories %}
                        <a href="{% url 'urunler:urun_listesi_by_category' cat.slug %}" class="modern-tag {% if category.slug == cat.slug %}active{% endif %}">{{ cat.name }}</a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Etiket Filtreleri -->
            <div class="col-md-4 wow fadeInUp" data-wow-delay="0.4s">
                <div class="filtre-paneli h-100">
                    <h4 class="filtre-paneli__baslik"><i class="bi bi-tags"></i>Etiketler</h4>
                    <div class="modern-tag-cloud">
                        <a href="{% url 'urunler:urun_listesi' %}{% if category %}{{ category.slug }}/{% endif %}" class="modern-tag {% if not current_tag %}active{% endif %}">Tümü</a>
                        {% for tag in tags %}
                        <a href="{% url 'urunler:urun_listesi' %}{% if category %}{{ category.slug }}/{% endif %}?tag={{ tag.slug }}" class="modern-tag {% if current_tag == tag.slug %}active{% endif %}">{{ tag.name }}</a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Arama Formu -->
            <div class="col-md-4">
                <div class="filtre-paneli h-100">
                    <h4 class="filtre-paneli__baslik"><i class="bi bi-search"></i>Ürün Ara</h4>
                    <form action="{% url 'urunler:urun_listesi' %}" method="get" class="form-modern">
                        <div class="input-group-modern">
                            <input type="text" name="q" class="form-control-modern"
                                   placeholder="Ürün adı, açıklama veya etiket..."
                                   value="{{ search_query|default:'' }}" aria-label="Ürün ara">
                            <button type="submit" class="button button--primary" aria-label="Ara">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        {% if current_tag %}
                        <input type="hidden" name="tag" value="{{ current_tag }}">
                        {% endif %}
                    </form>


                </div>
            </div>
        </div>

        {% if search_query %}
        <!-- Arama Sonuçları Başlığı -->
        <div class="alert mb-4 wow fadeIn arama-uyarisi" data-wow-delay="0.2s">
            <div class="d-flex align-items-center">
                <i class="bi bi-search me-3 fs-4"></i>
                <div>
                    <h5 class="mb-1">"{{ search_query }}" için arama sonuçları</h5>
                    <p class="mb-0">{{ products.count }} sonuç bulundu</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Ürün Listesi -->
        <div class="row g-4 product-grid">
            {% for product in products %}
            <div class="col-lg-4 col-md-6 product-item filter-{{ product.category.slug }} wow fadeInUp" data-wow-delay="0.3s">
                <div class="modern-card h-100">
                    <div class="image-container">
                        <a href="{% url 'urunler:urun_detay' product.id product.slug %}">
                            {% if product.images.first %}
                                <img src="{{ product.images.first.image.url }}" alt="{{ product.name }}" class="modern-image">
                            {% else %}
                                <div class="no-image-placeholder d-flex align-items-center justify-content-center">
                                    <i class="bi bi-image fs-1 text-muted"></i>
                                </div>
                            {% endif %}
                        </a>
                        {% if product.category %}
                        <span class="modern-badge-corner">{{ product.category.name }}</span>
                        {% endif %}
                        <div class="image-overlay">
                            <div class="d-flex gap-2 justify-content-center">
                                {% if product.images.first %}
                                <a href="{{ product.images.first.image.url }}" class="modern-btn-circle" title="{{ product.name }}" data-gallery="portfolio-gallery-{{ product.category.slug }}">
                                    <i class="bi bi-zoom-in"></i>
                                </a>
                                {% endif %}
                                <a href="{% url 'urunler:urun_detay' product.id product.slug %}" class="modern-btn-circle modern-btn-link">
                                    <i class="bi bi-link-45deg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <h3 class="modern-card-title">
                            <a href="{% url 'urunler:urun_detay' product.id product.slug %}" class="text-decoration-none text-inherit">{{ product.name|highlight:search_query }}</a>
                        </h3>
                        {% if product.tags.all %}
                        <div class="modern-tag-cloud mt-2">
                            {% for tag in product.tags.all %}
                            <a href="{% url 'urunler:urun_listesi' %}?tag={{ tag.slug }}" class="modern-tag">{{ tag.name|highlight:search_query }}</a>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="modern-card-footer">
                        {% if product.price %}
                        <div class="modern-card-price">{{ product.price }} TL</div>
                        {% else %}
                        <div class="modern-card-price">Fiyat Sorunuz</div>
                        {% endif %}
                        <div class="modern-card-actions">
                            <a href="{% url 'urunler:urun_detay' product.id product.slug %}" class="button button--primary button--sm">Detaylar</a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 wow fadeIn" data-wow-delay="0.3s">
                <div class="modern-empty-state">
                    <i class="bi bi-box-seam display-1 text-muted mb-4"></i>
                    <h3 class="mt-4">Henüz Ürün Eklenmemiş</h3>
                    <p class="text-muted mb-4">Mağazanızda görüntülenecek ürün bulunmuyor. İlk ürününüzü ekleyerek başlayın!</p>

                    {% if user.is_authenticated and user.is_staff %}
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{% url 'admin:urunler_product_add' %}" class="btn-modern btn-primary-modern btn-icon-modern">
                            <i class="bi bi-plus-circle"></i>
                            <span>İlk Ürünü Ekle</span>
                        </a>
                        <a href="{% url 'admin:urunler_product_changelist' %}" class="btn-modern btn-outline-modern btn-icon-modern">
                            <i class="bi bi-gear"></i>
                            <span>Ürün Yönetimi</span>
                        </a>
                    </div>
                    {% else %}
                    <p class="text-muted small">Ürün eklemek için admin paneline giriş yapın.</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="{% static 'urunler/js/urun_liste.js' %}"></script>
<script src="https://unpkg.com/isotope-layout@3/dist/isotope.pkgd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" />
<script>
    $(document).ready(function() {
        // Isotope başlatma
        var $grid = $('.product-grid').isotope({
            itemSelector: '.product-item',
            layoutMode: 'fitRows'
        });

        // Filtre butonları
        $('.modern-tag').on('click', function() {
            var filterValue = $(this).attr('data-filter');

            // Aktif sınıfı güncelle
            $('.modern-tag').removeClass('active');
            $(this).addClass('active');

            if (filterValue === '*') {
                $grid.isotope({ filter: '*' });
            } else {
                $grid.isotope({ filter: filterValue });
            }
        });

        // GLightbox başlatma - sadece zoom butonları için
        const lightbox = GLightbox({
            selector: '.modern-btn-circle[data-gallery]',
            touchNavigation: true,
            loop: true,
            autoplayVideos: true
        });

        // Link butonlarının normal davranmasını sağla
        $('.modern-btn-link').on('click', function(e) {
            e.stopPropagation();
            // Normal link davranışı devam etsin
        });
    });
</script>
{% endblock %}
