{% extends 'base.html' %}
{% load static %}

{% block title %}{{ product.name }} - <PERSON><PERSON><PERSON> Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css">

{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container modern-container">
        <!-- <PERSON><PERSON><PERSON><PERSON><PERSON>ölümü -->
        <div class="modern-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="gradient-heading display-4 fw-bold mb-3">Ürün Detayları</h1>
            <p class="lead text-muted">"{{ urun.name }}" ürün<PERSON> hakkında detaylı bilgiler</p>
        </div>

        <div class="row g-4">
            <!-- <PERSON>r<PERSON><PERSON> Görselleri -->
            <div class="col-lg-8 wow fadeInLeft" data-wow-delay="0.3s">
                <div class="modern-card mb-4">
                    <div class="swiper product-slider">
                        <div class="swiper-wrapper">
                            {% for image in product.images.all %}
                            <div class="swiper-slide">
                                <div class="image-container">
                                    <img src="{{ image.image.url }}" alt="{{ product.name }}" class="modern-image">
                                    <a href="{{ image.image.url }}" class="modern-btn-circle position-absolute top-50 start-50 translate-middle" title="{{ product.name }}" data-gallery="product-gallery">
                                        <i class="bi bi-zoom-in"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>

                <!-- Ürün Açıklaması -->
                <div class="modern-card wow fadeInUp" data-wow-delay="0.4s">
                    <div class="modern-card-header">
                        <h3 class="modern-title"><i class="bi bi-info-circle me-2"></i>Ürün Açıklaması</h3>
                    </div>
                    <div class="modern-card-body">
                        <div class="product-description">
                            {{ product.description|linebreaks }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ürün Bilgileri -->
            <div class="col-lg-4">
                <div class="card mb-4 wow fadeInRight urun-bilgi-karti" data-wow-delay="0.3s">
                    <div class="card__header autumn-card-header">
                        <h3 class="card__title"><i class="bi bi-info-square me-2"></i>Ürün Bilgileri</h3>
                    </div>
                    <div class="card__body p-0">
                        <div class="modern-info-list">
                            <div class="modern-info-item modern-list-item">
                                <strong>Kategori</strong>
                                <span>{{ product.category.name }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Fiyat</strong>
                                <span class="text-brown fw-bold">₺{{ product.price }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Stok Durumu</strong>
                                <span class="{% if product.is_available %}text-moss{% else %}text-brown{% endif %}">
                                    {% if product.is_available %}Stokta Var{% else %}Stokta Yok{% endif %}
                                </span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Stok Miktarı</strong>
                                <span>{{ product.stock_quantity|default:0 }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Eklenme Tarihi</strong>
                                <span>{{ product.created_at|date:"d.m.Y" }}</span>
                            </div>
                            {% if product.ekleyen %}
                            <div class="modern-info-item modern-list-item">
                                <strong>Ekleyen</strong>
                                <span>
                                    <a href="{% url 'uyelik:kullanici_profil' username=product.ekleyen.username %}" class="modern-link">
                                        <i class="bi bi-person-fill text-navy"></i> {{ product.ekleyen.username }}
                                    </a>
                                </span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Yönetim Paneli (Ürün Yöneticileri ve Üstü) -->
                        {% if user_perms.can_add_product or user_perms.can_edit_product %}
                <div class="card admin-panel-card mb-4 wow fadeInRight" data-wow-delay="0.6s">
                    <div class="card__header autumn-card-header-gradient">
                        <h3 class="card__title"><i class="bi bi-gear-fill me-2"></i>Yönetim Paneli</h3>
                    </div>
                    <div class="card__body p-0">
                        <!-- Yönetim Menüsü -->
                        <div class="admin-menu">
                            <!-- Yeni Ürün Ekle -->
                            <a href="{% url 'urunler:urun_ekle' %}" class="admin-menu-item">
                                <div class="admin-menu-icon">
                                    <i class="bi bi-plus-circle-fill" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Yeni Ürün Ekle</h5>
                                    <p class="text-muted admin-menu-description">Mağazaya yeni bir ürün ekleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Bu Ürünü Düzenle -->
                            {% if product and product.id and product.slug %}
                            <a href="{% url 'urunler:urun_duzenle' product.id product.slug %}" class="admin-menu-item">
                                <div class="admin-menu-icon" style="background-color: #2c5aa0;">
                                    <i class="bi bi-pencil-square" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Bu Ürünü Düzenle</h5>
                                    <p class="text-muted admin-menu-description">Mevcut ürünün bilgilerini güncelleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                            {% endif %}

                            <!-- Ürünü Sil -->
                            {% if product and product.id and product.slug %}
                            <a href="#" class="admin-menu-item delete-item" onclick="confirmDelete(event, '{% url 'urunler:urun_sil' product.id product.slug %}', 'urun')">
                                <div class="admin-menu-icon" style="background-color: #e74c3c;">
                                    <i class="bi bi-trash" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Bu Ürünü Sil</h5>
                                    <p class="text-muted admin-menu-description">Bu ürünü kalıcı olarak silin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                            {% endif %}

                            <!-- Ürün Listesine Dön -->
                            <a href="{% url 'urunler:urun_listesi' %}" class="admin-menu-item">
                                <div class="admin-menu-icon back-icon">
                                    <i class="bi bi-arrow-left-circle"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Ürün Listesine Dön</h5>
                                    <p class="text-muted admin-menu-description">Tüm ürünleri görüntüleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}

                        <!-- Satın Al Butonu -->
                        {% if urun.is_available %}
                        <div class="p-3 text-center">
                            <button class="button button--primary">
                                <i class="bi bi-bag-plus me-2"></i>Sepete Ekle
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Ürün Etiketleri -->
                <div class="card mb-4 wow fadeInRight etiketler-karti" data-wow-delay="0.4s">
                    <div class="card__header autumn-card-header">
                        <h3 class="card__title"><i class="bi bi-tags me-2"></i>Etiketler</h3>
                    </div>
                    <div class="card__body">
                        <div class="modern-tag-cloud">
                            {% for tag in urun.tags.all %}
                            <a href="{% url 'urunler:urun_listesi' %}?tag={{ tag.slug }}" class="modern-tag">
                                {{ tag.name }}
                            </a>
                            {% empty %}
                            <span class="text-muted">Bu ürün için henüz etiket eklenmemiş</span>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Benzer Ürünler -->
                <div class="card wow fadeInRight benzer-urunler-karti" data-wow-delay="0.5s">
                    <div class="card__header autumn-card-header-alt">
                        <h3 class="card__title"><i class="bi bi-grid me-2"></i>Benzer Ürünler</h3>
                        <div class="small text-muted mt-1">Gelişmiş benzerlik algoritması ile</div>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for similar_product in similar_products %}
                            <li>
                                <a href="{% url 'urunler:urun_detay' similar_product.id similar_product.slug %}" class="modern-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        <img src="{{ similar_product.images.first.image.url }}" alt="{{ similar_product.name }}">
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ similar_product.name }}</h6>
                                        <span class="text-primary fw-bold small">₺{{ similar_product.price }}</span>

                                        <!-- Benzerlik Puanı -->
                                        <div class="mt-1">
                                            <div class="similarity-score" title="Benzerlik Puanı">
                                                {% with score=similar_product.similarity_score|floatformat:1 %}
                                                    {% if score >= 6 %}
                                                        <span class="badge bg-success">Çok Benzer ({{ score }})</span>
                                                    {% elif score >= 3 %}
                                                        <span class="badge bg-moss">Benzer ({{ score }})</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">Az Benzer ({{ score }})</span>
                                                    {% endif %}
                                                {% endwith %}
                                            </div>
                                        </div>

                                        <!-- Ortak Etiketler -->
                                        {% if similar_product.tags.all %}
                                        <div class="mt-1">
                                            {% for tag in similar_product.tags.all %}
                                                {% if tag in product.tags.all %}
                                                <span class="badge bg-moss text-light">{{ tag.name }}</span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </a>
                            </li>
                            {% empty %}
                            <li class="text-center p-3">
                                <span class="text-muted">Benzer ürün bulunamadı</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if user_perms.can_edit_product or user_perms.can_delete_product %}


    {% endif %}
</section>
{% endblock %}



{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Swiper başlatma
        const swiper = new Swiper('.product-slider', {
            loop: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            autoplay: {
                delay: 5000,
                disableOnInteraction: false
            }
        });

        // GLightbox başlatma
        const lightbox = GLightbox({
            selector: '.modern-btn-circle',
            touchNavigation: true,
            loop: true,
            autoplayVideos: true
        });

        // Fotoğraf önizleme
        const photoInput = document.getElementById('id_image');
        const previewContainer = document.getElementById('preview-container');
        const imagePreview = document.getElementById('image-preview');

        if (photoInput) {
            photoInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        previewContainer.classList.remove('d-none');
                    }

                    reader.readAsDataURL(this.files[0]);
                } else {
                    previewContainer.classList.add('d-none');
                }
            });
        }

        // Modal kapatıldığında formu sıfırla
        const addPhotoModal = document.getElementById('addPhotoModal');
        if (addPhotoModal) {
            addPhotoModal.addEventListener('hidden.bs.modal', function() {
                const form = this.querySelector('form');
                if (form) {
                    form.reset();
                    previewContainer.classList.add('d-none');
                }
            });
        }

        // Custom confirm dialog artık global olarak yüklü
    });
</script>
{% endblock %}
