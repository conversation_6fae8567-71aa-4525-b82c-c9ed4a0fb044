{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'assets/css/admin-custom.css' %}">
<style>
    .stat-card {
        background-color: white;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .stat-card h3 {
        margin-top: 0;
        margin-bottom: 5px;
        font-size: 1rem;
        color: #666;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        color: var(--color-navy);
    }

    .product-list {
        margin-top: 20px;
    }

    .product-list h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1rem;
        color: #666;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }

    .product-card {
        background-color: white;
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }

    .product-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .product-image {
        height: 100px;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-info {
        padding: 10px;
    }

    .product-name {
        margin: 0;
        font-size: 0.9rem;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .product-category {
        font-size: 0.8rem;
        color: #666;
        margin-top: 5px;
    }

    .view-all-link {
        display: block;
        text-align: center;
        margin-top: 15px;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 5px;
        text-decoration: none;
        color: var(--color-navy);
        font-weight: 600;
        transition: all 0.2s ease;
    }

    .view-all-link:hover {
        background-color: #e9ecef;
    }

    .similar-tags {
        margin-top: 20px;
    }

    .similar-tags h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1rem;
        color: #666;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
    }

    .tag-item {
        display: inline-block;
        margin: 5px;
        padding: 5px 10px;
        background-color: var(--color-moss);
        color: white;
        border-radius: 15px;
        font-size: 0.9rem;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .tag-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    }

    .tag-item .count {
        background-color: white;
        color: var(--color-moss);
        border-radius: 10px;
        padding: 2px 6px;
        margin-left: 5px;
        font-size: 0.8rem;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
{% if original %}
<div class="tag-info">
    <h2>
        Etiket Bilgileri
        <span class="tag-badge">{{ original.name }}</span>
    </h2>
    
    <div class="tag-stats">
        <div class="stat-card">
            <h3>Ürün Sayısı</h3>
            <div class="stat-value">{{ product_count }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Oluşturulma</h3>
            <div class="stat-value" style="font-size: 1rem;">{{ original.created_at|date:"d.m.Y" }}</div>
        </div>
    </div>
    
    {% if products %}
    <div class="product-list">
        <h3>Bu Etiketi Kullanan Ürünler ({{ product_count }})</h3>
        
        <div class="product-grid">
            {% for product in products|slice:":8" %}
            <a href="{% url 'admin:urunler_product_change' product.id %}" class="product-card">
                <div class="product-image">
                    {% if product.images.exists %}
                    <img src="{{ product.images.first.image.url }}" alt="{{ product.name }}">
                    {% else %}
                    <i class="fas fa-image" style="color: #ddd; font-size: 2rem;"></i>
                    {% endif %}
                </div>
                <div class="product-info">
                    <h4 class="product-name">{{ product.name }}</h4>
                    <div class="product-category">{{ product.category.name }}</div>
                </div>
            </a>
            {% endfor %}
        </div>
        
        {% if product_count > 8 %}
        <a href="{% url 'admin:urunler_product_changelist' %}?tags__name__exact={{ original.name }}" class="view-all-link">
            Tüm Ürünleri Görüntüle
        </a>
        {% endif %}
    </div>
    {% endif %}
    
    {% if similar_tags %}
    <div class="similar-tags">
        <h3>Benzer Etiketler</h3>
        
        {% for tag in similar_tags %}
        <a href="{% url 'admin:taggit_tag_change' tag.id %}" class="tag-item">
            {{ tag.name }} <span class="count">{{ tag.count }}</span>
        </a>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endif %}

{{ block.super }}
{% endblock %}
