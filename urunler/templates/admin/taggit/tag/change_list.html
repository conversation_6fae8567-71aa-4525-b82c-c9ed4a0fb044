{% extends "admin/change_list.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'assets/css/admin-custom.css' %}">
<style>
    .stat-card {
        background-color: white;
        border-radius: 5px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .stat-card h3 {
        margin-top: 0;
        margin-bottom: 5px;
        font-size: 1rem;
        color: #666;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        color: var(--color-navy);
    }

    .tag-cloud {
        margin-top: 20px;
    }

    .tag-cloud h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1rem;
        color: #666;
    }

    .tag-item {
        display: inline-block;
        margin: 5px;
        padding: 5px 10px;
        background-color: var(--color-moss);
        color: white;
        border-radius: 15px;
        font-size: 0.9rem;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .tag-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    }

    .tag-item .count {
        background-color: white;
        color: var(--color-moss);
        border-radius: 10px;
        padding: 2px 6px;
        margin-left: 5px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .tag-help {
        margin-top: 20px;
        padding: 15px;
        background-color: #fff8e1;
        border-left: 4px solid #ffb74d;
        border-radius: 0 5px 5px 0;
    }

    .tag-help h3 {
        margin-top: 0;
        color: #f57c00;
        font-size: 1rem;
    }

    .tag-help ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .tag-help li {
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="tag-stats">
    <h2>Etiket İstatistikleri</h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Toplam Etiket</h3>
            <div class="stat-value">{{ tag_count }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Toplam Ürün</h3>
            <div class="stat-value">{{ product_count }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Etiketli Ürün</h3>
            <div class="stat-value">{{ tagged_product_count }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Ortalama Etiket/Ürün</h3>
            <div class="stat-value">{{ avg_tags_per_product|floatformat:1 }}</div>
        </div>
    </div>
    
    <div class="tag-cloud">
        <h3>Popüler Etiketler</h3>
        {% for tag in popular_tags %}
        <a href="{% url 'admin:taggit_tag_change' tag.id %}" class="tag-item">
            {{ tag.name }} <span class="count">{{ tag.count }}</span>
        </a>
        {% endfor %}
    </div>
    
    <div class="tag-help">
        <h3>Etiket Yönetimi İpuçları</h3>
        <ul>
            <li>Etiketleri düzenlemek için üzerlerine tıklayın.</li>
            <li>Benzer etiketleri birleştirmek için birden fazla etiket seçin ve "Seçili etiketleri birleştir" eylemini kullanın.</li>
            <li>Bir etiketle ilişkili ürünleri görmek için "Ürünleri Gör" butonuna tıklayın.</li>
            <li>Etiketleri virgülle ayırarak ürünlere ekleyebilirsiniz.</li>
        </ul>
    </div>
</div>

{{ block.super }}
{% endblock %}
