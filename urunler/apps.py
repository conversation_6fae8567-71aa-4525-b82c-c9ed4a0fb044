from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class UrunlerConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'urunler'
    verbose_name = _('<PERSON><PERSON><PERSON><PERSON>')

    def ready(self):
        """
        Uygulama hazır olduğunda çalışacak kod.
        Taggit'in admin kaydını bizim özel admin sınıfımızla değiştiriyoruz.
        """
        # İçe aktarmaları burada yapıyoruz çünkü Django uygulamaları yüklenirken
        # döngüsel içe aktarma sorunlarını önlemek için
        from django.contrib import admin
        from taggit.models import Tag, TaggedItem

        # Taggit modellerinin verbose_name'lerini değiştir
        Tag._meta.verbose_name = _('Etiket')
        Tag._meta.verbose_name_plural = _('Etiketler')
        TaggedItem._meta.verbose_name = _('Etiketlenmiş Öğe')
        TaggedItem._meta.verbose_name_plural = _('Etiketlenmiş Öğeler')

        # Taggit'in kendi admin kaydını kaldır
        try:
            admin.site.unregister(Tag)
        except admin.sites.NotRegistered:
            pass

        # Bizim özel admin sınıfımızı içe aktar ve kaydet
        from .admin import TagAdmin
        admin.site.register(Tag, TagAdmin)
