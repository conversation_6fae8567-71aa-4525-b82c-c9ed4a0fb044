from django import forms
from django.core.exceptions import ValidationError
from django.forms import inlineformset_factory
from decimal import Decimal, InvalidOperation
from .models import Product, ProductImage, Category


class TurkishPriceInput(forms.TextInput):
    """
    Türkçe fiyat girişi için özel widget.
    Virgül ve nokta desteği sağlar.
    """

    def __init__(self, attrs=None):
        default_attrs = {
            'class': 'turkish-price-input',
            'placeholder': 'Fiyat (örn: 100,50 veya 100.50)',
            'pattern': r'[0-9]+([,.][0-9]{1,2})?',
            'title': 'Fiyat giriniz. Ondalık ayırıcı olarak virgül (,) veya nokta (.) kullanabilirsiniz.'
        }
        if attrs:
            # Gelen class'ı mevcut olana ekle, üzerine yazma
            if 'class' in attrs and 'class' in default_attrs:
                attrs['class'] = default_attrs['class'] + ' ' + attrs['class']
            default_attrs.update(attrs)
        super().__init__(default_attrs)

    class Media:
        js = ('js/turkish-price-input.js',)
        css = {
            'all': ('css/turkish-price-input.css',)
        }


class ProductForm(forms.ModelForm):
    """Ürün ekleme ve düzenleme formu."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].required = False

        if self.instance and self.instance.pk:
            tag_names = [tag.name for tag in self.instance.tags.all()]
            self.initial['tags'] = ', '.join(tag_names)

        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = ''
                    if isinstance(widget, forms.Textarea):
                        invalid_class = 'form__textarea--invalid'
                    elif isinstance(widget, forms.Select):
                        invalid_class = 'form__select--invalid'
                    elif isinstance(widget, forms.CheckboxInput):
                        invalid_class = 'form__check-input--invalid'
                    else:
                        invalid_class = 'form__input--invalid'

                    if invalid_class and invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

    class Meta:
        model = Product
        fields = ['name', 'category', 'description', 'price', 'stock_quantity', 'is_available', 'tags']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'Ürün adı', 'class': 'form__input'}),
            'category': forms.Select(attrs={'class': 'form__select'}),
            'description': forms.Textarea(attrs={'rows': 5, 'placeholder': 'Ürün açıklaması', 'class': 'form__textarea'}),
            'price': TurkishPriceInput(attrs={'class': 'form__input'}),
            'stock_quantity': forms.NumberInput(attrs={'placeholder': 'Stok miktarı', 'min': 0, 'class': 'form__input'}),
            'is_available': forms.CheckboxInput(attrs={'class': 'form__check-input'}),
            'tags': forms.TextInput(attrs={'placeholder': 'Etiketleri virgülle ayırarak girin', 'class': 'form__input'}),
        }
        labels = {
            'name': 'Ürün Adı',
            'category': 'Kategori',
            'description': 'Açıklama',
            'price': 'Fiyat (₺)',
            'stock_quantity': 'Stok Miktarı',
            'is_available': 'Stokta Var',
            'tags': 'Etiketler',
        }
        help_texts = {
            'name': 'Ürünün tam adını girin.',
            'category': 'Ürünün ait olduğu kategoriyi seçin.',
            'description': 'Ürün hakkında detaylı bilgi verin.',
            'price': 'Ürünün satış fiyatını TL cinsinden girin. Ondalık ayırıcı olarak virgül (,) veya nokta (.) kullanabilirsiniz.',
            'stock_quantity': 'Mevcut stok miktarını girin.',
            'is_available': 'Ürün satışa hazır ise işaretleyin.',
            'tags': 'Ürünle ilgili etiketleri virgülle ayırarak girin (örn: ahşap, el yapımı, vintage).',
        }

    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price is None:
            return price
        price_str = str(price).strip()
        if not price_str:
            raise ValidationError('Fiyat alanı boş bırakılamaz.')
        try:
            price_str = price_str.replace(',', '.')
            price_decimal = Decimal(price_str)
            if price_decimal < 0:
                raise ValidationError('Fiyat negatif olamaz.')
            max_price = Decimal('99999999.99')
            if price_decimal > max_price:
                raise ValidationError(f'Fiyat {max_price:,.2f} TL\'den büyük olamaz.')
            if price_decimal.as_tuple().exponent < -2:
                raise ValidationError('Fiyat en fazla 2 ondalık basamak içerebilir.')
            return price_decimal
        except InvalidOperation:
            raise ValidationError('Geçersiz fiyat formatı. Lütfen geçerli bir sayı girin (örn: 100,50 veya 100.50).')
        except (ValueError, TypeError) as e:
            raise ValidationError(f'Geçersiz fiyat değeri. Lütfen geçerli bir sayı girin.')

    def clean_stock_quantity(self):
        stock_quantity = self.cleaned_data.get('stock_quantity')
        if stock_quantity is None:
            return stock_quantity
        if stock_quantity < 0:
            raise ValidationError('Stok miktarı negatif olamaz.')
        max_stock = 1000000
        if stock_quantity > max_stock:
            raise ValidationError(f'Stok miktarı {max_stock:,}\'dan büyük olamaz.')
        return stock_quantity

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise ValidationError('Ürün adı boş bırakılamaz.')
        name = name.strip()
        if len(name) < 2:
            raise ValidationError('Ürün adı en az 2 karakter olmalıdır.')
        if len(name) > 100:
            raise ValidationError('Ürün adı en fazla 100 karakter olabilir.')
        existing_products = Product.objects.filter(name__iexact=name)
        if self.instance and self.instance.pk:
            existing_products = existing_products.exclude(pk=self.instance.pk)
        if existing_products.exists():
            raise ValidationError('Bu isimde bir ürün zaten mevcut.')
        return name

    def clean_tags(self):
        tags_input = self.cleaned_data.get('tags', '')
        if not tags_input:
            return []
        tags_str = str(tags_input).strip()
        if not tags_str:
            return []
        tag_list = []
        for tag in tags_str.split(','):
            tag = tag.strip()
            if tag:
                import re
                cleaned_tag = re.sub(r'[^\w\s\-]', '', tag)
                cleaned_tag = cleaned_tag.strip()
                if cleaned_tag:
                    if len(cleaned_tag) > 50:
                        raise ValidationError(f'Etiket çok uzun: "{cleaned_tag[:20]}..." (maksimum 50 karakter)')
                    if cleaned_tag.lower() not in [t.lower() for t in tag_list]:
                        tag_list.append(cleaned_tag)
        if len(tag_list) > 10:
            raise ValidationError('En fazla 10 etiket ekleyebilirsiniz.')
        return tag_list

    def clean(self):
        cleaned_data = super().clean()
        price = cleaned_data.get('price')
        stock_quantity = cleaned_data.get('stock_quantity')
        is_available = cleaned_data.get('is_available')
        if is_available and stock_quantity is not None and stock_quantity == 0:
            self.add_error('stock_quantity',
                          'Ürün "stokta var" olarak işaretlenmişse stok miktarı 0\'dan büyük olmalıdır.')
        if is_available and price is not None and price == 0:
            self.add_error('price',
                          'Satışa hazır ürünlerin fiyatı 0\'dan büyük olmalıdır.')
        return cleaned_data


class ProductImageForm(forms.ModelForm):
    """Ürün fotoğrafı ekleme formu."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from django.conf import settings
        allowed_content_types = getattr(settings, 'SECURE_UPLOAD_CONTENT_TYPES',
                                      ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
        accept_types = ','.join(allowed_content_types)

        # Image alanını opsiyonel yap
        self.fields['image'].required = False

        # Dropzone.js için gerekli sınıfları ve BEM sınıfını birleştir
        self.fields['image'].widget.attrs.update({
            'class': 'form__input form__input--file-dropzone dropzone',
            'accept': accept_types,
            'multiple': True
        })

        if self.errors:
            widget = self.fields['image'].widget
            existing_classes = widget.attrs.get('class', '')
            invalid_class = 'form__input--invalid'
            if invalid_class not in existing_classes:
                widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

    class Meta:
        model = ProductImage
        fields = ['image', 'is_main']
        widgets = {
            'is_main': forms.HiddenInput(),
        }
        labels = {
            'image': 'Ürün Fotoğrafları',
        }
        help_texts = {
            'image': 'Ürün fotoğraflarını sürükleyip bırakın veya tıklayarak seçin. Maksimum dosya boyutu 5MB. Desteklenen formatlar: JPG, JPEG, PNG, GIF, WEBP.',
        }

    def clean_image(self):
        image = self.cleaned_data.get('image')
        if not image:
            return image
        from django.conf import settings
        max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 5 * 1024 * 1024)
        if image.size > max_size:
            max_size_mb = max_size / (1024 * 1024)
            raise ValidationError(f'Dosya boyutu {max_size_mb:.0f}MB\'dan büyük olamaz.')
        import os
        allowed_extensions = getattr(settings, 'SECURE_UPLOAD_EXTENSIONS', ['.jpg', '.jpeg', '.png', '.gif', '.webp'])
        file_ext = os.path.splitext(image.name)[1].lower()
        if file_ext not in allowed_extensions:
            allowed_str = ', '.join(allowed_extensions)
            raise ValidationError(f'Geçersiz dosya uzantısı. İzin verilen uzantılar: {allowed_str}')
        allowed_content_types = getattr(settings, 'SECURE_UPLOAD_CONTENT_TYPES',
                                      ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'])
        if hasattr(image, 'content_type') and image.content_type not in allowed_content_types:
            raise ValidationError('Geçersiz dosya türü. Sadece resim dosyaları yükleyebilirsiniz.')
        return image


class ProductDeleteForm(forms.Form):
    """Ürün silme onay formu."""
    confirm = forms.BooleanField(
        required=True,
        initial=False,
        widget=forms.HiddenInput(),
        label='Ürünü silmek istediğinizden emin misiniz?'
    )


# Custom formset class for product images
class BaseProductImageFormSet(forms.BaseInlineFormSet):
    def clean(self):
        """
        Boş formları görmezden gel ve sadece dolu formları validate et.
        """
        if any(self.errors):
            return

        # Boş olmayan formları say
        valid_forms = 0
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                if form.cleaned_data.get('image'):
                    valid_forms += 1

        # En az bir form olması gerekmiyor, bu opsiyonel

# Inline formset for product images
ProductImageFormSet = inlineformset_factory(
    Product,
    ProductImage,
    form=ProductImageForm,
    formset=BaseProductImageFormSet,
    fields=['image'],
    extra=1,
    can_delete=True,
    can_delete_extra=True,
    validate_min=False,
    min_num=0,
    validate_max=False,
    max_num=10
)
