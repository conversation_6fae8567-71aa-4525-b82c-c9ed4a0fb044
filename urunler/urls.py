from django.urls import path
from . import views

app_name = 'urunler'

# API URL'leri - Özel URL'ler üstte olmalı
urlpatterns = [
    # API endpoint'leri
    path('api/products/<int:id>/<slug:slug>/upload-image/', views.product_image_upload, name='product_image_upload'),
    path('api/products/<int:product_id>/set-main-image/', views.set_main_image, name='set_main_image'),
    path('api/images/<int:image_id>/delete/', views.delete_image, name='delete_image'),

    # Geriye uyumluluk için eski URL'ler (deprecated)
    path('api/upload-image/', views.product_image_upload_legacy, name='upload_image_legacy'),
]

# Ürün işlemleri
urlpatterns += [
    path('ekle/', views.urun_ekle, name='urun_ekle'),
    path('<int:id>/<slug:slug>/duzenle/', views.urun_duzenle, name='urun_duzenle'),
    path('<int:id>/<slug:slug>/sil/', views.urun_sil, name='urun_sil'),
    path('<int:id>/<slug:slug>/', views.urun_detay, name='urun_detay'),

    # Geriye uyumluluk için eski resim yükleme URL'i (deprecated)
    path('<int:id>/<slug:slug>/fotograf-ekle/', views.product_image_upload, name='fotograf_ekle'),
]

# Kategori ve liste sayfaları
urlpatterns += [
    path('kategori/<slug:category_slug>/', views.urun_listesi, name='urun_listesi_by_category'),
    path('', views.urun_listesi, name='urun_listesi'),
]
