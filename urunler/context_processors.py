"""
Ürünler uygulaması için context processor'lar.
Template'lerde kullanılabilecek global değişkenler sağlar.
"""

from .permissions import get_user_product_permissions


def product_permissions(request):
    """
    Kullanıcının ürün yetkilendirme bilgilerini template'lere sağlar.
    
    Kullanım:
        Template'lerde {{ product_permissions.can_add_product }} şeklinde kullanılabilir.
    
    Args:
        request: HTTP request objesi
        
    Returns:
        dict: Template'lerde kullanılabilecek yetki bilgileri
    """
    return {
        'product_permissions': get_user_product_permissions(request.user)
    }
