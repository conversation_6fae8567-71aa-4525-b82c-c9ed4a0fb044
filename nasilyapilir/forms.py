from django import forms
from django.core.exceptions import ValidationError
from django.utils.html import strip_tags
import re
from .models import Video
from urunler.models import Category

class VideoForm(forms.ModelForm):
    """Video oluşturma ve düzenleme formu."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # <PERSON><PERSON><PERSON> d<PERSON>e modundaysa, mevcut etiketleri düzgün formatta göster
        if self.instance and self.instance.pk:
            # Mevcut etiketleri virgülle ayrılmış string olarak al
            tag_names = [tag.name for tag in self.instance.tags.all()]
            self.initial['tags'] = ', '.join(tag_names)

        if self.errors:
            for field_name in self.errors:
                if field_name in self.fields:
                    widget = self.fields[field_name].widget
                    existing_classes = widget.attrs.get('class', '')
                    invalid_class = ''
                    if isinstance(widget, forms.Textarea):
                        invalid_class = 'form__textarea--invalid'
                    elif isinstance(widget, forms.Select):
                        invalid_class = 'form__select--invalid'
                    elif isinstance(widget, forms.CheckboxInput):
                        invalid_class = 'form__check-input--invalid'
                    else: # TextInput, URLInput, FileInput etc.
                        invalid_class = 'form__input--invalid'

                    if invalid_class and invalid_class not in existing_classes:
                        widget.attrs['class'] = f'{existing_classes} {invalid_class}'.strip()

    class Meta:
        model = Video
        fields = ['baslik', 'aciklama', 'youtube_url', 'kategori', 'tags', 'onizleme_resmi', 'aktif']
        widgets = {
            'baslik': forms.TextInput(attrs={'placeholder': 'Video başlığını girin', 'class': 'form__input'}),
            'aciklama': forms.Textarea(attrs={'rows': 5, 'placeholder': 'Video açıklamasını girin', 'class': 'form__textarea'}),
            'youtube_url': forms.URLInput(attrs={'placeholder': 'YouTube video URL\'sini girin', 'class': 'form__input'}),
            'kategori': forms.Select(attrs={'class': 'form__select'}),
            'tags': forms.TextInput(attrs={'placeholder': 'Etiketleri virgülle ayırarak girin', 'class': 'form__input'}),
            'onizleme_resmi': forms.FileInput(attrs={'class': 'form__input'}),
            'aktif': forms.CheckboxInput(attrs={'class': 'form__check-input'}),
        }
        labels = {
            'baslik': 'Video Başlığı',
            'aciklama': 'Açıklama',
            'youtube_url': 'YouTube URL',
            'kategori': 'Kategori',
            'tags': 'Etiketler',
            'onizleme_resmi': 'Önizleme Resmi',
            'aktif': 'Aktif',
        }
        help_texts = {
            'baslik': 'Videonun başlığını girin.',
            'aciklama': 'Video hakkında detaylı bilgi verin.',
            'youtube_url': 'YouTube video linkini girin (örn: https://www.youtube.com/watch?v=...).',
            'kategori': 'Videonun ait olduğu kategoriyi seçin.',
            'tags': 'Video ile ilgili etiketleri virgülle ayırarak girin (örn: ahşap, el yapımı, diy).',
            'onizleme_resmi': 'Video için önizleme resmi yükleyin (isteğe bağlı).',
            'aktif': 'Video yayında görünsün mü?',
        }

    def clean_youtube_url(self):
        """
        YouTube URL\'sini doğrula ve temizle.
        """
        url = self.cleaned_data.get('youtube_url', '').strip()

        if not url:
            raise ValidationError('YouTube URL alanı boş olamaz.')

        # HTML etiketlerini temizle
        url = strip_tags(url)

        # Temel URL formatı kontrolü
        if not url.startswith(('http://', 'https://')):
            raise ValidationError('URL http:// veya https:// ile başlamalıdır.')

        # YouTube domain kontrolü
        youtube_domains = ['youtube.com', 'youtu.be', 'www.youtube.com', 'm.youtube.com']
        if not any(domain in url.lower() for domain in youtube_domains):
            raise ValidationError('Geçerli bir YouTube URL\'si girin.')

        # Video ID pattern kontrolü
        video_id_pattern = r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})'
        if not re.search(video_id_pattern, url):
            raise ValidationError('Geçerli bir YouTube video URL\'si girin.')

        # Zararlı karakterleri kontrol et
        dangerous_patterns = [
            r'javascript:',
            r'data:',
            r'vbscript:',
        ]

        url_lower = url.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, url_lower):
                raise ValidationError('URL güvenlik açısından uygun olmayan karakterler içeriyor.')

        return url

    def clean_baslik(self):
        """
        Başlık temizleme ve validasyon.
        Django'nun template engine'i otomatik escape yaptığı için manuel escape gereksiz.
        """
        baslik = self.cleaned_data.get('baslik', '').strip()

        if not baslik:
            raise ValidationError('Başlık alanı boş olamaz.')

        # HTML etiketlerini temizle (güvenlik için)
        baslik = strip_tags(baslik)

        # Çok uzun başlıkları kontrol et
        max_length = 200
        if len(baslik) > max_length:
            raise ValidationError(f'Başlık {max_length} karakterden uzun olamaz.')

        # Minimum uzunluk kontrolü
        min_length = 3
        if len(baslik) < min_length:
            raise ValidationError(f'Başlık en az {min_length} karakter olmalıdır.')

        # Sadece boşluk karakterlerinden oluşup oluşmadığını kontrol et
        if not baslik.strip():
            raise ValidationError('Başlık sadece boşluk karakterlerinden oluşamaz.')

        # Zararlı karakterleri kontrol et (opsiyonel - çok sıkı güvenlik için)
        dangerous_patterns = [
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'on\w+\s*=',  # onclick, onload vb.
        ]

        baslik_lower = baslik.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, baslik_lower):
                raise ValidationError('Başlık güvenlik açısından uygun olmayan karakterler içeriyor.')

        return baslik

    def clean_aciklama(self):
        """
        Açıklama alanı temizleme ve validasyon.
        """
        aciklama = self.cleaned_data.get('aciklama', '').strip()

        if not aciklama:
            raise ValidationError('Açıklama alanı boş olamaz.')

        # HTML etiketlerini temizle
        aciklama = strip_tags(aciklama)

        # Çok uzun açıklamaları kontrol et
        max_length = 2000
        if len(aciklama) > max_length:
            raise ValidationError(f'Açıklama {max_length} karakterden uzun olamaz.')

        # Minimum uzunluk kontrolü
        min_length = 10
        if len(aciklama) < min_length:
            raise ValidationError(f'Açıklama en az {min_length} karakter olmalıdır.')

        # Zararlı içerik kontrolü
        dangerous_patterns = [
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'on\w+\s*=',
        ]

        aciklama_lower = aciklama.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, aciklama_lower):
                raise ValidationError('Açıklama güvenlik açısından uygun olmayan içerik barındırıyor.')

        return aciklama
