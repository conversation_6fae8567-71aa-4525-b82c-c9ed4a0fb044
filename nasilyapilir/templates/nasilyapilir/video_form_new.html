{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Kü<PERSON> Cadısı{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/modern-theme.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/form-styles.css' %}">
{% endblock %}

{% block content %}
<section class="modern-section py-5">
    <div class="container">
        <!-- Başlık Bölümü -->
        <div class="modern-section-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="modern-title">{{ title }}</h1>
            <p class="modern-subtitle">{% if video %}{{ video.baslik }} videosunu düzenleyin{% else %}Video bilgilerini doldurun ve paylaşın{% endif %}</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="modern-card wow fadeInUp" data-wow-delay="0.3s">
                    <div class="modern-card-header">
                        <h3><i class="bi bi-camera-video me-2"></i>Video Bilgileri</h3>
                    </div>
                    <div class="modern-card-body">
                        <form method="post" enctype="multipart/form-data" class="calisma-form">
                            {% csrf_token %}

                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Lütfen formdaki hataları düzeltin.
                            </div>
                            {% endif %}

                            <div class="row g-4">
                                <!-- Video Başlığı -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.baslik.id_for_label }}" class="form__label">{{ form.baslik.label }} <span class="text-danger">*</span></label>
                                        {{ form.baslik }}
                                        {% if form.baslik.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.baslik.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- YouTube URL -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.youtube_url.id_for_label }}" class="form__label">{{ form.youtube_url.label }} <span class="text-danger">*</span></label>
                                        {{ form.youtube_url }}
                                        <small class="form-text text-muted">YouTube video linkini girin (örn: https://www.youtube.com/watch?v=...)</small>
                                        {% if form.youtube_url.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.youtube_url.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Kategori -->
                                <div class="col-md-6">
                                    <div class="form__group">
                                        <label for="{{ form.kategori.id_for_label }}" class="form__label">{{ form.kategori.label }} <span class="text-danger">*</span></label>
                                        {{ form.kategori }}
                                        {% if form.kategori.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.kategori.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Etiketler -->
                                <div class="col-md-6">
                                    <div class="form__group">
                                        <label for="{{ form.tags.id_for_label }}" class="form__label">{{ form.tags.label }}</label>
                                        {{ form.tags }}
                                        <small class="form-text text-muted">Virgülle ayırarak birden fazla etiket ekleyebilirsiniz</small>
                                        {% if form.tags.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.tags.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Açıklama -->
                                <div class="col-md-12">
                                    <div class="form__group">
                                        <label for="{{ form.aciklama.id_for_label }}" class="form__label">{{ form.aciklama.label }} <span class="text-danger">*</span></label>
                                        {{ form.aciklama }}
                                        {% if form.aciklama.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aciklama.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Önizleme Resmi -->
                                <div class="col-md-6">
                                    <div class="form__group">
                                        <label for="{{ form.onizleme_resmi.id_for_label }}" class="form__label">{{ form.onizleme_resmi.label }}</label>
                                        {{ form.onizleme_resmi }}
                                        <small class="form-text text-muted">Video için önizleme resmi yükleyin (isteğe bağlı)</small>
                                        {% if form.onizleme_resmi.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.onizleme_resmi.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Aktif -->
                                <div class="col-md-6">
                                    <div class="form__group">
                                        <div class="form__check mt-4">
                                            {{ form.aktif }}
                                            <label for="{{ form.aktif.id_for_label }}" class="form__check-label">{{ form.aktif.label }}</label>
                                        </div>
                                        <small class="form-text text-muted">Video yayında görünsün mü?</small>
                                        {% if form.aktif.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.aktif.errors }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Butonlar -->
                                <div class="col-12 mt-4">
                                    <div class="form-actions">
                                        <button type="submit" class="button">
                                            <i class="bi bi-check-circle me-2"></i>{% if video %}Güncelle{% else %}Kaydet{% endif %}
                                        </button>
                                        <a href="{% if video %}{% url 'nasilyapilir:video_detay' video.slug %}{% else %}{% url 'nasilyapilir:video_listesi' %}{% endif %}" class="button button--outline ms-2">
                                            <i class="bi bi-x-circle me-2"></i>İptal
                                        </a>
                                        {% if video %}
                                        <a href="{% url 'nasilyapilir:video_sil' video.slug %}" class="button button--danger ms-auto">
                                            <i class="bi bi-trash me-2"></i>Videoyu Sil
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // WOW.js başlatma
        new WOW().init();
    });
</script>
{% endblock %}
