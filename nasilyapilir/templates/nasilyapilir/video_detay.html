{% extends 'base.html' %}
{% load static %}
{% load video_filters %}

{% block title %}{{ video.baslik }} - Kü<PERSON> Cadısı{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<section class="modern-section py-3">
    <div class="container modern-container">
        <!-- <PERSON>ş<PERSON>ık Bölümü -->
        <div class="modern-header text-center mb-5 wow fadeIn" data-wow-delay="0.2s">
            <h1 class="autumn-heading">Seramik Sanatında İlk Adımlar</h1>
            <p class="lead text-muted"><PERSON> emeğiyle şekillenen her seramik hikayesi... Videolarımızla birlikte siz de deneyin!</p>
        </div>

        <div class="row g-4">
            <!-- Video İçeriği -->
            <div class="col-lg-8 wow fadeInUp" data-wow-delay="0.3s">
                <div class="modern-video-container">
                    <div class="modern-card">
                        <div class="modern-video-player">
                            <iframe src="{{ video.youtube_url|youtube_embed }}"
                                    title="{{ video.baslik }}"
                                    allowfullscreen></iframe>
                        </div>
                        <div class="modern-card-body">
                            <h2 class="modern-title">{{ video.baslik }}</h2>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="modern-badge autumn-tag">{{ video.kategori.ad }}</span>
                                <span class="text-muted small"><i class="bi bi-calendar3 me-1 text-moss"></i>{{ video.tarih|date:"d.m.Y" }}</span>
                            </div>
                            <div class="modern-card-text mb-3">{{ video.aciklama|linebreaks }}</div>
                            <div class="d-flex align-items-center justify-content-between mt-3">
                                <div class="text-muted small">
                                    <i class="bi bi-eye me-1 text-navy"></i> {{ video.goruntulenme }} görüntüleme
                                </div>
                                <button type="button" id="begeniBtn" class="modern-like-btn {% if kullanici_begendi %}active{% endif %}">
                                    <i class="bi {% if kullanici_begendi %}bi-hand-thumbs-up-fill{% else %}bi-hand-thumbs-up{% endif %}"></i>
                                    <span id="begeniSayisi">{{ video.begeni_sayisi }}</span> Beğen
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% load yorum_tags %}
                {% yorumlari_goster video %}


            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.3s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-info-circle me-2 text-brown"></i>Video Hakkında</h3>
                    </div>

                    <div class="modern-card-body p-0 sidebar-body">
                        <div class="modern-info-list">
                            <div class="modern-info-item modern-list-item">
                                <strong>Ekleyen:</strong>
                                <a href="{% url 'uyelik:profil' %}" class="text-decoration-none d-flex align-items-center">
                                    <span class="text-navy me-1">{{ video.ekleyen.username }}</span>
                                    <i class="bi bi-person-circle text-navy"></i>
                                </a>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Yüklenme Tarihi:</strong>
                                <span><i class="bi bi-calendar3 me-1 text-moss"></i>{{ video.tarih|date:"d F Y" }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Kategori:</strong>
                                <span><i class="bi bi-tag me-1 text-brown"></i>{{ video.kategori.ad }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Görüntüleme:</strong>
                                <span><i class="bi bi-eye-fill me-1 text-navy"></i>{{ video.goruntulenme }}</span>
                            </div>
                            <div class="modern-info-item modern-list-item">
                                <strong>Beğeni:</strong>
                                <span><i class="bi bi-hand-thumbs-up-fill me-1 text-brown"></i>{{ video.begeni_sayisi }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.4s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-collection-play me-2 text-brown"></i>Benzer Videolar</h3>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for benzer_video in benzer_videolar %}
                            <li>
                                <a href="{% url 'nasilyapilir:video_detay' benzer_video.slug %}" class="modern-sidebar-item autumn-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        <img src="{{ benzer_video.youtube_url|youtube_thumbnail }}" alt="{{ benzer_video.baslik }}">
                                        <div class="modern-play-button">
                                            <i class="bi bi-play-circle"></i>
                                        </div>
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ benzer_video.baslik }}</h6>
                                        <div class="d-flex align-items-center small text-muted">
                                            <span class="me-2"><i class="bi bi-eye me-1 text-navy"></i>{{ benzer_video.goruntulenme }}</span>
                                            <span><i class="bi bi-calendar3 me-1 text-moss"></i>{{ benzer_video.tarih|date:"d.m.Y" }}</span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.4s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-fire me-2 text-brown"></i>Popüler Videolar</h3>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <ul class="modern-sidebar-list">
                            {% for populer_video in populer_videolar %}
                            <li>
                                <a href="{% url 'nasilyapilir:video_detay' populer_video.slug %}" class="modern-sidebar-item autumn-sidebar-item">
                                    <div class="modern-sidebar-thumbnail">
                                        <img src="{{ populer_video.youtube_url|youtube_thumbnail }}" alt="{{ populer_video.baslik }}">
                                        <div class="modern-play-button">
                                            <i class="bi bi-play-circle"></i>
                                        </div>
                                    </div>
                                    <div class="modern-sidebar-info">
                                        <h6 class="mb-1 autumn-title text-navy">{{ populer_video.baslik }}</h6>
                                        <div class="d-flex align-items-center small text-muted">
                                            <span class="me-2"><i class="bi bi-eye me-1 text-navy"></i>{{ populer_video.goruntulenme }}</span>
                                            <span><i class="bi bi-calendar3 me-1 text-moss"></i>{{ populer_video.tarih|date:"d.m.Y" }}</span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="modern-card mb-4 wow fadeInRight" data-wow-delay="0.5s">
                    <div class="modern-card-header autumn-card-header">
                        <h3><i class="bi bi-tags me-2 text-brown"></i>Etiketler</h3>
                    </div>
                    <div class="modern-card-body p-0 sidebar-body">
                        <div class="modern-tag-cloud">
                            {% for tag in video.tags.all %}
                            <a href="{% url 'nasilyapilir:video_listesi' %}?etiket={{ tag.slug }}"
                               class="modern-tag autumn-tag">
                                {{ tag.name }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Yönetim Paneli (Video Yöneticileri ve Üstü) -->
                {% if user_perms.can_add_video or user_perms.can_edit_video %}
                <div class="modern-card admin-panel-card mb-4 wow fadeInRight" data-wow-delay="0.6s">
                    <div class="featured-card-header admin-panel-header">
                        <h3><i class="bi bi-gear-fill me-2 text-white"></i>Yönetim Paneli</h3>
                    </div>
                    <div class="modern-card-body p-0">
                        <!-- Yönetim Menüsü -->
                        <div class="admin-menu">
                            <!-- Yeni Video Ekle -->
                            <a href="{% url 'nasilyapilir:video_olustur' %}" class="admin-menu-item">
                                <div class="admin-menu-icon">
                                    <i class="bi bi-plus-circle-fill" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Yeni Video Ekle</h5>
                                    <p class="text-muted admin-menu-description">Nasıl yapılır koleksiyonuna yeni video ekleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Bu Videoyu Düzenle -->
                            <a href="{% url 'nasilyapilir:video_duzenle' video.slug %}" class="admin-menu-item">
                                <div class="admin-menu-icon" style="background-color: #2c5aa0;">
                                    <i class="bi bi-pencil-square" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Bu Videoyu Düzenle</h5>
                                    <p class="text-muted admin-menu-description">Mevcut videonun bilgilerini güncelleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Videoyu Sil -->
                            <a href="#" class="admin-menu-item delete-item" onclick="confirmDelete(event, '{% url 'nasilyapilir:video_sil' video.slug %}', 'video')">
                                <div class="admin-menu-icon delete-icon">
                                    <i class="bi bi-trash3-fill" style="color: white; font-size: 1.2rem;"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Videoyu Sil</h5>
                                    <p class="text-muted admin-menu-description">Bu videoyu kalıcı olarak silin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>

                            <!-- Video Listesine Dön -->
                            <a href="{% url 'nasilyapilir:video_listesi' %}" class="admin-menu-item">
                                <div class="admin-menu-icon back-icon">
                                    <i class="bi bi-arrow-left-circle"></i>
                                </div>
                                <div class="admin-menu-content">
                                    <h5>Video Listesine Dön</h5>
                                    <p class="text-muted admin-menu-description">Tüm videoları görüntüleyin</p>
                                </div>
                                <div class="admin-menu-arrow">
                                    <i class="bi bi-chevron-right"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>



<!-- AJAX beğeni işlevselliği için -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        // Beğeni butonu işlevi
        $('#begeniBtn').click(function(e) {
            e.preventDefault();

            console.log('Beğeni butonuna tıklandı - jQuery');

            $.ajax({
                url: "{% url 'nasilyapilir:video_begen' video.slug %}",
                type: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    console.log('Başarılı yanıt:', data);

                    // Beğeni sayısını güncelle
                    $('#begeniSayisi').text(data.begeni_sayisi);

                    // Beğeni ikonu güncelle
                    var $icon = $('#begeniBtn i');
                    if (data.begendi) {
                        $('#begeniBtn').addClass('active');
                        $icon.removeClass('bi-hand-thumbs-up').addClass('bi-hand-thumbs-up-fill');
                    } else {
                        $('#begeniBtn').removeClass('active');
                        $icon.removeClass('bi-hand-thumbs-up-fill').addClass('bi-hand-thumbs-up');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Beğeni hatası:', error);
                    alert('Beğeni işlemi sırasında bir hata oluştu.');
                }
            });

            return false; // Bağlantı tıklamasını engelle
        });

        // Custom confirm dialog artık global olarak yüklü
    });
</script>
{% endblock %}