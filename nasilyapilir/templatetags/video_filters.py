from django import template
import re

register = template.Library()

@register.filter
def youtube_embed(url):
    """
    YouTube URL'sini embed formatına dö<PERSON>türür.
    Örnek: https://www.youtube.com/watch?v=dQw4w9WgXcQ -> https://www.youtube.com/embed/dQw4w9WgXcQ
    """
    # YouTube URL'sinden video ID'sini çıkar
    video_id = re.search(r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})', url)
    if video_id:
        return f'https://www.youtube.com/embed/{video_id.group(1)}'
    return url

@register.filter
def youtube_thumbnail(url):
    # YouTube URL'sinden video ID'sini çıkar
    video_id = re.search(r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})', url)
    if video_id:
        return f'https://img.youtube.com/vi/{video_id.group(1)}/hqdefault.jpg'
    return None 