from django.shortcuts import render, get_object_or_404, redirect
from django.core.paginator import Paginator
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponseForbidden
from django.contrib import messages
from django.views.decorators.http import require_POST
from .models import Video
from urunler.models import Category
from .forms import VideoForm
from taggit.models import Tag
from utils.logging import get_logger, log_user_action

logger = get_logger(__name__)

def video_listesi(request):
    # Kategorileri ve etiketleri al
    kategoriler = Category.objects.all()
    etiketler = Tag.objects.all()

    # Filtreleme
    videos = Video.objects.filter(aktif=True)
    kategori_slug = request.GET.get('kategori')
    etiket_slug = request.GET.get('etiket')

    if kategori_slug:
        videos = videos.filter(kategori__slug=kategori_slug)
    if etiket_slug:
        videos = videos.filter(tags__slug=etiket_slug)

    # Sayfalama
    paginator = Paginator(videos.order_by('-tarih'), 6)  # Her sayfada 6 video
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'videolar': page_obj,
        'kategoriler': kategoriler,
        'etiketler': etiketler,
        'total_videos': Video.objects.filter(aktif=True).count(),
        'is_paginated': True if paginator.num_pages > 1 else False,
    }
    return render(request, 'nasilyapilir/video_listesi.html', context)

def video_detay(request, slug):
    video = get_object_or_404(Video, slug=slug, aktif=True)
    video.goruntulenme += 1
    video.save()

    benzer_videolar = Video.objects.filter(
        kategori=video.kategori,
        aktif=True
    ).exclude(id=video.id).order_by('-goruntulenme')[:4]

    populer_videolar = Video.objects.filter(aktif=True).order_by('-goruntulenme')[:5]

    context = {
        'video': video,
        'benzer_videolar': benzer_videolar,
        'populer_videolar': populer_videolar,
        'kullanici_begendi': video.kullanici_begendi_mi(request.user) if request.user.is_authenticated else False,
    }
    return render(request, 'nasilyapilir/video_detay.html', context)

@login_required
@require_POST
def video_begen(request, slug):
    video = get_object_or_404(Video, slug=slug, aktif=True)

    # Önceki beğeni durumunu logla
    onceki_begendi = video.kullanici_begendi_mi(request.user)
    onceki_begeni_sayisi = video.begeni_sayisi()

    logger.debug("Video beğeni işlemi başlatıldı", extra={
        'video_id': video.id,
        'video_baslik': video.baslik,
        'user': request.user.username,
        'onceki_begendi': onceki_begendi,
        'onceki_begeni_sayisi': onceki_begeni_sayisi
    })

    if onceki_begendi:
        video.begenenler.remove(request.user)
        begendi = False
        action = 'unlike_video'
    else:
        video.begenenler.add(request.user)
        begendi = True
        action = 'like_video'

    # begeni_sayisi değeri güncelleniyor
    begeni_sayisi = video.begenenler.count()
    video.begeni = begeni_sayisi
    video.save(update_fields=['begeni'])

    # Sonucu logla
    logger.info("Video beğeni işlemi tamamlandı", extra={
        'video_id': video.id,
        'video_baslik': video.baslik,
        'user': request.user.username,
        'action': action,
        'yeni_begendi': begendi,
        'yeni_begeni_sayisi': begeni_sayisi
    })

    log_user_action(request.user, action, f"video:{video.id}", {
        'video_baslik': video.baslik,
        'begeni_sayisi': begeni_sayisi
    })

    # AJAX isteği mi normal form gönderimi mi kontrol et
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    if is_ajax:
        return JsonResponse({
            'success': True,
            'begeni_sayisi': begeni_sayisi,
            'begendi': begendi
        })
    else:
        # Normal form post ise sayfaya geri yönlendir
        return redirect('nasilyapilir:video_detay', slug=slug)

@login_required
def video_olustur(request):
    """Yeni video oluşturma view'ı."""
    if not request.user.is_superuser:
        return HttpResponseForbidden("Bu işlem için yönetici yetkisi gereklidir.")

    if request.method == 'POST':
        form = VideoForm(request.POST, request.FILES)
        if form.is_valid():
            video = form.save(commit=False)
            video.ekleyen = request.user
            video.save()
            form.save_m2m()  # Çoktan çoğa ilişkiler için gerekli
            messages.success(request, 'Video başarıyla oluşturuldu!')
            return redirect('nasilyapilir:video_detay', slug=video.slug)
    else:
        form = VideoForm()

    context = {
        'form': form,
        'kategoriler': Category.objects.all(),
        'etiketler': Tag.objects.all(),
        'title': 'Yeni Video Ekle'
    }
    return render(request, 'nasilyapilir/video_form_new.html', context)

@login_required
def video_duzenle(request, slug):
    """Video düzenleme view'ı."""
    video = get_object_or_404(Video, slug=slug)

    if not request.user.is_superuser:
        return HttpResponseForbidden("Bu işlem için yönetici yetkisi gereklidir.")

    if request.method == 'POST':
        form = VideoForm(request.POST, request.FILES, instance=video)
        if form.is_valid():
            form.save()
            messages.success(request, 'Video bilgileri güncellendi!')
            return redirect('nasilyapilir:video_detay', slug=video.slug)
    else:
        form = VideoForm(instance=video)

    context = {
        'form': form,
        'video': video,
        'kategoriler': Category.objects.all(),
        'etiketler': Tag.objects.all(),
        'title': f'{video.baslik} - Düzenle'
    }
    return render(request, 'nasilyapilir/video_form_new.html', context)

@login_required
@require_POST
def video_sil(request, slug):
    """Video silme view'ı."""
    video = get_object_or_404(Video, slug=slug)

    if not request.user.is_superuser:
        return HttpResponseForbidden("Bu işlem için yönetici yetkisi gereklidir.")

    video_baslik = video.baslik
    video.delete()
    messages.success(request, f'"{video_baslik}" videosu başarıyla silindi.')
    return redirect('nasilyapilir:video_listesi')