from django.db import models
from django.contrib.auth.models import User
from django.utils.text import slugify
from django.db.models.signals import m2m_changed
from django.dispatch import receiver
from taggit.managers import TaggableManager
from urunler.models import Category
from utils.logging import get_logger

# Logger'ı başlat
logger = get_logger(__name__)



class Video(models.Model):
    baslik = models.CharField(max_length=200, verbose_name="Video Başlığı")
    slug = models.SlugField(unique=True)
    aciklama = models.TextField(verbose_name="Video Açıklaması")
    youtube_url = models.URLField(verbose_name="YouTube Video URL")
    kategori = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='videolar')
    tags = TaggableManager(blank=True, verbose_name="Etiketler", help_text="Video ile ilgili etiketleri virgülle ayırarak girin")
    onizleme_resmi = models.ImageField(upload_to='video_onizleme/', blank=True, null=True)
    goruntulenme = models.PositiveIntegerField(default=0, verbose_name="<PERSON>örüntülenme Sayısı")
    begeni = models.PositiveIntegerField(default=0, verbose_name="Beğeni Sayısı")
    tarih = models.DateTimeField(auto_now_add=True, verbose_name="Eklenme Tarihi")
    aktif = models.BooleanField(default=True, verbose_name="Aktif")
    begenenler = models.ManyToManyField(User, related_name='begenilen_videolar', blank=True)
    ekleyen = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='videolar', verbose_name="Ekleyen Kullanıcı")

    class Meta:
        verbose_name = "Video"
        verbose_name_plural = "Videolar"
        ordering = ['-tarih']

    def __str__(self):
        return self.baslik

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.baslik)
        super().save(*args, **kwargs)

    def begeni_sayisi(self):
        return self.begenenler.count()

    def kullanici_begendi_mi(self, user):
        if user.is_authenticated:
            return self.begenenler.filter(id=user.id).exists()
        return False

    def begeni_toggle(self, user):
        """
        Kullanıcının beğeni durumunu değiştirir (toggle).
        Signal otomatik olarak begeni alanını güncelleyecek.
        """
        if not user.is_authenticated:
            return False, self.begeni

        if self.kullanici_begendi_mi(user):
            self.begenenler.remove(user)
            begendi = False
        else:
            self.begenenler.add(user)
            begendi = True

        # Signal otomatik olarak begeni'yi güncelleyecek
        self.refresh_from_db(fields=['begeni'])
        yeni_sayisi = self.begeni

        logger.info(f"Video beğeni durumu değişti: {self.baslik}", extra={
            'video_id': self.id,
            'user_id': user.id,
            'user_username': user.username,
            'begendi': begendi,
            'yeni_begeni_sayisi': yeni_sayisi,
            'operation': 'video_begeni_toggle'
        })

        return begendi, yeni_sayisi


# Signal handlers
@receiver(m2m_changed, sender=Video.begenenler.through)
def video_begenenler_changed(sender, instance, action, pk_set, **kwargs):
    """
    Video.begenenler ManyToManyField değiştiğinde begeni alanını otomatik günceller.
    """
    try:
        # Sadece gerçek değişikliklerden sonra güncelle
        if action in ['post_add', 'post_remove', 'post_clear']:
            old_count = instance.begeni
            new_count = instance.begenenler.count()

            # Sadece değişiklik varsa güncelle
            if old_count != new_count:
                instance.begeni = new_count
                instance.save(update_fields=['begeni'])

                logger.debug(f"Video beğeni sayısı otomatik güncellendi: {instance.baslik}", extra={
                    'video_id': instance.id,
                    'video_baslik': instance.baslik,
                    'action': action,
                    'old_count': old_count,
                    'new_count': new_count,
                    'pk_set': list(pk_set) if pk_set else None,
                    'operation': 'video_begenenler_m2m_changed'
                })

    except Exception as e:
        logger.error(f"Video beğeni sayısı güncelleme hatası: {e}", extra={
            'video_id': getattr(instance, 'id', 'None'),
            'action': action,
            'error': str(e),
            'operation': 'video_begenenler_m2m_changed'
        })