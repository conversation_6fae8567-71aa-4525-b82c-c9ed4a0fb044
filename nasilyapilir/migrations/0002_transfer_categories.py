# Generated manually for category data transfer

from django.db import migrations

def transfer_categories(apps, schema_editor):
    """nasilyapilir kategorilerini urunler.Category'ye transfer et"""
    # Eski modelleri al
    NasilKategori = apps.get_model('nasilyapilir', 'Kategori')
    UrunlerCategory = apps.get_model('urunler', 'Category')
    
    # Mevcut nasilyapilir kategorilerini urunler.Category'ye kopyala
    for kategori in NasilKategori.objects.all():
        # Aynı slug'a sahip kategori var mı kontrol et
        existing_category = UrunlerCategory.objects.filter(slug=kategori.slug).first()
        if not existing_category:
            UrunlerCategory.objects.create(
                ad=kategori.ad,
                slug=kategori.slug,
                aciklama=kategori.aciklama
            )

def reverse_transfer_categories(apps, schema_editor):
    """Geri alma işlemi - gerekirse kategorileri geri oluştur"""
    pass  # Bu durumda geri alma yapmıyoruz

class Migration(migrations.Migration):

    dependencies = [
        ('nasilyapilir', '0001_initial'),
        ('urunler', '0003_alter_category_options_and_more'),
    ]

    operations = [
        migrations.RunPython(transfer_categories, reverse_transfer_categories),
    ]
