# Generated by Django 5.2 on 2025-06-08 01:58

import django.db.models.deletion
import taggit.managers
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Kategori',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ad', models.CharField(max_length=100, verbose_name='<PERSON><PERSON><PERSON> Adı')),
                ('slug', models.SlugField(unique=True)),
                ('aciklama', models.TextField(blank=True, verbose_name='<PERSON>gori Açıkla<PERSON>ı')),
            ],
            options={
                'verbose_name': 'Kategori',
                'verbose_name_plural': 'Kategoriler',
            },
        ),
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('baslik', models.CharField(max_length=200, verbose_name='Video Başlığı')),
                ('slug', models.SlugField(unique=True)),
                ('aciklama', models.TextField(verbose_name='Video Açıklaması')),
                ('youtube_url', models.URLField(verbose_name='YouTube Video URL')),
                ('onizleme_resmi', models.ImageField(blank=True, null=True, upload_to='video_onizleme/')),
                ('goruntulenme', models.PositiveIntegerField(default=0, verbose_name='Görüntülenme Sayısı')),
                ('begeni', models.PositiveIntegerField(default=0, verbose_name='Beğeni Sayısı')),
                ('tarih', models.DateTimeField(auto_now_add=True, verbose_name='Eklenme Tarihi')),
                ('aktif', models.BooleanField(default=True, verbose_name='Aktif')),
                ('begenenler', models.ManyToManyField(blank=True, related_name='begenilen_videolar', to=settings.AUTH_USER_MODEL)),
                ('ekleyen', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='videolar', to=settings.AUTH_USER_MODEL, verbose_name='Ekleyen Kullanıcı')),
                ('kategori', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videolar', to='nasilyapilir.kategori')),
                ('tags', taggit.managers.TaggableManager(blank=True, help_text='Video ile ilgili etiketleri virgülle ayırarak girin', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Etiketler')),
            ],
            options={
                'verbose_name': 'Video',
                'verbose_name_plural': 'Videolar',
                'ordering': ['-tarih'],
            },
        ),
    ]
