<!-- mysite/templates/base.html -->
<!DOCTYPE html>
<html lang="tr">

<head>
    {% load static %}
    <!-- Favicons (öncelikli olarak en üstte) -->    <link rel="icon" href="{% static 'assets/img/favicon/favicon.svg' %}?v=1">
    <link rel="icon" href="{% static 'assets/img/favicon/favicon.ico' %}?v=1">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'assets/img/favicon/favicon-32x32.png' %}?v=1">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'assets/img/favicon/favicon-16x16.png' %}?v=1">

    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>{% block title %}Ana Sayfa - KüpCadısı{% endblock %}</title>

    <!-- PWA Meta Tags - Temporarily disabled for development -->
    <!--
    <meta name="application-name" content="Küp Cadısı Atolye">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Atolye">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#007bff">
    <meta name="msapplication-TileColor" content="#007bff">
    <meta name="msapplication-tap-highlight" content="no">

    <link rel="manifest" href="{% static 'manifest.json' %}">
    -->
    <meta name="description" content="Küp Cadısı - Rubik küp ve bulmaca ürünleri">
    <meta name="keywords" content="küp, rubik, bulmaca, zeka oyunları">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">

    <!-- CSS değişkenleri artık _variables.css dosyasında tanımlanmıştır -->

    <!-- Vendor CSS Files -->
    <link href="{% static 'assets/vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/bootstrap-icons/bootstrap-icons.css' %}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link href="{% static 'assets/vendor/aos/aos.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/swiper/swiper-bundle.min.css' %}" rel="stylesheet">
    <link href="{% static 'assets/vendor/glightbox/css/glightbox.min.css' %}" rel="stylesheet">

    <!-- 1. Değişkenler ve Temel Sistem -->
    <link href="{% static 'assets/css/_variables.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/design-system.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/typography-system.css' %}" rel="stylesheet">

    <!-- 2. Eski/Genel Stil Dosyaları -->
    <link href="{% static 'assets/css/main.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/styles.css' %}" rel="stylesheet">

    <!-- 3. Modern BEM Bileşenleri (Eskileri Ezer) -->
    <link href="{% static 'assets/css/modern-theme.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/global-components.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/modern-components.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/modern-buttons.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/modern-cards.css' %}?v=2.0" rel="stylesheet">
    <link href="{% static 'assets/css/modern-forms.css' %}" rel="stylesheet">

    <!-- 4. Diğer Yardımcı Stiller -->
    <link href="{% static 'assets/css/inline-edit.css' %}" rel="stylesheet">
    <link href="{% static 'assets/css/custom-confirm.css' %}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">

    <!-- 5. Uygulama Özel Stilleri (En Son Yüklenir) -->
    {% block extra_css %}{% endblock %}
</head>

<body class="index-page {% if user_perms.can_view_admin %}user-is-admin{% endif %}">

    <header id="header" class="header modern-header sticky-top">
        <div class="container-fluid position-relative d-flex align-items-center justify-content-between">

            <a href="{% url 'anasayfa:ana_sayfa' %}" class="logo d-flex align-items-center me-auto me-xl-0">
                <h1 class="modern-logo">KüpCadısı</h1>
            </a>

            <div class="d-flex align-items-center">
                <!-- Mobile Nav Toggle Button -->
                <i class="mobile-nav-toggle d-lg-none bi bi-list ms-3"></i>
            </div>

            <!-- Navigation Menu -->
            <nav id="navmenu" class="modern-nav">
                <ul>
                    <li><a href="{% url 'anasayfa:ana_sayfa' %}" {% if request.path == '/' or request.path == '/anasayfa/' %}class="active"{% endif %}>Ana Sayfa</a></li>
                    <li><a href="{% url 'anasayfa:hakkimda' %}" {% if request.path == '/hakkimda/' %}class="active"{% endif %}>Hakkımda</a></li>
                    <li><a href="{% url 'anasayfa:iletisim' %}" {% if request.path == '/iletisim/' %}class="active"{% endif %}>İletişim</a></li>
                    <li><a href="{% url 'urunler:urun_listesi' %}" {% if '/urunler/' in request.path %}class="active"{% endif %}>Ürün Listesi</a></li>
                    <li><a href="{% url 'nasilyapilir:video_listesi' %}" {% if '/nasil-yapilir/' in request.path %}class="active"{% endif %}>Nasıl Yapılır?</a></li>
                    <li><a href="{% url 'calismalar:calisma_listesi' %}" {% if '/calismalar/' in request.path %}class="active"{% endif %}>Çalışmalar</a></li>

                    <!-- Mesajlaşma Menüsü (Sadece onaylı kullanıcılar için) -->



                    <!-- Debug Info -->
                    {% if debug %}
                    <li style="color: red; font-size: 12px;">
                        DEBUG: user.is_authenticated = {{ user.is_authenticated }}<br>
                        DEBUG: user.username = {{ user.username|default:"None" }}<br>
                        DEBUG: user_perms exists = {{ user_perms|yesno:"Yes,No" }}
                    </li>
                    {% endif %}

                    <!-- Auth Buttons in Navbar -->
                    {% if user.is_authenticated %}
                        {% if user_perms.can_view_admin %}
                        <li class="nav-auth-item d-none d-lg-block">
                            <a href="/admin/" class="nav-auth-link nav-auth-admin" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Yönetim Paneli">
                                <i class="bi bi-gear-fill"></i>
                                <span>Admin</span>
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-auth-item d-none d-lg-block">
                            <a href="{% url 'uyelik:profil' %}" class="nav-auth-link" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Profilim">
                                <i class="bi bi-person-circle"></i>
                                <span>{{ user.username }}</span>
                            </a>
                        </li>
                        <li class="nav-auth-item d-none d-lg-block">
                            <a href="{% url 'uyelik:cikis' %}" class="nav-auth-link nav-auth-outline" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Çıkış Yap">
                                <i class="bi bi-box-arrow-right"></i>
                                <span>Çıkış Yap</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-auth-item d-none d-lg-block">
                            <a href="{% url 'uyelik:kayit' %}" class="nav-auth-link nav-auth-moss" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Kayıt Ol">
                                <i class="bi bi-person-plus"></i>
                                <span>Kayıt Ol</span>
                            </a>
                        </li>
                        <li class="nav-auth-item d-none d-lg-block">
                            <a href="{% url 'uyelik:giris' %}" class="nav-auth-link nav-auth-highlight" title="Giriş Yap">
                                <i class="bi bi-box-arrow-in-right"></i>
                                <span>Giriş Yap</span>
                            </a>
                        </li>
                    {% endif %}

                    <!-- Mobile Only Auth Buttons -->
                    <li class="d-lg-none">
                        <div class="mobile-auth-buttons mt-3 mb-2 d-flex flex-column gap-2">
                            {% if user.is_authenticated %}
                                {% if user_perms.can_view_admin %}
                                <a href="/admin/" class="button button--warning button--icon">
                                    <i class="bi bi-gear-fill"></i>
                                    <span>Yönetim Paneli</span>
                                </a>
                                {% endif %}
                                <!-- Profil Butonu (Mobile) -->
                                <a href="{% url 'uyelik:profil' %}" class="button button--primary button--icon">
                                    <i class="bi bi-person-circle"></i>
                                    <span>{{ user.username }}</span>
                                </a>
                                <a href="{% url 'uyelik:cikis' %}" class="button button--outline button--icon">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>Çıkış Yap</span>
                                </a>
                            {% else %}
                                <a href="{% url 'uyelik:kayit' %}" class="button button--success button--icon">
                                    <i class="bi bi-person-plus"></i>
                                    <span>Kayıt Ol</span>
                                </a>
                                <a href="{% url 'uyelik:giris' %}" class="button button--primary button--icon">
                                    <i class="bi bi-box-arrow-in-right"></i>
                                    <span>Giriş Yap</span>
                                </a>
                            {% endif %}
                        </div>
                    </li>
                </ul>
            </nav>

        </div>
    </header>

    <main id="main">
        {% block content %}
        {% endblock %}
    </main>

    {% load cache %}
    {% comment %} Cache geçici olarak devre dışı - sosyal medya güncellemeleri için {% endcomment %}
    {% comment %} {% cache 3600 footer %} {% endcomment %}
    <footer id="footer" class="modern-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h3 class="modern-footer-title">Küp Cadısı</h3>
                    <p class="modern-footer-text">"İç Mimarlık ve Tasarım Hizmetleri"</p>
                </div>
                <div class="col-md-4 mb-4 mb-md-0">
                    <h3 class="modern-footer-title">Hızlı Bağlantılar</h3>
                    <ul class="modern-footer-links">
                        <li><a href="{% url 'anasayfa:ana_sayfa' %}">Ana Sayfa</a></li>
                        <li><a href="{% url 'anasayfa:hakkimda' %}">Hakkımda</a></li>
                        <li><a href="{% url 'anasayfa:iletisim' %}">İletişim</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h3 class="modern-footer-title">Takip Edin</h3>
                    <div class="modern-social-links">
                        {% for hesap in sosyal_medya %}
                            {% if hesap.url != '#' %}
                                <a href="{{ hesap.url }}" class="social-link" target="_blank"><i class="bi {{ hesap.icon }}"></i></a>
                            {% endif %}
                        {% empty %}
                            <!-- Fallback sosyal medya linkleri -->
                            <a href="#" class="social-link"><i class="bi bi-instagram"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-facebook"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-twitter"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-youtube"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-linkedin"></i></a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="modern-footer-bottom">
                <div class="copyright text-center">
                    <p>&copy; <span id="copyright-year">2025</span> <strong>Küp Cadısı</strong> | Tüm Hakları Saklıdır</p>
                </div>
                <div class="credits text-center mt-2">
                    Tasarım: <a href="https://bootstrapmade.com/">BootstrapMade</a> |
                    Geliştirme: <a href="#">Küp Cadısı Ekibi</a>
                </div>
            </div>
        </div>
    </footer>
    {% comment %} {% endcache %} {% endcomment %}

    <!-- Scroll Top -->
    <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i
            class="bi bi-arrow-up-short"></i></a>

    <!-- Preloader -->
    <div id="preloader"></div>

    <!-- Vendor JS Files -->
    <script src="{% static 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'assets/vendor/php-email-form/validate.js' %}"></script>
    <script src="{% static 'assets/vendor/aos/aos.js' %}"></script>
    <script src="{% static 'assets/vendor/waypoints/noframework.waypoints.js' %}"></script>
    <script src="{% static 'assets/vendor/purecounter/purecounter_vanilla.js' %}"></script>
    <script src="{% static 'assets/vendor/swiper/swiper-bundle.min.js' %}"></script>
    <script src="{% static 'assets/vendor/glightbox/js/glightbox.min.js' %}"></script>
    <script src="{% static 'assets/vendor/imagesloaded/imagesloaded.pkgd.min.js' %}"></script>
    <script src="{% static 'assets/vendor/isotope-layout/isotope.pkgd.min.js' %}"></script>

    <!-- Main JS File -->
    <script src="{% static 'assets/js/main.js' %}"></script>
    <script src="{% static 'assets/js/modern-ui.js' %}"></script>
    <script src="{% static 'assets/js/custom-confirm.js' %}"></script>



    <!-- Vendor JS Files -->
    <script src="{% static 'assets/vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'assets/vendor/aos/aos.js' %}"></script>
    <script src="{% static 'assets/vendor/glightbox/js/glightbox.min.js' %}"></script>
    <script src="{% static 'assets/vendor/swiper/swiper-bundle.min.js' %}"></script>    <!-- Additional JS Files -->
    <script src="{% static 'assets/js/inline_edit.js' %}"></script>

    <script>
        document.getElementById('copyright-year').textContent = new Date().getFullYear();

        // Tooltip'leri ve diğer özellikleri başlat
        document.addEventListener('DOMContentLoaded', function() {
            // Bootstrap 5 tooltip'leri başlat
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // WOW.js başlat
            new WOW().init();
        });
    </script>

    <!-- WOW.js başlat -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.min.js"></script>

    <!-- CSRF Token Yardımcıları -->
    <script>
        // CSRF Token Yardımcı Fonksiyonları
        window.CSRFUtils = {
            // CSRF token'ı al
            getToken: function() {
                const token = document.querySelector('[name=csrfmiddlewaretoken]');
                return token ? token.value : null;
            },

            // CSRF token'ı yenile
            refreshToken: function() {
                return fetch('/utils/csrf-token/')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Tüm CSRF input'larını güncelle
                            document.querySelectorAll('[name=csrfmiddlewaretoken]').forEach(input => {
                                input.value = data.csrf_token;
                            });
                            return data.csrf_token;
                        }
                        throw new Error('CSRF token yenilenemedi');
                    });
            },

            // AJAX istekleri için header ekle
            setupAjax: function() {
                // Fetch API için
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    if (options.method && !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(options.method)) {
                        options.headers = options.headers || {};
                        options.headers['X-CSRFToken'] = CSRFUtils.getToken();
                    }
                    return originalFetch(url, options);
                };
            }
        };

        // Sayfa yüklendiğinde AJAX'ı ayarla
        document.addEventListener('DOMContentLoaded', function() {
            CSRFUtils.setupAjax();
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>

</html>
