{% extends 'base.html' %}
{% load static %}

{% block title %}{% if urun %}{{ urun.name }} Düzenle{% else %}<PERSON><PERSON>{% endif %}{% endblock %}

{% block extra_css %}
<link href="{% static 'urunler/css/urun_form.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card urun-form-card">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="h4 mb-3">
                            <i class="fas fa-{{ form_icon|default:'box' }} me-2"></i>
                            {% if urun %}{{ urun.name }} Düzenle{% else %}Yeni <PERSON><PERSON>{% endif %}
                        </h2>
                        <p class="text-muted"><PERSON>üt<PERSON> ürün bilgilerini doldurunuz.</p>
                    </div>
                    
                    <form id="urunForm" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% if urun %}
                            <input type="hidden" name="product_id" value="{{ urun.id }}">
                        {% endif %}
                        
                        <!-- Resim Yükleme Alanı -->
                        {% include 'urunler/includes/_image_upload.html' %}
                        
                        <!-- Temel Bilgiler -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Temel Bilgiler</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">Ürün Adı</label>
                                        {{ form.name }}
                                        <div class="invalid-feedback">Lütfen ürün adını giriniz.</div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="{{ form.category.id_for_label }}" class="form-label">Kategori</label>
                                        {{ form.category }}
                                        <div class="invalid-feedback">Lütfen bir kategori seçiniz.</div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <label for="{{ form.description.id_for_label }}" class="form-label">Açıklama</label>
                                        {{ form.description }}
                                        <div class="form-text">Ürün hakkında detaylı bilgi veriniz.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fiyat ve Stok Bilgileri -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Fiyat ve Stok Bilgileri</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label for="{{ form.price.id_for_label }}" class="form-label">Fiyat (₺)</label>
                                        <div class="input-group">
                                            {{ form.price }}
                                            <span class="input-group-text">₺</span>
                                        </div>
                                        <div class="invalid-feedback">Lütfen geçerli bir fiyat giriniz.</div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="{{ form.stock_quantity.id_for_label }}" class="form-label">Stok Miktarı</label>
                                        {{ form.stock_quantity }}
                                        <div class="invalid-feedback">Lütfen stok miktarını giriniz.</div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="{{ form.unit.id_for_label }}" class="form-label">Birim</label>
                                        {{ form.unit }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Diğer Bilgiler -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Diğer Bilgiler</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="{{ form.brand.id_for_label }}" class="form-label">Marka</label>
                                        {{ form.brand }}
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="{{ form.barcode.id_for_label }}" class="form-label">Barkod</label>
                                        {{ form.barcode }}
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="form-check form-switch">
                                            {{ form.is_active }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                Aktif
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Butonlar -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'urunler:urun_listesi' %}" class="button button--outline me-md-2">
                                <i class="fas fa-times me-1"></i> İptal
                            </a>
                            <button type="submit" class="button button--primary">
                                <i class="fas fa-save me-1"></i> Kaydet
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'urunler/js/urun_form.js' %}"></script>
{% endblock %}
