<!-- <PERSON><PERSON><PERSON> -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0"><PERSON><PERSON><PERSON><PERSON></h5>
    </div>
    <div class="card-body">
        <div class="image-upload-container">
            <div id="image-upload" class="dropzone"></div>
            <div class="form-text mt-2">Resimleri sürükleyip bırakabilir veya tıklayarak seçebilirsiniz.</div>
        </div>
        
        <!-- Mevcut Resimler -->
        {% if urun and images %}
            <div class="mt-4">
                <h6 class="mb-3">Mevcut Resimler</h6>
                <div class="image-gallery">
                    {% for img in images %}
                        <div class="image-card{% if img.is_main %} border border-primary{% endif %}" id="image-col-{{ img.id }}">
                            <img src="{{ img.image.url }}" class="img-fluid" alt="{{ urun.name }}">
                            <div class="image-actions">
                                <button type="button" class="button button--small button--primary" 
                                    onclick="setAsMainImage({{ img.id }})" 
                                    title="Ana Resim Yap">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button type="button" 
                                    class="button button--small button--danger btn-delete-image" 
                                    data-image-id="{{ img.id }}" 
                                    title="Sil">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <div class="form-check text-center p-2">
                                <input class="form-check-input" type="radio" 
                                    name="main_image" 
                                    id="main_image_{{ img.id }}" 
                                    value="{{ img.id }}"
                                    {% if img.is_main %}checked{% endif %}
                                    onchange="setAsMainImage({{ img.id }})">
                                <label class="form-check-label" for="main_image_{{ img.id }}">
                                    Ana Resim
                                </label>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
