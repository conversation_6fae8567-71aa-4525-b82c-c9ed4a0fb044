from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponseBadRequest
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.contenttypes.models import ContentType
from django.views.decorators.http import require_POST
from django.template.loader import render_to_string
from django.utils.translation import gettext as _
import logging

from .models import Yorum
from .forms import YorumForm

# Logging ayarı
logger = logging.getLogger(__name__)

@require_POST
def yorum_ekle(request):
    """
    Herhangi bir sayfaya yorum ekleme görünümü
    İstek AJAX veya normal POST olabilir
    """
    logger.debug("Yorum ekleme view'ı çağrıldı")
    
    # POST verisinden içerik türü bilgilerini alın
    content_type_id = request.POST.get('content_type')
    object_id = request.POST.get('object_id')
    parent_id = request.POST.get('parent_id')
    
    logger.debug(f"Gelen veriler: content_type_id={content_type_id}, object_id={object_id}, parent_id={parent_id}")
    
    if not content_type_id or not object_id:
        return HttpResponseBadRequest("Eksik içerik bilgileri")
    
    try:
        content_type = ContentType.objects.get(id=content_type_id)
        content_object = content_type.get_object_for_this_type(id=object_id)
        logger.debug(f"Content Type: {content_type.app_label}.{content_type.model}, Nesne ID: {object_id}")
    except (ContentType.DoesNotExist, Exception) as e:
        logger.error(f"İçerik bulunamadı hatası: {e}")
        return HttpResponseBadRequest(f"İçerik bulunamadı: {e}")
    
    # Yanıtlanacak bir yorum varsa onu al
    parent = None
    if parent_id:
        try:
            parent = Yorum.objects.get(id=parent_id)
            logger.debug(f"Üst yorum bulundu: {parent}")
        except Yorum.DoesNotExist:
            logger.warning(f"Belirtilen ID ile üst yorum bulunamadı: {parent_id}")
            pass
    
    form = YorumForm(request.POST)
    if form.is_valid():
        logger.debug("Form geçerli, yorum oluşturuluyor")
        # Yeni yorum nesnesi
        yorum = form.save(commit=False)
        # İçerik bağlantıları
        yorum.content_type = content_type
        yorum.object_id = object_id
        yorum.parent = parent
        
        # Kullanıcı bilgileri
        if request.user.is_authenticated:
            yorum.user = request.user
            # Giriş yapmış kullanıcılar için doğrudan onay
            yorum.onaylandi = True
            logger.debug(f"Oturum açmış kullanıcı: {request.user.username}")
        else:
            # İsimsiz yorumlar için IP kaydı tutma
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            yorum.ip_adresi = ip
            logger.debug(f"Anonim yorum, IP: {ip}")
            # Yönetici onayına gerek (False varsayılan değer)
        
        # Yorumu kaydet
        yorum.save()
        logger.debug(f"Yorum kaydedildi, ID: {yorum.id}")
        
        # AJAX isteği ise JSON yanıtı döndür
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            logger.debug("AJAX isteği, JSON yanıtı hazırlanıyor")
            # Yorumun HTML görünümünü oluştur
            context = {
                'yorum': yorum, 
                'kullanici': request.user, 
                'request': request  # Şablon için gerekli request bilgisi
            }
            
            # Default olarak yorum_liste_ogesi şablonunu kullan
            yorum_html = render_to_string('yorumlar/yorum_liste_ogesi.html', context)
            
            # HTML içeriğini kontrol et
            logger.debug(f"Oluşturulan HTML içeriği uzunluğu: {len(yorum_html)}")
            logger.debug(f"HTML içeriği: {yorum_html[:100]}...")  # İlk 100 karakteri göster
            
            response_data = {
                'durum': 'başarılı',
                'mesaj': 'Yorumunuz gönderildi. Onaylandıktan sonra görünecektir.' if not yorum.onaylandi else 'Yorumunuz yayınlandı.',
                'html': yorum_html,
                'yorum_id': yorum.id,
                'parent_id': parent.id if parent else None,
                'onayli': yorum.onaylandi,
                'yorum_sahibi': yorum.user.username if yorum.user else yorum.isim,
                'tarih': yorum.tarih.isoformat()
            }
            logger.debug(f"JSON yanıtı: {response_data}")
            return JsonResponse(response_data)
        
        # Normal POST isteği
        logger.debug("POST isteği, sayfa yönlendiriliyor")
        return redirect(content_object.get_absolute_url())
    
    # Form geçerli değilse AJAX isteği yanıtı
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        logger.warning(f"Form hataları: {form.errors}")
        return JsonResponse({
            'durum': 'hata',
            'mesaj': 'Form geçerli değil. Lütfen alanları kontrol edin.',
            'hatalar': form.errors.as_json()
        }, status=400)
    
    # Normal POST isteği
    return redirect(content_object.get_absolute_url())

@login_required
@require_POST
def yorum_duzenle(request, yorum_id):
    """
    Sadece kullanıcıların kendi yorumlarını düzenleyebilmesi için görünüm
    """
    yorum = get_object_or_404(Yorum, id=yorum_id)
    
    # Yalnızca yorum sahibi veya moderatörler düzenleyebilir
    if not (request.user == yorum.user or request.user.has_perm('yorumlar.change_yorum')):
        return JsonResponse({'durum': 'hata', 'mesaj': 'Bu işlem için yetkiniz yok'}, status=403)
    
    form = YorumForm(request.POST, instance=yorum)
    if form.is_valid():
        form.save()
        # HTML formatında içeriği hazırla
        icerik_html = yorum.icerik.replace('\n', '<br>')
        return JsonResponse({
            'durum': 'başarılı',
            'mesaj': 'Yorum güncellendi',
            'icerik': yorum.icerik,
            'icerik_html': icerik_html
        })
    
    return JsonResponse({
        'durum': 'hata',
        'mesaj': 'Form geçerli değil',
        'hatalar': form.errors.as_json()
    }, status=400)

@login_required
@require_POST
def yorum_sil(request, yorum_id):
    """
    Yorumu silme görünümü - sadece yorum sahibi veya moderatörler
    """
    yorum = get_object_or_404(Yorum, id=yorum_id)
    
    # Yalnızca yorum sahibi veya moderatörler silebilir
    if not (request.user == yorum.user or request.user.has_perm('yorumlar.delete_yorum')):
        return JsonResponse({'durum': 'hata', 'mesaj': 'Bu işlem için yetkiniz yok'}, status=403)
    
    content_object = yorum.content_object  # Geri dönüş URL'si için saklayın
    yorum.delete()
    
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'durum': 'başarılı',
            'mesaj': 'Yorum silindi'
        })
    
    return redirect(content_object.get_absolute_url())

@permission_required('yorumlar.change_yorum')
@require_POST
def yorum_onayla(request, yorum_id):
    """
    Sadece moderatörler için yorum onaylama görünümü
    """
    yorum = get_object_or_404(Yorum, id=yorum_id)
    yorum.onaylandi = True
    yorum.save()
    
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'durum': 'başarılı',
            'mesaj': 'Yorum onaylandı'
        })
    
    return redirect(yorum.content_object.get_absolute_url())

@login_required
@require_POST
def yorum_sil_ajax(request):
    """
    Yorumu silme görünümü - AJAX için
    """
    yorum_id = request.POST.get('id')
    if not yorum_id:
        return JsonResponse({'durum': 'hata', 'hata': 'Yorum ID belirtilmedi'}, status=400)
        
    yorum = get_object_or_404(Yorum, id=yorum_id)
    
    # Yalnızca yorum sahibi veya moderatörler silebilir
    if not (request.user == yorum.user or request.user.has_perm('yorumlar.delete_yorum')):
        return JsonResponse({'durum': 'hata', 'hata': 'Bu işlem için yetkiniz yok'}, status=403)
    
    yorum.delete()
    
    return JsonResponse({
        'durum': 'başarılı',
        'mesaj': 'Yorum silindi'
    })

@permission_required('yorumlar.change_yorum')
@require_POST
def yorum_onayla_ajax(request):
    """
    Sadece moderatörler için yorum onaylama görünümü - AJAX için
    """
    yorum_id = request.POST.get('id')
    if not yorum_id:
        return JsonResponse({'durum': 'hata', 'hata': 'Yorum ID belirtilmedi'}, status=400)
        
    yorum = get_object_or_404(Yorum, id=yorum_id)
    yorum.onaylandi = True
    yorum.save()
    
    return JsonResponse({
        'durum': 'başarılı',
        'mesaj': 'Yorum onaylandı'
    })
