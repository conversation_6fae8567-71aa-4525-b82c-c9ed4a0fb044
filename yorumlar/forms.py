from django import forms
from .models import Yorum
from django.contrib.contenttypes.models import ContentType

class YorumForm(forms.ModelForm):
    def clean_icerik(self):
        icerik = self.cleaned_data.get('icerik', '')
        if len(icerik) > 500:
            raise forms.ValidationError('Yorumunuz 500 karakterden uzun olamaz.')
        return icerik
    """
    Çok amaçlı yorum formu
    """
    honeypot = forms.CharField(required=False, widget=forms.HiddenInput, label="")
    
    class Meta:
        model = Yorum
        fields = ['icerik', 'isim', 'email']
        widgets = {
            'icerik': forms.Textarea(attrs={
                'class': 'form__textarea',
                'rows': 4,
                'placeholder': 'Yorumunuzu buraya yazın...',
                'id': 'id_icerik' 
            }),
            'isim': forms.TextInput(attrs={
                'class': 'form__input',
                'placeholder': 'İsminiz',
                'id': 'id_isim'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form__input',
                'placeholder': 'E-posta adresiniz',
                'id': 'id_email'
            }),
        }
    
    def clean(self):
        """
        Güvenlik kontrolü - honeypot alanının boş olduğundan emin oluyoruz
        """
        cleaned_data = super().clean()
        honeypot = cleaned_data.get('honeypot')
        if honeypot:
            # Bir bot tarafından dolduruldu
            raise forms.ValidationError(
                "Bot algılandı. Lütfen sayfayı yenileyin ve tekrar deneyin."
            )
        return cleaned_data
