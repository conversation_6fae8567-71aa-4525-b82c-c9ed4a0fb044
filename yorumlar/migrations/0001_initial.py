# Generated by Django 5.2 on 2025-06-08 01:58

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Yorum',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField()),
                ('isim', models.CharField(blank=True, max_length=100, verbose_name='İsim')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='E-posta')),
                ('icerik', models.TextField(verbose_name='Yorum')),
                ('tarih', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Tarih')),
                ('onaylandi', models.BooleanField(default=False, verbose_name='Onaylandı')),
                ('ip_adresi', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Adresi')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cevaplar', to='yorumlar.yorum', verbose_name='Üst Yorum')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Kullanıcı')),
            ],
            options={
                'verbose_name': 'Yorum',
                'verbose_name_plural': 'Yorumlar',
                'ordering': ['-tarih'],
            },
        ),
    ]
