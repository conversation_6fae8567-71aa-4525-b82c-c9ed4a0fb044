from django.urls import path
from . import views

app_name = 'yorumlar'

urlpatterns = [
    path('ekle/', views.yorum_ekle, name='yorum_ekle'),
    path('duzenle/<int:yorum_id>/', views.yorum_duzenle, name='yorum_duzenle'),
    path('sil/<int:yorum_id>/', views.yorum_sil, name='yorum_sil'),
    path('onayla/<int:yorum_id>/', views.yorum_onayla, name='yorum_onayla'),
    # AJAX kullanımı için ID'siz URL'ler
    path('sil/', views.yorum_sil_ajax, name='yorum_sil_ajax'),
    path('onayla/', views.yorum_onayla_ajax, name='yorum_onayla_ajax'),
]
