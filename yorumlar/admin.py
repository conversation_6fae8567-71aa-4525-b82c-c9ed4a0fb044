from django.contrib import admin
from django.utils.html import format_html
from .models import Yorum

@admin.register(Yorum)
class YorumAdmin(admin.ModelAdmin):
    list_display = ['kullanici_adi', 'ilgili_icerik', 'kisaltilmis_icerik', 'tarih', 'onaylandi', 'ip_adresi']
    list_filter = ['onaylandi', 'tarih', 'content_type']
    search_fields = ['icerik', 'user__username', 'isim', 'email', 'ip_adresi']
    actions = ['yorumlari_onayla', 'yorumlari_reddet']
    readonly_fields = ['content_type', 'object_id', 'ip_adresi', 'tarih']
    list_editable = ['onaylandi']
    raw_id_fields = ['user', 'parent']
    
    fieldsets = [
        ('Yorum Bilgileri', {
            'fields': ['icerik', 'onaylandi', 'parent']
        }),
        ('Kullanıcı Bilgileri', {
            'fields': ['user', 'isim', 'email', 'ip_adresi']
        }),
        ('İçerik Bilgileri', {
            'fields': ['content_type', 'object_id', 'tarih']
        }),
    ]
    
    def kullanici_adi(self, obj):
        if obj.user:
            return obj.user.username
        elif obj.isim:
            return obj.isim
        else:
            return "Anonim"
    kullanici_adi.short_description = "Kullanıcı"
    
    def kisaltilmis_icerik(self, obj):
        if len(obj.icerik) > 75:
            return f"{obj.icerik[:75]}..."
        return obj.icerik
    kisaltilmis_icerik.short_description = "Yorum"
    
    def ilgili_icerik(self, obj):
        try:
            link = obj.content_object.get_absolute_url()
            return format_html('<a href="{}" target="_blank">{}</a>', link, obj.content_type.model)
        except:
            return f"{obj.content_type.model} #{obj.object_id}"
    ilgili_icerik.short_description = "İlgili İçerik"
    
    def yorumlari_onayla(self, request, queryset):
        queryset.update(onaylandi=True)
    yorumlari_onayla.short_description = "Seçili yorumları onayla"
    
    def yorumlari_reddet(self, request, queryset):
        queryset.update(onaylandi=False)
    yorumlari_reddet.short_description = "Seçili yorumları reddet"
