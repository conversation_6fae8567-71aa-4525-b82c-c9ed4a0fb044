from django import template
from django.contrib.contenttypes.models import ContentType
from django.utils.safestring import mark_safe
from django.template.loader import render_to_string
import logging

from ..models import Yorum
from ..forms import YorumForm

register = template.Library()
logger = logging.getLogger(__name__)

@register.filter
def content_type_id(obj):
    """
    Verilen nesnenin ContentType ID'sini döndürür
    """
    if obj is None:
        return ""
    return ContentType.objects.get_for_model(obj).id

@register.simple_tag(takes_context=True)
def yorumlari_goster(context, obj, template_name='yorumlar/yorumlar.html'):
    """
    Belirtilen nesne için yorumları gösterir.
    Kullanım:
    {% load yorum_tags %}
    {% yorumlari_goster nesne %}
    """
    request = context['request']
    logger.debug(f"yorumlari_goster tag çağrıldı, nesne: {obj}, template: {template_name}")
    
    # Nesnenin ContentType'ını al
    content_type = ContentType.objects.get_for_model(obj)
    logger.debug(f"Content Type: {content_type.app_label}.{content_type.model}, ID: {content_type.id}")
    
    # Bu nesne için onaylı yorumları al
    yorumlar = Yorum.objects.filter(
        content_type=content_type,
        object_id=obj.id,
        onaylandi=True
    )
    
    # Eğer kullanıcı moderatör ise, onaylanmamış yorumları da göster
    if request.user.has_perm('yorumlar.change_yorum'):
        yorumlar = Yorum.objects.filter(
            content_type=content_type,
            object_id=obj.id
        )
    
    logger.debug(f"Bulunan yorum sayısı: {yorumlar.count()}")
    
    # Yorum formunu oluştur
    form = YorumForm()
    
    # Şablonu render et
    rendered = render_to_string(
        template_name,
        {
            'yorumlar': yorumlar,
            'form': form,
            'content_type_id': content_type.id,
            'object_id': obj.id,
            'request': request
        },
        request=request
    )
    
    logger.debug(f"yorumlari_goster tag tamamlandı, oluşturulan içerik uzunluğu: {len(rendered)}")
    return mark_safe(rendered)

@register.simple_tag
def yorum_sayisi(obj):
    """
    Belirtilen nesnenin yorum sayısını döndürür.
    Kullanım:
    {% load yorum_tags %}
    {% yorum_sayisi nesne %}
    """
    if obj is None:
        return 0
        
    content_type = ContentType.objects.get_for_model(obj)
    return Yorum.objects.filter(
        content_type=content_type,
        object_id=obj.id,
        onaylandi=True,
        parent=None  # Sadece üst seviye yorumları say
    ).count()
