{% load static %}
<div class="yorumlar-container mt-4 mb-5">
    <div class="modern-card wow fadeInUp" data-wow-delay="0.4s">
        <div class="modern-card-header">
            <h3><i class="bi bi-chat-square-text me-2"></i>Yorumlar <span class="modern-badge">{{ yorumlar.count }}</span></h3>
        </div>
        <div class="modern-card-body">
            <!-- Yorum form bölümü -->
            <div class="yorum-form mb-4">
                <h3 class="mb-3"><i class="bi bi-pencil-square me-2"></i>Bir Yorum Yazın</h3>
                <form id="yorumFormu" method="post" action="{% url 'yorumlar:yorum_ekle' %}" class="modern-form">
                    {% csrf_token %}
                    <input type="hidden" name="content_type" value="{{ content_type_id }}">
                    <input type="hidden" name="object_id" value="{{ object_id }}">
                    <input type="hidden" name="parent_id" id="parent_id" value="">
                    <input type="hidden" name="honeypot" value="">
                    
                    <div class="form__group">
                        {{ form.icerik }}
                        <div class="invalid-feedback icerik-error"></div>
                    </div>
                    
                    {% if not request.user.is_authenticated %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form__group">
                                    {{ form.isim }}
                                    <div class="invalid-feedback isim-error"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form__group">
                                    {{ form.email }}
                                    <div class="invalid-feedback email-error"></div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <small>Yorumunuz moderasyon onayına gönderilecek. Onaylandıktan sonra görünecektir.</small>
                        </div>
                    {% endif %}
                    
                    <div class="mt-3 d-flex align-items-center">
                        <button type="submit" class="button">
                            <i class="bi bi-paper-plane me-1"></i> Yorum Gönder
                        </button>
                        <div id="yanit-bilgisi" class="ms-3 d-none">
                            <span class="yanit-kullanici fw-bold"></span> kullanıcısının yorumunu yanıtlıyorsunuz.
                            <button type="button" class="button button--link p-0 ms-2 yanit-iptal">İptal</button>
                        </div>
                        
                        <!-- Emoji butonları -->
                        <div class="ms-auto emoji-buttons">
                            <button type="button" class="button button--circle" onclick="addEmojiDirect('😊')" title="Gülümseme">😊</button>
                            <button type="button" class="button button--circle" onclick="addEmojiDirect('👍')" title="Beğendim">👍</button>
                            <button type="button" class="button button--circle" onclick="addEmojiDirect('❤️')" title="Kalp">❤️</button>
                            <button type="button" class="button button--circle" onclick="addEmojiDirect('😂')" title="Kahkaha">😂</button>
                            <button type="button" class="button button--circle" onclick="addEmojiDirect('🙏')" title="Rica ederim">🙏</button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Yorumlar listesi -->
            <div id="yorumlarListesi" class="yorumlar-listesi">
                {% if yorumlar %}
                    {% for yorum in yorumlar %}
                        {% if not yorum.parent %}  <!-- Sadece üst yorumları göster, cevaplar alt yorum_liste_ogesi.html içinde gösterilecek -->
                            {% include 'yorumlar/yorum_liste_ogesi.html' with yorum=yorum kullanici=request.user %}
                        {% endif %}
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4 text-muted">
                        <i class="bi bi-chat-square fs-1 mb-3"></i>
                        <p>Henüz yorum yapılmamış. İlk yorumu siz yapın!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Toast bildirim -->
<div class="position-fixed bottom-0 end-0 p-3 toast-container">
    <div id="yorumToast" class="toast align-items-center text-white bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body" id="toastMessage">
                İşlem başarıyla tamamlandı.
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Kapat"></button>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Karakter sayacı
    const icerikTextarea = document.getElementById('id_icerik');
    const sayac = document.getElementById('icerik-karakter-sayaci');
    if (icerikTextarea && sayac) {
        sayac.textContent = `${icerikTextarea.value.length}/500 karakter`;
        icerikTextarea.addEventListener('input', function() {
            sayac.textContent = `${this.value.length}/500 karakter`;
            if (this.value.length > 500) {
                sayac.classList.add('text-danger');
            } else {
                sayac.classList.remove('text-danger');
            }
        });
    }
    console.log('DOM yüklendi, emoji sistemi başlatılıyor...');
    
    // Basık emoji ekleme fonksiyonu - inline butonlar için
    window.addEmojiDirect = function(emoji) {
        console.log('Emoji ekleniyor:', emoji);
        const textarea = document.getElementById('id_icerik');
        if (!textarea) {
            console.error('Textarea bulunamadı!', document.getElementById('id_icerik'));
            return;
        }
        
        const cursorPos = textarea.selectionStart;
        const textBefore = textarea.value.substring(0, cursorPos);
        const textAfter = textarea.value.substring(cursorPos);
        
        textarea.value = textBefore + emoji + textAfter;
        
        // Cursor pozisyonunu emojiden sonraya taşı
        const newCursorPos = cursorPos + emoji.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
    }
    
    // Yanıtla butonu için event delegation
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('yanit-btn') || 
            e.target.closest('.yanit-btn')) {
            
            const button = e.target.classList.contains('yanit-btn') ? 
                        e.target : e.target.closest('.yanit-btn');
            const parentId = button.dataset.parentId;
            const yorumSahibi = button.closest('.yorum-ogesi').querySelector('h6').textContent.trim();
            
            document.getElementById('parent_id').value = parentId;
            document.querySelector('.yanit-kullanici').textContent = yorumSahibi;
            document.getElementById('yanit-bilgisi').classList.remove('d-none');
            
            // Form alanına odaklan ve sayfa konumunu form'a kaydır
            document.getElementById('id_icerik').focus();
            document.getElementById('yorumFormu').scrollIntoView({ behavior: 'smooth' });
        }
    });
    
    // Yanıt iptal
    document.querySelector('.yanit-iptal').addEventListener('click', function() {
        document.getElementById('parent_id').value = '';
        document.getElementById('yanit-bilgisi').classList.add('d-none');
    });
    
    // Toast bildirimi gösterme fonksiyonu
    function showToast(message) {
        const toastEl = document.getElementById('yorumToast');
        const toast = new bootstrap.Toast(toastEl);
        document.getElementById('toastMessage').textContent = message;
        toast.show();
    }
    
    // Event delegation ile düzenle/iptal/kaydet butonlarına tıklamaları dinle
    document.addEventListener('click', function(e) {
        // Düzenle butonu için
        if (e.target.classList.contains('duzenle-toggle-btn') || 
            e.target.closest('.duzenle-toggle-btn')) {
            
            const button = e.target.classList.contains('duzenle-toggle-btn') ? 
                        e.target : e.target.closest('.duzenle-toggle-btn');
            const yorumId = button.dataset.yorumId;
            
            // İçerik ve form alanlarını seç
            const icerikAlani = document.getElementById(`yorum-icerik-${yorumId}`);
            const formAlani = document.getElementById(`duzenle-form-${yorumId}`);
            
            // İçeriği gizle, formu göster
            if (icerikAlani && formAlani) {
                icerikAlani.classList.add('d-none');
                formAlani.classList.remove('d-none');
            }
        }
        
        // Düzenleme İptal butonu için
        else if (e.target.classList.contains('duzenle-iptal-btn') || 
                e.target.closest('.duzenle-iptal-btn')) {
            
            const button = e.target.classList.contains('duzenle-iptal-btn') ? 
                        e.target : e.target.closest('.duzenle-iptal-btn');
            const yorumId = button.dataset.yorumId;
            
            // İçerik ve form alanlarını seç
            const icerikAlani = document.getElementById(`yorum-icerik-${yorumId}`);
            const formAlani = document.getElementById(`duzenle-form-${yorumId}`);
            
            // Formu gizle, içeriği göster
            if (icerikAlani && formAlani) {
                formAlani.classList.add('d-none');
                icerikAlani.classList.remove('d-none');
            }
        }
        
        // Düzenleme Kaydet butonu için
        else if (e.target.classList.contains('duzenle-kaydet-btn') || 
                e.target.closest('.duzenle-kaydet-btn')) {
            
            const button = e.target.classList.contains('duzenle-kaydet-btn') ? 
                        e.target : e.target.closest('.duzenle-kaydet-btn');
            const yorumId = button.dataset.yorumId;
            const yeniIcerik = document.getElementById(`duzenle-textarea-${yorumId}`).value;
            
            // AJAX isteği ile yorumu güncelle
            fetch('{% url "yorumlar:yorum_duzenle" yorum_id=0 %}'.replace('/0/', `/${yorumId}/`), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `icerik=${encodeURIComponent(yeniIcerik)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.durum === 'başarılı') {
                    // İçerik alanını güncelle
                    document.getElementById(`yorum-icerik-${yorumId}`).innerHTML = data.icerik_html;
                    
                    // Formu gizle, içeriği göster
                    document.getElementById(`duzenle-form-${yorumId}`).classList.add('d-none');
                    document.getElementById(`yorum-icerik-${yorumId}`).classList.remove('d-none');
                    
                    // Bildirim göster
                    showToast('Yorum başarıyla güncellendi.');
                } else {
                    showToast('Hata: ' + data.hata);
                }
            })
            .catch(error => {
                console.error('Hata:', error);
                showToast('Bir hata oluştu. Lütfen tekrar deneyin.');
            });
        }
        
        // Silme butonu için
        else if (e.target.classList.contains('sil-dogrudan-btn') || 
                e.target.closest('.sil-dogrudan-btn')) {
            
            const button = e.target.classList.contains('sil-dogrudan-btn') ? 
                        e.target : e.target.closest('.sil-dogrudan-btn');
            const yorumId = button.dataset.yorumId;
            
            if (confirm('Bu yorumu silmek istediğinize emin misiniz?')) {
                // AJAX isteği ile yorumu sil
                fetch('{% url "yorumlar:yorum_sil_ajax" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `id=${yorumId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.durum === 'başarılı') {
                        // Yorum öğesini DOM'dan kaldır
                        const yorumOgesi = document.getElementById(`yorum-${yorumId}`);
                        yorumOgesi.remove();
                        
                        // Bildirim göster
                        showToast('Yorum başarıyla silindi.');
                    } else {
                        showToast('Hata: ' + data.hata);
                    }
                })
                .catch(error => {
                    console.error('Hata:', error);
                    showToast('Bir hata oluştu. Lütfen tekrar deneyin.');
                });
            }
        }
        
        // Onaylama butonu için
        else if (e.target.classList.contains('onayla-btn') || 
                e.target.closest('.onayla-btn')) {
            
            const button = e.target.classList.contains('onayla-btn') ? 
                        e.target : e.target.closest('.onayla-btn');
            const yorumId = button.dataset.yorumId;
            
            // AJAX isteği ile yorumu onayla
            fetch('{% url "yorumlar:yorum_onayla_ajax" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `id=${yorumId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.durum === 'başarılı') {
                    // Yorum öğesinden "beklemede" sınıfını kaldır
                    const yorumOgesi = document.getElementById(`yorum-${yorumId}`);
                    yorumOgesi.classList.remove('beklemede');
                    
                    // Onay bekliyor rozetini kaldır
                    const onayRozeti = yorumOgesi.querySelector('.modern-badge');
                    if (onayRozeti) onayRozeti.remove();
                    
                    // Onay butonunu kaldır
                    button.remove();
                    
                    // Bildirim göster
                    showToast('Yorum başarıyla onaylandı.');
                } else {
                    showToast('Hata: ' + data.hata);
                }
            })
            .catch(error => {
                console.error('Hata:', error);
                showToast('Bir hata oluştu. Lütfen tekrar deneyin.');
            });
        }
    });
    
    // Yorum formunu gönderme
    document.getElementById('yorumFormu').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Form verilerini al
        const formData = new FormData(this);
        
        // AJAX isteği gönder
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.durum === 'başarılı') {
                // Form içeriğini temizle
                document.getElementById('id_icerik').value = '';
                if (!document.querySelector('body').classList.contains('user-is-authenticated')) {
                    document.getElementById('id_isim').value = '';
                    document.getElementById('id_email').value = '';
                }
                
                // Yanıt bilgisini sıfırla
                document.getElementById('parent_id').value = '';
                document.getElementById('yanit-bilgisi').classList.add('d-none');
                
                // Bildirim göster
                showToast('Yorumunuz başarıyla gönderildi.');
                
                // HTML yanıtı debugging
                console.log('Sunucudan gelen veri:', data);
                
                // Kullanıcı oturum açmışsa, yorumu hemen ekle
                if (data.html) {
                    const yorumlarListesi = document.getElementById('yorumlarListesi');
                    console.log('Yorumlar listesi bulundu:', !!yorumlarListesi);
                    
                    if (data.parent_id) {
                        // Eğer bir yanıtsa, üst yorumun altına ekle
                        const parentYorum = document.getElementById(`yorum-${data.parent_id}`);
                        console.log('Üst yorum bulundu:', !!parentYorum);
                        
                        if (parentYorum) {
                            let cevaplarListesi = parentYorum.querySelector('.cevaplar-listesi');
                            
                            if (!cevaplarListesi) {
                                // Eğer yanıt listesi yoksa oluştur
                                cevaplarListesi = document.createElement('div');
                                cevaplarListesi.className = 'cevaplar-listesi ms-5 mt-2';
                                parentYorum.appendChild(cevaplarListesi);
                            }
                            
                            // Yanıtı listeye ekle
                            cevaplarListesi.insertAdjacentHTML('beforeend', data.html);
                            console.log('Yanıt eklendi');
                        } else {
                            console.error('Üst yorum bulunamadı, ID:', data.parent_id);
                        }
                    } else {
                        // Eğer üst yorumsa, listenin başına ekle
                        if (yorumlarListesi) {
                            const firstYorum = yorumlarListesi.firstChild;
                            if (firstYorum) {
                                yorumlarListesi.insertAdjacentHTML('afterbegin', data.html);
                                console.log('Yorum başa eklendi');
                            } else {
                                // Hiç yorum yoksa boş mesajı kaldır ve yeni yorumu ekle
                                yorumlarListesi.innerHTML = data.html;
                                console.log('Boş listeye yorum eklendi');
                            }
                        } else {
                            console.error('Yorumlar listesi elementi bulunamadı!');
                        }
                    }
                } else {
                    // Oturum açılmamışsa veya HTML gönderilmediyse
                    console.log('HTML verisi bulunamadı veya kullanıcı oturum açmamış');
                    showToast('Yorumunuz moderasyon onayına gönderildi. Onaylandıktan sonra görünecektir.');
                }
            } else {
                // Hataları göster
                if (data.hatalar) {
                    const hatalar = JSON.parse(data.hatalar);
                    
                    // İçerik hatası
                    if (hatalar.icerik) {
                        document.getElementById('id_icerik').classList.add('is-invalid');
                        document.querySelector('.icerik-error').textContent = hatalar.icerik[0];
                    }
                    
                    // İsim hatası
                    if (hatalar.isim) {
                        document.getElementById('id_isim').classList.add('is-invalid');
                        document.querySelector('.isim-error').textContent = hatalar.isim[0];
                    }
                    
                    // Email hatası
                    if (hatalar.email) {
                        document.getElementById('id_email').classList.add('is-invalid');
                        document.querySelector('.email-error').textContent = hatalar.email[0];
                    }
                }
            }
        })
        .catch(error => {
            console.error('Hata:', error);
            showToast('Bir hata oluştu. Lütfen tekrar deneyin.');
        });
    });
});
</script>
{% endblock %}

<style>
    .yorumlar-card {
    background: linear-gradient(135deg, #F8F5F0 0%, #E0D8C8 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0,0,0,0.05);
    border: 1.5px solid #D9B391;
}

.yorumlar-header {
    background: linear-gradient(to right, #D9B391, #E0D8C8);
    color: #402401;
    font-weight: 600;
    text-shadow: 0 1px 1px rgba(255,255,255,0.5);
    padding: 1.5rem;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
}

.yorumlar-body {
    background: none;
    padding: 1.5rem;
}

.yorumlar-header .badge {
    background-color: #E0D8C8;
    color: #402401;
    font-weight: 500;
    font-size: 1rem;
}

.bg-gradient-brown {
        background: linear-gradient(135deg, #8B4513, #A0522D);
    }
    
    .btn-brown {
        background-color: #8B4513;
        color: white;
    }
    
    .btn-brown:hover {
        background-color: #A0522D;
        color: white;
    }
    
    .btn-outline-brown {
        color: #8B4513;
        border-color: #8B4513;
    }
    
    .btn-outline-brown:hover {
        background-color: #8B4513;
        color: white;
    }
    
    .emoji-picker {
        width: 300px;
        position: absolute;
        right: 0;
        z-index: 1000;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border: 1px solid #ddd;
        background-color: white;
        border-radius: 0.5rem;
    }
    
    .emoji-container {
        max-height: 150px;
        overflow-y: auto;
    }
    
    .emoji-item {
        font-size: 1.5rem;
        cursor: pointer;
        padding: 5px;
        transition: transform 0.2s;
    }
    
    .emoji-item:hover {
        transform: scale(1.2);
        background-color: rgba(0,0,0,0.05);
        border-radius: 5px;
    }
    
    .beklemede {
        opacity: 0.7;
    }
    
    .beklemede .card {
        border-left: 4px solid #8B4513;
    }
    
    .cevap .card {
        border-left: 4px solid #8B4513;
    }
</style>
