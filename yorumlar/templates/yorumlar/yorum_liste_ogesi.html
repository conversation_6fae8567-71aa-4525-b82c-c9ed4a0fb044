{% load static %}
<div id="yorum-{{ yorum.id }}" class="yorum-ogesi {% if yorum.parent %}cevap{% endif %} mb-3 {% if not yorum.onaylandi %}beklemede{% endif %}">
    <div class="modern-card h-100">
        <div class="modern-card-body">
            <div class="d-flex align-items-center mb-2">
                {% if yorum.user %}
                    <img src="{% if yorum.user.profil.profile_picture %}{{ yorum.user.profil.profile_picture.url }}{% else %}{% static 'assets/img/default-profile.png' %}{% endif %}" 
                         alt="{{ yorum.user.username }}" class="rounded-circle me-2" width="40" height="40">
                    <div>
                        <h6 class="mb-0 fw-bold">
                            <a href="{% url 'uyelik:kullanici_profil' username=yorum.user.username %}" class="text-inherit text-decoration-none">
                                {{ yorum.user.get_full_name|default:yorum.user.username }}
                            </a>
                        </h6>
                        <small class="text-muted">{{ yorum.tarih|timesince }} önce</small>
                    </div>
                {% else %}
                    <img src="{% static 'assets/img/default-profile.png' %}" alt="{{ yorum.isim|default:'Anonim' }}" 
                         class="rounded-circle me-2" width="40" height="40">
                    <div>
                        <h6 class="mb-0 fw-bold">{{ yorum.isim|default:"Anonim" }}</h6>
                        <small class="text-muted">{{ yorum.tarih|timesince }} önce</small>
                    </div>
                {% endif %}
                
                {% if not yorum.onaylandi %}
                    <span class="modern-badge ms-auto">Onay Bekliyor</span>
                {% endif %}
            </div>
            
            <div class="yorum-icerik" id="yorum-icerik-{{ yorum.id }}">{{ yorum.icerik|linebreaks }}</div>
            
            <!-- Düzenleme formu (varsayılan olarak gizli) -->
            <div class="duzenle-form-alani d-none" id="duzenle-form-{{ yorum.id }}">
                <textarea class="form-control mb-2" id="duzenle-textarea-{{ yorum.id }}" rows="3">{{ yorum.icerik }}</textarea>
                <div class="d-flex gap-2">
                    <button class="button duzenle-kaydet-btn" data-yorum-id="{{ yorum.id }}">
                        <i class="bi bi-check-circle"></i> Kaydet
                    </button>
                    <button class="button button--outline duzenle-iptal-btn" data-yorum-id="{{ yorum.id }}">
                        <i class="bi bi-x-circle"></i> İptal
                    </button>
                </div>
            </div>
            
            <div class="yorum-aksiyonlar mt-3">
                <!-- kullanici değişkeni yoksa, request.user kontrolü yap -->
                {% with active_user=kullanici|default:request.user %}
                {% if active_user.is_authenticated %}
                    <button class="button button--small yanit-btn" data-parent-id="{{ yorum.id }}">
                        <i class="bi bi-reply"></i> Yanıtla
                    </button>
                    
                    {% if active_user == yorum.user or perms.yorumlar.change_yorum %}
                        <button class="button button--small button--outline duzenle-toggle-btn" data-yorum-id="{{ yorum.id }}">
                            <i class="bi bi-pencil"></i> Düzenle
                        </button>
                    {% endif %}
                    
                    {% if active_user == yorum.user or perms.yorumlar.delete_yorum %}
                        <button class="button button--small button--danger sil-dogrudan-btn" data-yorum-id="{{ yorum.id }}">
                            <i class="bi bi-trash"></i> Sil
                        </button>
                    {% endif %}
                    
                    {% if perms.yorumlar.change_yorum and not yorum.onaylandi %}
                        <button class="button button--small onayla-btn" data-yorum-id="{{ yorum.id }}">
                            <i class="bi bi-check-circle"></i> Onayla
                        </button>
                    {% endif %}
                {% endif %}
                {% endwith %}
            </div>
        </div>
    </div>
    
    <!-- Alt yorumlar (yanıtlar) -->
    {% if yorum.cevaplar.exists %}
        <div class="cevaplar-listesi ms-5 mt-2">
            {% for cevap in yorum.cevaplar.all %}
                {% include 'yorumlar/yorum_liste_ogesi.html' with yorum=cevap kullanici=kullanici %}
            {% endfor %}
        </div>
    {% endif %}
</div>
