{% load static %}
{% load yorum_tags %}

<div class="yorumlar-container mt-4">
    <div class="modern-card-header d-flex justify-content-between align-items-center">
        <h3><i class="bi bi-chat-square-text me-2"></i>Yorumlar <span class="modern-badge">{% yorum_sayisi nesne %}</span></h3>
        <button class="button" id="yorumEkleBtn">
            <i class="bi bi-plus-circle me-1"></i>Yorum Ekle
        </button>
    </div>
    
    <!-- Yorum Formu (başlangıçta gizli) -->
    <div id="yorumFormuContainer" class="mb-4" style="display: none;">
        <div class="modern-card">
            <div class="modern-card-header">
                <h3><i class="bi bi-pencil-square me-2"></i>Yorum Yazın</h3>
            </div>
            <div class="modern-card-body">
                <form id="yorumFormu" method="post" action="{% url 'yorumlar:yorum_ekle' %}" class="modern-form">
                    {% csrf_token %}
                    <input type="hidden" name="content_type" value="{{ nesne|content_type_id }}">
                    <input type="hidden" name="object_id" value="{{ nesne.id }}">
                    <input type="hidden" name="parent_id" id="parent_id" value="">
                    <input type="hidden" name="honeypot" value="">
                    
                    <div class="form__group">
                        <label for="id_icerik" class="form__label">Yorumunuz</label>
                        <textarea name="icerik" id="id_icerik" class="form__textarea" rows="3" placeholder="Yorumunuzu buraya yazın..."></textarea>
                        <div class="invalid-feedback icerik-error"></div>
                    </div>
                    
                    {% if not request.user.is_authenticated %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form__group">
                                <label for="id_isim" class="form__label">İsim</label>
                                <input type="text" name="isim" id="id_isim" class="form__input" placeholder="İsminiz">
                                <div class="invalid-feedback isim-error"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form__group">
                                <label for="id_email" class="form__label">E-posta</label>
                                <input type="email" name="email" id="id_email" class="form__input" placeholder="E-posta adresiniz">
                                <div class="invalid-feedback email-error"></div>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <small>Yorumunuz moderasyon onayına gönderilecek. Onaylandıktan sonra görünecektir.</small>
                    </div>
                    {% endif %}
                    
                    <div class="mt-3 d-flex align-items-center">
                        <button type="submit" class="button">
                            <i class="bi bi-paper-plane me-1"></i> Gönder
                        </button>
                        <button type="button" id="yorumIptalBtn" class="button button--outline ms-2">
                            <i class="bi bi-x-circle me-1"></i> İptal
                        </button>
                        <div id="yanit-bilgisi" class="ms-3 d-none">
                            <span class="yanit-kullanici fw-bold"></span> kullanıcısının yorumunu yanıtlıyorsunuz.
                            <button type="button" class="modern-link p-0 ms-2 yanit-iptal">İptal</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Yorumlar Listesi -->
    <div class="yorumlar-listesi">
        <!-- Burada yorumları_goster etiketi kullanılıyor -->
        {% yorumlari_goster nesne 'yorumlar/yorum_liste_ogesi.html' %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Yorum ekleme butonuna tıklama
    document.getElementById('yorumEkleBtn')?.addEventListener('click', function() {
        document.getElementById('yorumFormuContainer').style.display = 'block';
        this.style.display = 'none';
    });
    
    // İptal butonuna tıklama
    document.getElementById('yorumIptalBtn')?.addEventListener('click', function() {
        document.getElementById('yorumFormuContainer').style.display = 'none';
        document.getElementById('yorumEkleBtn').style.display = 'inline-block';
    });
    
    // Yanıtla butonlarına tıklama
    document.querySelectorAll('.yanit-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const parentId = this.getAttribute('data-parent-id');
            const yorumOgesi = document.getElementById('yorum-' + parentId);
            const kullaniciAdi = yorumOgesi.querySelector('.fw-bold').textContent.trim();
            
            document.getElementById('parent_id').value = parentId;
            document.querySelector('.yanit-kullanici').textContent = kullaniciAdi;
            document.getElementById('yanit-bilgisi').classList.remove('d-none');
            
            // Yorum formunu göster
            document.getElementById('yorumFormuContainer').style.display = 'block';
            document.getElementById('yorumEkleBtn').style.display = 'none';
            
            // Textarea'ya odaklan
            document.getElementById('id_icerik').focus();
        });
    });
    
    // Yanıt iptal butonuna tıklama
    document.querySelector('.yanit-iptal')?.addEventListener('click', function() {
        document.getElementById('parent_id').value = '';
        document.getElementById('yanit-bilgisi').classList.add('d-none');
    });
    
    // Yorum formunu gönderme
    document.getElementById('yorumFormu')?.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Form verilerini al
        const formData = new FormData(this);
        
        // AJAX isteği gönder
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Sunucudan gelen yanıt:', data);
            
            if (data.durum === 'başarılı') {
                // Form içeriğini temizle
                document.getElementById('id_icerik').value = '';
                if (!document.querySelector('body').classList.contains('user-is-authenticated')) {
                    document.getElementById('id_isim').value = '';
                    document.getElementById('id_email').value = '';
                }
                
                // Yanıt bilgisini sıfırla
                document.getElementById('parent_id').value = '';
                document.getElementById('yanit-bilgisi').classList.add('d-none');
                
                // Formu gizle, butonu göster
                document.getElementById('yorumFormuContainer').style.display = 'none';
                document.getElementById('yorumEkleBtn').style.display = 'inline-block';
                
                // Debug bilgisi
                console.log('Yorum ekleme başarılı oldu, şimdi DOM güncellemesi yapılacak');
                
                // Kullanıcı oturum açmışsa, yorumu hemen ekle
                if (data.html) {
                    console.log('html verisi mevcut, uzunluk:', data.html.length);
                    
                    // Doğru seçiciyi kullan - sayfa yapısına göre
                    const yorumlarListesi = document.querySelector('.yorumlar-listesi');
                    console.log('Yorumlar listesi bulundu:', !!yorumlarListesi, yorumlarListesi);
                    
                    if (!yorumlarListesi) {
                        console.error('Yorumlar listesi bulunamadı! Mevcut yorumlar container:', document.querySelector('.yorumlar-container'));
                        // Olası alternatif elementleri ara
                        const alternateContainers = document.querySelectorAll('div[class*="yorumlar"]');
                        console.log('Alternatif konteynerler:', alternateContainers.length, alternateContainers);
                        alert('Yorumunuz kaydedildi, ancak sayfayı yenilemeniz gerekebilir.');
                        return;
                    }
                    
                    if (data.parent_id) {
                        // Eğer bir yanıtsa, üst yorumun altına ekle
                        console.log('Bir yanıt yorumu, parent ID:', data.parent_id);
                        const parentYorum = document.getElementById(`yorum-${data.parent_id}`);
                        console.log('Üst yorum bulundu:', !!parentYorum, parentYorum);
                        
                        if (parentYorum) {
                            let cevaplarListesi = parentYorum.querySelector('.cevaplar-listesi');
                            console.log('Cevaplar listesi bulundu:', !!cevaplarListesi);
                            
                            if (!cevaplarListesi) {
                                // Eğer yanıt listesi yoksa oluştur
                                console.log('Yanıt listesi oluşturuluyor...');
                                cevaplarListesi = document.createElement('div');
                                cevaplarListesi.className = 'cevaplar-listesi ms-5 mt-2';
                                parentYorum.appendChild(cevaplarListesi);
                            }
                            
                            // Yanıtı listeye ekle
                            cevaplarListesi.insertAdjacentHTML('beforeend', data.html);
                            console.log('Yanıt eklendi');
                        } else {
                            console.error('Üst yorum bulunamadı, ID:', data.parent_id);
                        }
                    } else {
                        // Eğer üst yorumsa, listenin başına ekle
                        console.log('Bir üst yorum, doğrudan listeye ekleniyor');
                        
                        const beforeHTML = yorumlarListesi.innerHTML;
                        yorumlarListesi.insertAdjacentHTML('afterbegin', data.html);
                        console.log('Yorum başa eklendi, önceki içerik boyutu:', beforeHTML.length, 'yeni içerik boyutu:', yorumlarListesi.innerHTML.length);
                        
                        // Hata ayıklama için yorum elementini kontrol et
                        setTimeout(() => {
                            const yeniYorum = document.getElementById(`yorum-${data.yorum_id}`);
                            console.log('Yeni yorum DOM kontrolü:', !!yeniYorum, data.yorum_id, yeniYorum);
                            
                            if (!yeniYorum) {
                                // Eğer yeni yorum bulunamadıysa, alternatif bir yaklaşım deneyelim
                                console.warn('DOM\'da yeni yorum bulunamadı, sayfa yenilenecek...');
                                alert('Yorumunuz kaydedildi, ancak görüntüleme için sayfa yenileniyor.');
                                window.location.reload();
                            }
                        }, 100);
                    }
                } else {
                    // Oturum açılmamışsa, bildirim göster
                    console.log('HTML içeriği yok, muhtemelen kullanıcı oturum açmamış');
                    alert('Yorumunuz moderasyon onayına gönderildi. Onaylandıktan sonra görünecektir.');
                }
            } else {
                // Hataları göster
                if (data.hatalar) {
                    const hatalar = JSON.parse(data.hatalar);
                    
                    // İçerik hatası
                    if (hatalar.icerik) {
                        document.getElementById('id_icerik').classList.add('is-invalid');
                        document.querySelector('.icerik-error').textContent = hatalar.icerik[0];
                    }
                    
                    // İsim hatası
                    if (hatalar.isim) {
                        document.getElementById('id_isim').classList.add('is-invalid');
                        document.querySelector('.isim-error').textContent = hatalar.isim[0];
                    }
                    
                    // Email hatası
                    if (hatalar.email) {
                        document.getElementById('id_email').classList.add('is-invalid');
                        document.querySelector('.email-error').textContent = hatalar.email[0];
                    }
                }
            }
        })
        .catch(error => {
            console.error('Hata:', error);
            alert('Yorum gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
        });
    });
});
</script>
