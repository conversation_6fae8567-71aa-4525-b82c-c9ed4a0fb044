from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

class Yorum(models.Model):
    # <PERSON><PERSON><PERSON><PERSON> türü ve nesne il<PERSON>ş<PERSON>lendirme (Generic Foreign Key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # Yorum sahibi (Anonim yorumlar için null=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, verbose_name="Kullanıcı")
    isim = models.CharField(max_length=100, verbose_name="İsim", blank=True)
    email = models.EmailField(blank=True, verbose_name="E-posta")
    
    # Yorum içeriği
    icerik = models.TextField(verbose_name="Yorum")
    tarih = models.DateTimeField(default=timezone.now, verbose_name="Tarih")
    onaylandi = models.BooleanField(default=False, verbose_name="Onaylandı")
    ip_adresi = models.GenericIPAddressField(blank=True, null=True, verbose_name="IP Adresi")
    
    # Meta veriler
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='cevaplar', verbose_name="Üst Yorum")
    
    class Meta:
        verbose_name = "Yorum"
        verbose_name_plural = "Yorumlar"
        ordering = ['-tarih']
    
    def __str__(self):
        if self.user:
            return f"{self.user.username} tarafından yapılan yorum"
        elif self.isim:
            return f"{self.isim} tarafından yapılan yorum"
        else:
            return "Anonim yorum"
