# Küp Cadısı Projesi - Makefile

.PHONY: help install test lint format clean migrate runserver shell docker-build docker-run docker-stop docker-logs docker-shell

# Varsay<PERSON>lan hedef
help:
	@echo "Küp Cadısı Projesi - Kullanılabilir Komutlar:"
	@echo ""
	@echo "  install     - Bağımlılıkları yükle"
	@echo "  test        - Testleri çalıştır"
	@echo "  test-cov    - Test coverage raporu"
	@echo "  lint        - Kod kalitesi kontrolü"
	@echo "  format      - Kod formatını düzelt"
	@echo "  migrate     - Veritabanı migrasyonları"
	@echo "  runserver   - Geliştirme sunucusunu başlat"
	@echo "  shell       - Django shell"
	@echo "  clean       - Geçici dosyaları temizle"
	@echo "  security    - Güvenlik kontrolü"
	@echo "  setup-dev   - Geliştirme ortamını kur"
	@echo ""
	@echo "Docker Komutları:"
	@echo "  docker-build - Docker image'ını oluştur"
	@echo "  docker-run   - Docker container'ını çalıştır"
	@echo "  docker-stop  - Docker container'ını durdur"
	@echo "  docker-logs  - Container loglarını göster"
	@echo "  docker-shell - Container içinde shell aç"

# Bağımlılıkları yükle
install:
	pip install -r requirements.txt

# Geliştirme ortamını kur
setup-dev:
	python -m venv venv
	venv\Scripts\activate && pip install -r requirements.txt
	python manage.py migrate
	@echo "Geliştirme ortamı hazır!"
	@echo "Kullanım: venv\Scripts\activate"

# Testleri çalıştır
test:
	python manage.py test

# Test coverage
test-cov:
	coverage run --source='.' manage.py test
	coverage report
	coverage html

# Kod kalitesi kontrolü
lint:
	flake8 .
	isort --check-only .
	black --check .

# Kod formatını düzelt
format:
	isort .
	black .

# Veritabanı migrasyonları
migrate:
	python manage.py makemigrations
	python manage.py migrate

# Geliştirme sunucusu
runserver:
	python manage.py runserver

# Django shell
shell:
	python manage.py shell

# Güvenlik kontrolü
security:
	python manage.py check --deploy
	bandit -r . -x venv,tests

# Geçici dosyaları temizle
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.log" -delete
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/

# Statik dosyaları topla
collectstatic:
	python manage.py collectstatic --noinput

# Süper kullanıcı oluştur
createsuperuser:
	python manage.py createsuperuser

# Veritabanını sıfırla (DİKKAT!)
reset-db:
	@echo "UYARI: Bu işlem tüm veritabanını silecek!"
	@echo "Devam etmek için 'yes' yazın:"
	@read confirm && [ "$$confirm" = "yes" ] || exit 1
	rm -f db.sqlite3
	python manage.py migrate

# Docker Komutları
docker-build:
	docker build -t kup-cadisi-web .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f

docker-shell:
	docker-compose exec web bash

docker-restart:
	docker-compose restart

# Docker ile development
docker-dev:
	docker-compose up
